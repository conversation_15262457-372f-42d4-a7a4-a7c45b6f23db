<?php

namespace App\Http\Controllers;

use App\Models\Charge;
use App\Models\Refund;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Stripe\Stripe;
use Stripe\Refund as StripeRefund;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class ChargeController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('viewAny', Charge::class);

        $query = Charge::with(['user', 'refunds']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('stripe_charge_id', 'like', "%{$search}%")
                    ->orWhere('stripe_payment_intent_id', 'like', "%{$search}%")
                    ->orWhere('receipt_number', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('firstName', 'like', "%{$search}%")
                            ->orWhere('lastName', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('charge_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('charge_date', '<=', $request->date_to);
        }

        $charges = $query->orderBy('charge_date', 'desc')->paginate(15);

        if ($request->ajax()) {
            return response()->json([
                'charges' => view('admin.charges.partialChargesList', compact('charges'))->render(),
                'pagination' => $charges->onEachSide(1)->links('pagination::bootstrap-5')->render(),
            ]);
        }

        return view('admin.charges.index', compact('charges'));
    }

    public function show(Charge $charge)
    {
        $this->authorize('view', $charge);

        $charge->load(['user', 'refunds']);

        return view('admin.charges.show', compact('charge'));
    }

    public function processRefund(Request $request, Charge $charge)
    {
        $this->authorize('refund', $charge);

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $charge->refundable_amount,
            'reason' => 'required|string|max:255',
        ]);

        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $amountInCents = $request->amount * 100;

            // Create refund in Stripe
            $stripeRefund = StripeRefund::create([
                'charge' => $charge->stripe_charge_id,
                'amount' => $amountInCents,
                'reason' => $request->reason,
                'metadata' => [
                    'user_id' => auth()->id(),
                    'charge_id' => $charge->id,
                    'refund_reason' => $request->reason,
                ],
            ]);

            // Create refund record in database
            $refund = Refund::create([
                'user_id' => auth()->id(),
                'refundable_type' => Charge::class,
                'refundable_id' => $charge->id,
                'stripe_refund_id' => $stripeRefund->id,
                'amount' => $request->amount,
                'currency' => $stripeRefund->currency,
                'reason' => $request->reason,
                'status' => $stripeRefund->status,
                'metadata' => $stripeRefund->metadata->toArray(),
                'refunded_at' => now(),
            ]);

            // Update charge refund information
            $charge->refunded_amount += $request->amount;
            $charge->refund_status = $charge->refunded_amount >= $charge->amount ? 'full' : 'partial';
            $charge->save();

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully',
                'refund' => $refund,
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Stripe refund error', [
                'charge_id' => $charge->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process refund: ' . $e->getMessage(),
            ], 500);
        } catch (\Exception $e) {
            Log::error('Refund processing error', [
                'charge_id' => $charge->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the refund',
            ], 500);
        }
    }

    public function downloadReceipt(Charge $charge)
    {
        $this->authorize('view', $charge);

        if (!$charge->receipt_url) {
            return back()->with('error', 'Receipt not available for this charge');
        }

        return redirect($charge->receipt_url);
    }

    public function createChargeFromStripe($stripeChargeId)
    {
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $stripeCharge = \Stripe\Charge::retrieve($stripeChargeId);

            // Check if charge already exists
            $existingCharge = Charge::where('stripe_charge_id', $stripeChargeId)->first();
            if ($existingCharge) {
                return $existingCharge;
            }

            // Extract metadata from charge, payment intent, or subscription
            $metadata = $stripeCharge->metadata->toArray();
            if (empty($metadata) && $stripeCharge->payment_intent) {
                try {
                    $paymentIntent = \Stripe\PaymentIntent::retrieve($stripeCharge->payment_intent);
                    $metadata = $paymentIntent->metadata->toArray();
                } catch (\Exception $e) {
                    Log::error('Failed to retrieve payment intent metadata', ['error' => $e->getMessage()]);
                }
            }

            // If still no metadata and this is from a subscription, try to get metadata from subscription
            if (empty($metadata) && $stripeCharge->invoice) {
                try {
                    $invoice = \Stripe\Invoice::retrieve($stripeCharge->invoice);
                    if ($invoice->subscription) {
                        $subscription = \Stripe\Subscription::retrieve($invoice->subscription);
                        $metadata = $subscription->metadata->toArray();
                        Log::info('Retrieved metadata from subscription', ['metadata' => $metadata]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to retrieve subscription metadata', ['error' => $e->getMessage()]);
                }
            }

            // Determine user ID from metadata or payment intent
            $userId = $metadata['user_id'] ?? null;
            if (!$userId && $stripeCharge->payment_intent) {
                try {
                    $paymentIntent = \Stripe\PaymentIntent::retrieve($stripeCharge->payment_intent);
                    $userId = $paymentIntent->metadata['user_id'] ?? null;
                } catch (\Exception $e) {
                    Log::error('Failed to retrieve user ID from payment intent', ['error' => $e->getMessage()]);
                }
            }

            if (!$userId) {
                Log::error('No user ID found for charge', ['stripe_charge_id' => $stripeChargeId]);
                return null;
            }

            // Handle program_ids and player_ids properly
            $programIds = null;
            if (isset($metadata['program_id'])) {
                $programIds = [$metadata['program_id']];
            } elseif (isset($metadata['program_ids'])) {
                $programIds = is_array($metadata['program_ids']) ? $metadata['program_ids'] : json_decode($metadata['program_ids'], true);
            }

            $playerIds = null;
            if (isset($metadata['player_ids'])) {
                $playerIds = is_array($metadata['player_ids']) ? $metadata['player_ids'] : json_decode($metadata['player_ids'], true);
            }

            // Create charge record
            $charge = Charge::create([
                'user_id' => $userId,
                'stripe_charge_id' => $stripeCharge->id,
                'stripe_payment_intent_id' => $stripeCharge->payment_intent,
                'amount' => $stripeCharge->amount / 100,
                'currency' => $stripeCharge->currency,
                'status' => $stripeCharge->status,
                'payment_type' => $this->normalizePaymentType($metadata['payment_type'] ?? null),
                'program_ids' => $programIds,
                'player_ids' => $playerIds,
                'metadata' => $metadata,
                'charge_date' => \Carbon\Carbon::createFromTimestamp($stripeCharge->created),
                'paid_at' => $stripeCharge->status === 'succeeded' ? \Carbon\Carbon::createFromTimestamp($stripeCharge->created) : null,
                'description' => $stripeCharge->description,
                'receipt_url' => $stripeCharge->receipt_url,
                'receipt_number' => $stripeCharge->receipt_number,
            ]);

            Log::info('Charge created from Stripe', [
                'charge_id' => $charge->id,
                'stripe_charge_id' => $stripeChargeId,
                'user_id' => $userId,
                'program_ids' => $programIds,
                'player_ids' => $playerIds,
            ]);

            return $charge;
        } catch (\Exception $e) {
            Log::error('Error creating charge from Stripe', [
                'stripe_charge_id' => $stripeChargeId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Normalize payment type to match database enum values
     */
    private function normalizePaymentType($paymentType)
    {
        if (!$paymentType) {
            return null;
        }

        $mapping = [
            'recurringPayments' => 'recurring',
            'fullAmount' => 'full',
            'specificAmount' => 'split',
            'splitPayments' => 'split',
            'Outstanding to recurring' => 'outstanding_to_recurring',
        ];

        return $mapping[$paymentType] ?? $paymentType;
    }
}
