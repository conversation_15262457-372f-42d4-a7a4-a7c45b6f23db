@extends('layouts.app')

@section('title', 'Charge Receipt')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="invoice-card shadow border-0 rounded-4 overflow-hidden mb-4 position-relative">
                <!-- Status Badge -->
                <div class="position-absolute top-0 end-0 m-4">
                    @if($charge->status === 'succeeded' && $charge->refunded_amount < $charge->amount)
                        <span class="badge status-badge"><i class="bi bi-check-circle-fill me-1"></i>PAID</span>
                    @elseif($charge->refunded_amount >= $charge->amount)
                        <span class="badge status-badge bg-danger"><i class="bi bi-arrow-counterclockwise me-1"></i>REFUNDED</span>
                    @else
                        <span class="badge status-badge bg-warning text-dark"><i class="bi bi-exclamation-circle me-1"></i>PENDING</span>
                    @endif
                </div>
                <!-- Header -->
                <div class="invoice-header px-4 py-4 d-flex align-items-center justify-content-between" style="background: #0b4499; color: #fff;">
                    <div class="d-flex align-items-center">
                        <img src="https://dummyimage.com/48x48/0b4499/fff&text=MP" alt="Logo" style="width:48px;height:48px;border-radius:8px;object-fit:cover;">
                        <div class="ms-3">
                            <h4 class="mb-0 fw-bold" style="letter-spacing:1px;">MASS PREMIER COURTS</h4>
                            <div class="small">Sports Training & Programs</div>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold fs-5" style="letter-spacing:1px;">INVOICE</div>
                        <div class="small">Invoice #: <span class="fw-semibold" style="color:#ffd600;">{{ $charge->stripe_charge_id }}</span></div>
                        <div class="small">Date: {{ $charge->charge_date ? $charge->charge_date->format('M d, Y') : 'N/A' }}</div>
                    </div>
                </div>
                <!-- Info Row -->
                <div class="px-4 py-3 bg-white border-bottom">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-6">
                            <div class="fw-bold text-uppercase small mb-1" style="color:#0b4499;">Billed To</div>
                            <div class="fw-semibold" style="color:#0b4499;">
                                <i class="bi bi-person-circle me-1"></i>
                                {{ $charge->user->firstName }} {{ $charge->user->lastName }}
                            </div>
                            <div class="small text-muted"><i class="bi bi-envelope me-1"></i>{{ $charge->user->email }}</div>
                            @if($charge->user->mobile_number)
                                <div class="small text-muted"><i class="bi bi-telephone me-1"></i>{{ $charge->user->mobile_number }}</div>
                            @endif
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="fw-bold text-uppercase small mb-1" style="color:#0b4499;">Payment Method</div>
                            <div class="small"><i class="bi bi-credit-card-2-front me-1"></i>Method: <span class="fw-semibold" style="color:#0b4499;">{{ ucfirst($charge->payment_type ?? 'Credit Card') }}</span></div>
                            <div class="small">Status:
                                <span class="badge" style="background:#0b4499;">{{ ucfirst($charge->status) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Program & Player Section -->
                <div class="px-4 py-3 bg-white border-bottom">
                    <div class="row">
                        <div class="col-12">
                            <div class="fw-bold text-uppercase small mb-1" style="color:#0b4499;">Program & Player</div>
                            <div>
                                @foreach($charge->programs as $program)
                                    <span class="fw-bold" style="color:#0b4499;"><i class="bi bi-award me-1"></i>{{ $program->name }}</span>
                                @endforeach
                                @foreach($charge->players as $player)
                                    <span class="fw-bold ms-3" style="color:#0b4499;"><i class="bi bi-person me-1"></i>{{ $player->firstName }} {{ $player->lastName }}</span>
                                @endforeach
                            </div>
                            @if($charge->description)
                                <div class="small text-muted mt-1">{{ $charge->description }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- Item Table -->
                <div class="px-4 py-3 bg-white">
                    <div class="table-responsive">
                        <table class="table table-sm align-middle mb-0 invoice-table">
                            <thead style="background:#f5f7fa;">
                                <tr>
                                    <th class="text-uppercase small" style="color:#0b4499;">Description</th>
                                    <th class="text-uppercase small text-end" style="color:#0b4499;">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        @if($charge->description)
                                            {{ $charge->description }}
                                        @else
                                            Sports program payment
                                        @endif
                                    </td>
                                    <td class="text-end" style="color:#0b4499;">{{ $charge->formatted_amount }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Totals -->
                <div class="px-4 py-3 bg-white border-top">
                    <div class="row">
                        <div class="col-6 text-end text-muted small">Subtotal:</div>
                        <div class="col-6 text-end" style="color:#0b4499;">{{ $charge->formatted_amount }}</div>
                    </div>
                    @if($charge->refunded_amount > 0)
                    <div class="row">
                        <div class="col-6 text-end text-danger small">Refunded:</div>
                        <div class="col-6 text-end text-danger">-{{ $charge->formatted_refunded_amount }}</div>
                    </div>
                    @endif
                    <div class="row mt-2">
                        <div class="col-6 text-end fw-bold" style="color:#0b4499;">Total Paid:</div>
                        <div class="col-6 text-end fw-bold fs-5" style="color:#0b4499;">{{ $charge->formatted_amount }}</div>
                    </div>
                    <div class="row">
                        <div class="col-6 text-end fw-bold" style="color:#0b4499;">Total Remaining:</div>
                        <div class="col-6 text-end fw-bold fs-5" style="color:#0b4499;">{{ $charge->formatted_remaining_amount }}</div>
                    </div>
                </div>
                <!-- Inline Refund Section -->
                @if($charge->canBeRefunded())
                <div class="px-4 py-3 bg-white border-top">
                    <form id="refundForm" class="row g-3 align-items-end">
                        @csrf
                        <div class="col-md-4">
                            <label class="form-label small fw-medium" style="color:#0b4499;">Paid Amount</label>
                            <input type="text" class="form-control-plaintext fw-bold" readonly value="{{ $charge->formatted_amount }}" style="color:#0b4499;">
                        </div>
                        <div class="col-md-4">
                            <label for="refundAmount" class="form-label small fw-medium" style="color:#0b4499;">Refund Amount</label>
                            <input type="number"
                                   class="form-control"
                                   id="refundAmount"
                                   name="amount"
                                   step="0.01"
                                   max="{{ $charge->refundable_amount }}"
                                   value="{{ $charge->refundable_amount }}"
                                   required>
                            <div class="form-text small">
                                Maximum: <span class="fw-semibold">{{ $charge->formatted_refundable_amount }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="refundReason" class="form-label small fw-medium" style="color:#0b4499;">Reason</label>
                            <select class="form-select" id="refundReason" name="reason" required>
                                <option value="">Select a reason</option>
                                <option value="duplicate">Duplicate charge</option>
                                <option value="fraudulent">Fraudulent charge</option>
                                <option value="requested_by_customer">Requested by customer</option>
                                <option value="expired_uncaptured_charge">Expired uncaptured charge</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <button type="submit" class="btn invoice-btn w-100" style="margin-top:2rem;"><i class="bi bi-arrow-clockwise me-1"></i></button>
                        </div>
                    </form>
                </div>
                @elseif($charge->refunded_amount >= $charge->amount)
                <div class="px-4 py-3 bg-white border-top text-center">
                    <span class="badge bg-danger fs-6"><i class="bi bi-arrow-counterclockwise me-1"></i>Fully Refunded</span>
                </div>
                @endif
                <!-- Terms & Footer -->
                <div class="px-4 py-3 bg-white border-top">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="small text-muted mb-2">Terms and Conditions</div>
                            <div class="small text-muted">All payments are final unless otherwise stated. For questions, <a href="mailto:<EMAIL>" style="color:#0b4499;">contact support</a>.</div>
                        </div>
                        <div class="col-md-4 text-end">
                            @if($charge->receipt_url)
                                <a href="{{ $charge->receipt_url }}" target="_blank" class="btn btn-sm invoice-btn mb-2 w-100"><i class="bi bi-download me-1"></i>Download Receipt</a>
                            @endif
                        </div>
                    </div>
                    <div class="text-center pt-3">
                        <span class="fw-bold" style="color:#0b4499;">Thank you for choosing Mass Premier Courts!</span>
                    </div>
                </div>
            </div>
            <!-- Refund History -->
            @if($charge->refunds->count() > 0)
            <div class="card border-0 shadow-sm rounded-4 mb-4">
                <div class="card-header bg-white py-3 border-bottom-0">
                    <div class="fw-bold text-uppercase small" style="color:#0b4499;"><i class="bi bi-clock-history me-2"></i>Refund History</div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead style="background:#f5f7fa;">
                                <tr>
                                    <th class="small text-uppercase" style="color:#0b4499;">Refund ID</th>
                                    <th class="small text-uppercase text-end" style="color:#0b4499;">Amount</th>
                                    <th class="small text-uppercase" style="color:#0b4499;">Reason</th>
                                    <th class="small text-uppercase" style="color:#0b4499;">Status</th>
                                    <th class="small text-uppercase" style="color:#0b4499;">Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($charge->refunds as $refund)
                                <tr>
                                    <td><code class="small">{{ $refund->stripe_refund_id }}</code></td>
                                    <td class="text-end text-danger small">{{ $refund->formatted_amount }}</td>
                                    <td class="small text-capitalize">{{ str_replace('_', ' ', $refund->reason) }}</td>
                                    <td><span class="badge" style="background:#0b4499;">{{ ucfirst($refund->status) }}</span></td>
                                    <td class="small text-muted">{{ $refund->formatted_refunded_at }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<style>
.invoice-header {
    background: #0b4499;
    color: #fff;
}
.invoice-btn {
    background: #0b4499;
    color: #fff;
    border: none;
    transition: background 0.2s;
}
.invoice-btn:hover, .invoice-btn:focus {
    background: #08306b;
    color: #fff;
}
.status-badge {
    background: #0b4499;
    color: #fff;
    font-size: 1rem;
    padding: 0.5em 1em;
    border-radius: 2em;
    box-shadow: 0 2px 8px rgba(11,68,153,0.08);
}
.invoice-table th, .invoice-table td {
    font-size: 0.95rem;
}
.badge {
    background: #0b4499;
    color: #fff;
    font-size: 0.85em;
    border-radius: 4px;
}
@media print {
    .btn, .modal, .invoice-btn { display: none !important; }
    .invoice-card, .card { box-shadow: none !important; border: 1px solid #dee2e6 !important; }
}
</style>
<script>
document.getElementById('refundForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    if (!confirm('Are you sure you want to process this refund? This action cannot be undone.')) {
        return;
    }
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Processing...';
    fetch('{{ route("admin.charges.refund", $charge) }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: formData.get('amount'),
            reason: formData.get('reason'),
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; font-size: 0.875rem;';
            successAlert.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                <strong>Success!</strong> Refund processed successfully.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(successAlert);
            setTimeout(() => { location.reload(); }, 2000);
        } else {
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            errorAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; font-size: 0.875rem;';
            errorAlert.innerHTML = `
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>Error!</strong> ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(errorAlert);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        errorAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; font-size: 0.875rem;';
        errorAlert.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Error!</strong> An error occurred while processing the refund.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(errorAlert);
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
</script>
@endsection
