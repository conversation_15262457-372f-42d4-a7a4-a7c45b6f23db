<?php
    namespace App\Exports;
    use Maatwebsite\Excel\Concerns\FromArray;
    use Maatwebsite\Excel\Concerns\WithHeadings;

    class ProgramDataExport implements FromArray, WithHeadings
    {
        protected $data;

        public function __construct(array $data)
        {
            $this->data = $data;
        }

        public function array(): array
        {
            return $this->data;
        }

        public function headings(): array
        {
            return [
                'Program Slug',
                'Player/Team Name',
                'Email',
                'Guardian/Coach Email',
                'Programs Dates',
            ];
        }
    }
