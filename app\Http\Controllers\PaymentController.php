<?php

namespace App\Http\Controllers;

use App\Models\GuardianCredit;
use App\Models\TeamPlayer;
use App\Models\User;
use App\Models\GuardianPayment;
use App\Models\PlayerInvitation;
use App\Models\ProgramRegistration;
use App\Models\PlayerProgram;
use App\Models\Program;
use App\Models\ProgramPaymentTransaction;
use App\Models\Subscription as LocalSubscription;
use Illuminate\Http\Request;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use Stripe\Customer;
use Stripe\Subscription;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use DateTimeZone;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class PaymentController extends Controller
{
    public function index(Request $request)
    {

        return view('payment.StripePayment');
    }


    public function updatePaymentStatus(Request $request)
    {
        $user = auth()->user();

        // Validate the request input
        $validated = $request->validate([
            'paymentIntentId' => 'required|string',
            'amount' => 'required|numeric',
            'playerIds' => 'required|array',
            'playerIds.*' => 'integer',
            'programId' => 'required|integer',
            'totalAmountToBePaid' => 'required|numeric',
        ]);

        $playerIds = $validated['playerIds'];
        $programId = $validated['programId'];
        $amount = $validated['amount'];
        $totalAmountToBePaid = $validated['totalAmountToBePaid'];
        $paymentId = $validated['paymentIntentId'];

        $updateCount = 0;

        $pendingAmount = $totalAmountToBePaid - $amount;


        foreach ($playerIds as $playerId) {

            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


            PlayerProgram::create([
                'player_id' => $playerId,
                'program_id' => $programId,
            ]);


            $existingRegistration = ProgramRegistration::where('user_id', $user->id)
                ->where('player_id', $playerId)
                ->where('program_id', $programId)
                ->first();

            if ($existingRegistration) {

                $existingRegistration->update([
                    'is_paid' => true,
                ]);
            } else {

                ProgramRegistration::create([
                    'user_id' => $user->id,
                    'program_id' => $programId,
                    'amount' => $amountForPerPlayerByCost,
                    'player_id' => $playerId,
                    'is_paid' => true,
                ]);
            }

            $updateCount++;
        }

        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $paymentId,
            'paid_amount' => $amount,
            'pending_amount' => $pendingAmount
        ]);


        if ($updateCount > 0) {
            return response()->json(['message' => 'Payment status updated successfully.']);
        } else {
            return response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
        }
    }


    public function updatePaymentStatusForPaymentOptions(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            // Validate the request
            $validated = $request->validate([
                'paymentIntentId' => 'required|string',
                'paymentId' => 'required|string',
                'amount' => 'required|numeric|min:0',
                'totalAmountToBePaid' => 'required|numeric|min:0',
            ]);

            $user = auth()->user();

            $guardianPayments = GuardianPayment::where('user_id', $user->id)
                ->where('payment_type', 'split')
                ->where('pending_amount', '>', 0)
                ->get();

            $totalBalanceDue = $guardianPayments->sum('pending_amount');

            $amount = $validated['amount'];
            if ($amount > $totalBalanceDue) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment exceeds the total balance due.',
                ], 400);
            }

            $remainingAmount = $amount;
            $affectedPayments = []; // To store program IDs, player IDs, and amounts deducted

            DB::beginTransaction();

            foreach ($guardianPayments as $payment) {
                if ($remainingAmount <= 0) {
                    break;
                }

                $programId = $payment->program_id;
                $playerIds = json_decode($payment->player_ids, true);
                $currentPending = $payment->pending_amount;

                $amountToSubtract = min($currentPending, $remainingAmount);

                $payment->pending_amount -= $amountToSubtract;
                $payment->save();

                Log::info('Payment update', [
                    'program_id' => $programId,
                    'player_ids' => $playerIds,
                    'amount_subtracted' => $amountToSubtract,
                    'remaining_pending_amount' => $payment->pending_amount,
                ]);

                // Store program ID, player ID, and amount deducted
                $affectedPayments[] = [
                    'program_id' => $programId,
                    'player_ids' => $playerIds,
                    'amount' => $amountToSubtract,
                ];

                $remainingAmount -= $amountToSubtract;
            }

            // Update the PaymentIntent with the affected programs, players, and deducted amounts
            $paymentIntent =  PaymentIntent::update($validated['paymentId'], [
                'metadata' => [
                    'affected_payments' => json_encode($affectedPayments),
                    'updated_amount' => $amount,
                ],
            ]);

            $paymentIntent->save();




            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment update failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment status.',
            ], 500);
        }
    }





    //  public function processPayment(Request $request)

    // {
    //     Stripe::setApiKey(env('STRIPE_SECRET'));

    //     try {
    //         $amount = $request->input('amount');
    //         $email = $request->input('email');
    //         $guardians = $request->input('guardians', []);
    //         $recurring = $request->input('recurring', false);
    //         $startDate = $request->input('startDate');
    //         $numberOfPayments = $request->input('numberOfPayments');

    //         // Validate amount or recurring settings
    //         if (!$amount && !$recurring) {
    //             return response()->json(['error' => 'Invalid payment data.'], 400);
    //         }

    //         $metadata = [];


    //         if ($email) {
    //             $metadata['invoice_email'] = $email;
    //         }

    //         if (!empty($guardians)) {
    //             $metadata['guardians'] = json_encode($guardians);
    //         }

    //         if ($recurring) {
    //             $metadata['recurring'] = true;
    //             $metadata['start_date'] = $startDate;
    //             $metadata['number_of_payments'] = $numberOfPayments;
    //         }


    //         $paymentIntent = PaymentIntent::create([
    //             'amount' => $amount * 100,
    //             'currency' => 'usd',
    //             'metadata' => $metadata,
    //         ]);

    //         return response()->json([
    //             'clientSecret' => $paymentIntent->client_secret,
    //         ]);

    //     } catch (ApiErrorException $e) {

    //         return response()->json([
    //             'error' => 'Stripe Error: ' . $e->getMessage(),
    //         ], 500);
    //     } catch (\Exception $e) {

    //         return response()->json([
    //             'error' => 'Payment failed: ' . $e->getMessage(),
    //         ], 500);
    //     }
    // }



    public function fullPayment(Request $request)
    {
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));


            $user = auth()->user();

            $validated = $request->validate([
                'payment_type' => 'required|string',
                'amount' => 'required|numeric',
                'paymentMethodId' => 'required|string',
                'paidAmount' => 'required|numeric',
                'player_ids' => 'required|array',
                'program_id' => 'required|integer',
                'createdAt' => 'required|date',
                'cardHolderName' => 'required|string',
                'state' => 'required|string',
                'address' => 'required|string',
                'team_id' => 'nullable',
            ]);


            $paymentIntent = PaymentIntent::create([
                'amount' => $validated['paidAmount'] * 100,
                'currency' => 'usd',
                'payment_method' => $validated['paymentMethodId'],
                'confirm' => true,
                'description' => 'Program payment for user ID: ' . $user->id,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',
                ],
                'metadata' => [
                    'cardHolderName' => $validated['cardHolderName'],
                    'address' => $validated['address'],
                    'state' => $validated['state'],
                    'user_id' => $user->id,
                    'program_id' => $validated['program_id'],
                    'player_ids' => json_encode($validated['player_ids']),
                    'payment_type' => $validated['payment_type'],
                    'paid_amount' => $validated['paidAmount'],
                    'town' => $user->town ?? null,
                    'email' => $user->email,
                    'payment_id' => $validated['paymentMethodId']

                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',
                ],
            ]);

            if ($paymentIntent->status === 'requires_action') {
                return response()->json([
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret,
                ]);
            }

            // Remove immediate charge creation - let webhook handle it
            // if ($paymentIntent->status === 'succeeded') {
            //     $this->createChargeFromPaymentIntent($paymentIntent, $user, $validated);
            // }

            return $this->processPlayerRegistrationsAndPayments($user, $validated);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Payment processing error', 'details' => $e->getMessage()], 500);
        }
    }


    protected function processPlayerRegistrationsAndPayments($user, $validated)
    {

        if (!isset($validated['team_id'])) {

            try {
                $playerIds = $validated['player_ids'];
                $programId = $validated['program_id'];
                $updateCount = 0;

                foreach ($playerIds as $playerId) {

                    $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


                    PlayerProgram::updateOrCreate(['player_id' => $playerId, 'program_id' => $programId]);


                    ProgramRegistration::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'player_id' => $playerId,
                            'program_id' => $programId,
                        ],
                        ['is_paid' => true, 'amount' => $amountForPerPlayerByCost]
                    );

                    $updateCount++;
                }



                GuardianPayment::create([
                    'user_id' => $user->id,
                    'payment_id' => $validated['paymentMethodId'],
                    'paid_amount' => $validated['paidAmount'],
                    'pending_amount' => $validated['amount'] - $validated['paidAmount'],
                    'created_at' => $validated['createdAt'],
                    'payment_type' => ($validated['payment_type'] === 'fullAmount') ? 'full' : 'full',
                    'program_id' => $programId,
                    'player_ids' => json_encode($playerIds),
                ]);

                return response()->json(['message' => 'Payment status updated successfully.']);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
            }
        } else {

            try {
                $playerIds = $validated['player_ids'];
                $programId = $validated['program_id'];
                $updateCount = 0;

                foreach ($playerIds as $playerId) {

                    $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

                    PlayerProgram::create([
                        'player_id' => $playerId,
                        'program_id' => $programId,
                    ]);

                    TeamPlayer::create([
                        'team_id' => $validated['team_id'],
                        'player_id' => $playerId,
                    ]);

                    $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

                    $balanceDue = $validated['amount'] - $validated['paidAmount'];

                    // Only update balance_due, status will be updated after successful payment
                    $playerInvitation->update([
                        'balance_due' => $balanceDue
                    ]);

                    $updateCount++;
                }



                GuardianPayment::create([
                    'user_id' => $user->id,
                    'payment_id' => $validated['paymentMethodId'],
                    'paid_amount' => $validated['paidAmount'],
                    'pending_amount' => $validated['amount'] - $validated['paidAmount'],
                    'created_at' => $validated['createdAt'],
                    'payment_type' => ($validated['payment_type'] === 'fullAmount') ? 'full' : 'full',
                    'program_id' => $programId,
                    'player_ids' => json_encode($playerIds),
                ]);

                return response()->json(['message' => 'Payment status updated successfully.']);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
            }
        }
    }





    public function processPaymentForPaymentOptions(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        $user = auth()->user();

        $validated = $request->validate([
            'amount' => 'required',
            'payment_type' => 'required',
        ]);

        if ($validated['payment_type'] == 'split' || $validated['payment_type'] == 'full') {
            $pendingAmountSum = GuardianPayment::where('user_id', $user->id)
                ->where('payment_type', '!=', 'recurring')
                ->where('pending_amount', '>', 0)
                ->sum('pending_amount');

            $programIds = GuardianPayment::where('user_id', $user->id)
                ->where('payment_type', '!=', 'recurring')
                ->where('pending_amount', '>', 0)
                ->pluck('program_id');

            $playerIds = GuardianPayment::where('user_id', $user->id)
                ->where('payment_type', '!=', 'recurring')
                ->where('pending_amount', '>', 0)
                ->pluck('player_ids');
        }


        try {
            // Create the PaymentIntent
            $paymentIntent = PaymentIntent::create([
                'amount' => $validated['amount'] * 100,
                'currency' => 'usd',
                'metadata' => [
                    'user_id' => $user->id,
                    'payment_type' => $validated['payment_type'],
                    'program_ids' => $programIds?->toJson(),
                    'player_ids' => $playerIds?->toJson(),
                    'paid_amount' => $validated['amount'],
                    'town' => $user->town ?? null,
                ],
            ]);

            $paymentIntent->metadata['payment_id'] = $paymentIntent->id;
            $paymentIntent->save();








            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
                'paymentId' => $paymentIntent->id
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'error' => 'Stripe Error: ' . $e->getMessage(),
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment failed: ' . $e->getMessage(),
            ], 500);
        }
    }





    public function success()
    {
        return view('payment.success');
    }

    public function error()
    {
        return view('payment.error');
    }

    /**
     * Process payment using guardian credits intelligently
     * - If credit >= program cost: auto-register and deduct full amount
     * - If credit < program cost: use all available credit and return remaining amount to pay
     */
    // public function processCreditPayment(Request $request)
    // {
    //     try {
    //         $validated = $request->validate([
    //             'player_ids' => 'required|array',
    //             'player_ids.*' => 'integer',
    //             'program_id' => 'required|integer',
    //             'totalAmountToBePaid' => 'required|numeric',
    //             'team_id' => 'nullable'
    //         ]);

    //         $user = auth()->user();
    //         $playerIds = $validated['player_ids'];
    //         $programId = $validated['program_id'];
    //         $totalAmountToBePaid = $validated['totalAmountToBePaid'];

    //         // Get available credit
    //         $availableCredit = $user->getTotalAvailableCredit();

    //         if ($availableCredit <= 0) {
    //             return response()->json([
    //                 'success' => false,
    //                 'message' => 'No available credit to use for payment.'
    //             ], 400);
    //         }

    //         // Check if credit covers full amount
    //         if ($availableCredit >= $totalAmountToBePaid) {
    //             // Credit covers full amount - complete registration
    //             DB::beginTransaction();

    //             try {
    //                 // Use the credit
    //                 $creditUsed = $user->useCredit($totalAmountToBePaid);

    //                 if ($creditUsed < $totalAmountToBePaid) {
    //                     throw new \Exception('Failed to use sufficient credit');
    //                 }

    //                 // Process player registrations
    //                 foreach ($playerIds as $playerId) {
    //                     $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

    //                     PlayerProgram::updateOrCreate([
    //                         'player_id' => $playerId,
    //                         'program_id' => $programId,
    //                     ]);

    //                     ProgramRegistration::updateOrCreate(
    //                         [
    //                             'user_id' => $user->id,
    //                             'player_id' => $playerId,
    //                             'program_id' => $programId,
    //                         ],
    //                         [
    //                             'is_paid' => true,
    //                             'amount' => $amountForPerPlayerByCost
    //                         ]
    //                     );
    //                 }

    //                 // Create guardian payment record
    //                 GuardianPayment::create([
    //                     'user_id' => $user->id,
    //                     'payment_id' => 'CREDIT_' . time() . '_' . $user->id,
    //                     'paid_amount' => $creditUsed,
    //                     'pending_amount' => 0,
    //                     'payment_type' => 'credit',
    //                     'program_id' => $programId,
    //                     'player_ids' => json_encode($playerIds),
    //                 ]);

    //                 // If this is for team invitation, update the invitation status
    //                 if (isset($validated['team_id'])) {
    //                     $invitation = PlayerInvitation::where('team_id', $validated['team_id'])
    //                         ->where('user_id', $playerIds[0])
    //                         ->where('program_id', $programId)
    //                         ->first();

    //                     if ($invitation) {
    //                         $invitation->update([
    //                             'status' => 'accepted',
    //                             'balance_due' => 0
    //                         ]);
    //                     }
    //                 }

    //                 DB::commit();

    //                 return response()->json([
    //                     'success' => true,
    //                     'fully_paid' => true,
    //                     'message' => 'Registration completed successfully using credits.',
    //                     'credit_used' => $creditUsed,
    //                     'remaining_credit' => $user->getTotalAvailableCredit()
    //                 ]);
    //             } catch (\Exception $e) {
    //                 DB::rollBack();
    //                 throw $e;
    //             }
    //         } else {
    //             // Credit doesn't cover full amount - return remaining amount for frontend to handle
    //             $remainingAmount = $totalAmountToBePaid - $availableCredit;

    //             return response()->json([
    //                 'success' => true,
    //                 'fully_paid' => false,
    //                 'message' => 'Insufficient credit. Please pay the remaining amount.',
    //                 'available_credit' => $availableCredit,
    //                 'remaining_amount' => $remainingAmount,
    //                 'total_amount' => $totalAmountToBePaid
    //             ]);
    //         }
    //     } catch (\Exception $e) {
    //         return response()->json([
    //             'success' => false,
    //             'message' => 'An error occurred while processing credit payment: ' . $e->getMessage()
    //         ], 500);
    //     }
    // }









    public function processCreditPayment(Request $request)
    {





        try {
            $validatedData = $request->validate([
                'user_id' => 'required|integer',
                'program_id' => 'required|integer',
                'payment_type' => 'required|string|in:creditPayment',
                'paidAmount' => 'required|numeric|',
                'totalAmountToBePaid' => 'required|numeric|min:0',
                'player_ids' => 'required|array',
                'email' => 'nullable|email|required_if:payment_type,otherGuardian',
                'team_id' => 'nullable',
                'used_credit' => 'required|numeric|min:0',
            ]);




            if ($validatedData['used_credit'] == $validatedData['totalAmountToBePaid']) {



                $user = auth()->user();
                $playerIds = $validatedData['player_ids'];
                $programId = $validatedData['program_id'];
                $fullyPaid = true;

                $userId = (int) $validatedData['user_id'];
                $usedCredit = (float) $validatedData['used_credit'];

                $this->useGuardianCredit($userId, $usedCredit);

                foreach ($playerIds as $playerId) {
                    $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

                    PlayerProgram::updateOrCreate([
                        'player_id' => $playerId,
                        'program_id' => $programId,
                    ]);

                    ProgramRegistration::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'player_id' => $playerId,
                            'program_id' => $programId,
                        ],
                        [
                            'is_paid' => true,
                            'amount' => $amountForPerPlayerByCost
                        ]
                    );
                }

                if (isset($validated['team_id'])) {
                    $invitation = PlayerInvitation::where('team_id', $validatedData['team_id'])
                        ->where('user_id', $playerIds[0])
                        ->where('program_id', $programId)
                        ->first();

                    if ($invitation) {
                        $invitation->update([
                            'status' => 'accepted',
                            'balance_due' => 0
                        ]);
                    }
                }


                $redirect_url = 'guardian.dashboard';
                return response()->json([
                    'success' => true,
                    'fully_paid' => $fullyPaid,
                    'redirect_url' => route($redirect_url),
                ]);
            } elseif ($validatedData['used_credit'] < $validatedData['totalAmountToBePaid']) {
                $program = Program::find($validatedData['program_id']);
                if (!$program) {
                    return response()->json([
                        'error' => 'Program not found',
                    ], 404);
                }

                $validatedData['totalAmountToBePaid'] = $validatedData['totalAmountToBePaid'] - $validatedData['used_credit'];

                session()->put('payment_data', $validatedData);


                if ($request->has('team_id')) {
                    session()->put('team_id', $request->team_id);
                }

                $userId = (int) $validatedData['user_id'];
                $usedCredit = (float) $validatedData['used_credit'];

                $this->useGuardianCredit($userId, $usedCredit);

                $fullyPaid = false;

                return response()->json([
                    'success' => true,
                    'fully_paid' => $fullyPaid,
                    'redirect_url' => route('guardian.cardDetails'),
                ]);
            } else {
                return response()->json([
                    'error' => 'Invalid payment type',
                ], 422);
            }
        } catch (ValidationException $e) {

            return response()->json([
                'error' => 'Validation error',
                'details' => $e->errors(),
            ], 422);
        }
    }

    function useGuardianCredit(int $userId, float $amountToUse): bool
    {
        return DB::transaction(function () use ($userId, $amountToUse) {
            $credits = GuardianCredit::where('user_id', $userId)
                ->where('remaining_amount', '>', 0)
                ->orderBy('created_at') // FIFO order
                ->lockForUpdate()       // prevent race conditions
                ->get();

            $remainingToDeduct = $amountToUse;

            foreach ($credits as $credit) {
                if ($remainingToDeduct <= 0) break;

                $available = $credit->remaining_amount;
                $deduct = min($available, $remainingToDeduct);

                $credit->remaining_amount -= $deduct;
                $credit->save();

                $remainingToDeduct -= $deduct;
            }


            if ($remainingToDeduct > 0) {
                throw new \Exception("Not enough credit");
            }

            return true;
        });
    }






    /**
     * Apply credit after successful card payment
     */
    public function applyCreditAfterPayment($user, $programId, $playerIds, $creditAmount)
    {
        try {
            // Use the credit
            $creditUsed = $user->useCredit($creditAmount);

            if ($creditUsed > 0) {
                // Create guardian payment record for the credit portion
                GuardianPayment::create([
                    'user_id' => $user->id,
                    'payment_id' => 'CREDIT_AFTER_PAYMENT_' . time() . '_' . $user->id,
                    'paid_amount' => $creditUsed,
                    'pending_amount' => 0,
                    'payment_type' => 'credit',
                    'program_id' => $programId,
                    'player_ids' => json_encode($playerIds),
                ]);

                return $creditUsed;
            }

            return 0;
        } catch (\Exception $e) {
            Log::error('Failed to apply credit after payment: ' . $e->getMessage());
            return 0;
        }
    }

    protected function createStripeCustomer($user)
    {
        $customer = Customer::create([
            'name' => "{$user->firstName} {$user->lastName}",
            'email' => $user->email,
            'phone' => $user->mobile_number ?? null,
            'description' => "Customer for {$user->email}",
        ]);
        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer->id;
    }


    // {
    //     $user = auth()->user();
    //     Stripe::setApiKey(env('STRIPE_SECRET'));

    //     try {
    //         $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);

    //         $paymentMethodId = $request->input('paymentMethodId');
    //         $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
    //         $paymentMethod->attach(['customer' => $customerId]);

    //         Customer::update($customerId, [
    //             'invoice_settings' => [
    //                 'default_payment_method' => $paymentMethodId,
    //             ],
    //         ]);

    //         $paymentDate = new DateTime($request->input('paymentDate'));
    //         $currentDate = new DateTime();


    //         if ($paymentDate > $currentDate) {
    //             $billingCycleAnchor = $paymentDate->getTimestamp();
    //             $firstPaymentNow = false;
    //         } else {
    //             $billingCycleAnchor = $currentDate->getTimestamp();
    //             $firstPaymentNow = true;
    //         }


    //         $productId = $this->createStripeProduct(
    //             'Recurring Payment',
    //             'Monthly subscription for recurring payments'
    //         );

    //         $subscription = Subscription::create([
    //             'customer' => $customerId,
    //             'items' => [[
    //                 'price_data' => [
    //                     'currency' => 'usd',
    //                     'product' => $productId,
    //                     'unit_amount' => $request->input('amount') * 100,
    //                     'recurring' => ['interval' => 'month'],
    //                 ],
    //             ]],
    //             'billing_cycle_anchor' => $billingCycleAnchor,
    //             'proration_behavior' => 'none',
    //             'payment_behavior' => 'default_incomplete',
    //             'metadata' => [
    //                 'number_of_payments' => $request->input('numberOfPayments'),
    //                 'payment_date' => $paymentDate->format('Y-m-d'),
    //             ],
    //         ]);
    //         if ($firstPaymentNow) {
    //             $invoice = \Stripe\Invoice::create([
    //                 'customer' => $customerId,
    //                 'subscription' => $subscription->id,
    //                 'auto_advance' => true,
    //                 'billing_reason' => 'subscription_create',
    //             ]);

    //             $retrievedInvoice = \Stripe\Invoice::retrieve($invoice->id);
    //             $retrievedInvoice->pay();
    //         }

    //         $this->scheduleSubscriptionCancellation(
    //             $subscription->id,
    //             $request->input('numberOfPayments'),
    //             $paymentDate
    //         );

    //         return response()->json(['subscriptionId' => $subscription->id]);
    //     } catch (\Exception $e) {
    //         return response()->json(['error' => $e->getMessage()], 500);
    //     }
    // }

    public function setupSubscriptionForPaymentOptions(Request $request)
    {
        $user = auth()->user();
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);
            $paymentMethodId = $request->input('paymentMethodId');
            if (!$paymentMethodId) {
                Log::error('Payment method ID is missing');
                throw new \Exception('Payment method ID is required.');
            }
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $customerId]);

            Customer::update($customerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            $paymentDate = new DateTime($request->input('paymentDate'), new DateTimeZone('UTC'));
            $currentDate = new DateTime('now', new DateTimeZone('UTC'));

            $numPayments = $request->input('numberOfPayments');
            $endDate = clone $paymentDate;
            $endDate->modify('+' . ($numPayments - 1) . ' months');

            $currentDate = new DateTime('now', new DateTimeZone('UTC'));
            if ($endDate < $currentDate) {
                Log::error('The calculated end date is in the past. Process terminated.');
                return response()->json(['error' => 'Invalid end date: The calculated end date is in the past. Please provide a valid payment start date and number of payments.'], 400);
            }

            if ($paymentDate->format('Y-m-d') < $currentDate->format('Y-m-d')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment date cannot be in the past',
                ], 400);
            }

            $billingCycleAnchor = ($paymentDate <= $currentDate)
                ? $currentDate->modify('+1 month')->getTimestamp()
                : $paymentDate->getTimestamp();

            $productId = $this->createStripeProduct(
                'Recurring Payment',
                'Monthly subscription for recurring payments'
            );

            if (!$productId) {
                Log::error('Failed to create or retrieve Stripe product');
                throw new \Exception('Failed to create or retrieve Stripe product.');
            }

            // DO NOT create a PaymentIntent for the first payment. Stripe will handle the first charge automatically when the subscription is created.
            // Only create the subscription and let Stripe handle the first invoice/payment.
            if ($request->input('payment_type') == 'recurring') {
                // Fetch pending amounts and other required data
                $guardianPayments = GuardianPayment::where('user_id', $user->id)
                    ->where('payment_type', '=', 'split')
                    ->where('pending_amount', '>', 0)
                    ->select('program_id', 'player_ids', DB::raw('SUM(pending_amount) as total_pending_amount'))
                    ->groupBy('program_id', 'player_ids')
                    ->get();

                $pendingAmountSum = $guardianPayments->sum('total_pending_amount');

                $playerProgram = $guardianPayments->mapWithKeys(function ($item) {
                    return [$item->program_id => explode(',', $item->player_ids)];
                });

                // Fetch pending amounts grouped by program_id
                $pendingAmountsByProgram = GuardianPayment::where('user_id', $user->id)
                    ->where('payment_type', '=', 'split')
                    ->where('pending_amount', '>', 0)
                    ->groupBy('program_id')
                    ->select('program_id', DB::raw('SUM(pending_amount) as total_pending_amount'))
                    ->pluck('total_pending_amount', 'program_id');
            }

            $stripeSubscription = Subscription::create([
                'customer' => $customerId,
                'items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product' => $productId,
                        'unit_amount' => $request->input('amount') * 100,
                        'recurring' => ['interval' => 'month'],
                    ],
                ]],
                'billing_cycle_anchor' => $billingCycleAnchor,
                'proration_behavior' => 'none',
                'payment_behavior' => 'default_incomplete',
                'metadata' => [
                    'number_of_payments' => $numPayments,
                    'start_date' => $paymentDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'user_id' => $user->id,
                    'initial_amount_paid' => $request->input('amount'),
                    'total_amount_due' => $request->input('totalAmount'),
                    'payment_type' => 'Outstanding to recurring',
                    'email' => $user->email,
                    'address' => $request->input('address'),
                    'state' => $request->input('state'),
                    'player_program' => $playerProgram ?? null,
                    'pendingAmountsByProgram' => $pendingAmountsByProgram ?? null,
                ],
            ]);

            $stripeSubscription->metadata['subscription_id'] = $stripeSubscription->id ?: null;
            $stripeSubscription->save();

            // Create local subscription record
            $localSubscription = $this->createLocalSubscription($stripeSubscription, $user, [
                'stripe_product_id' => $productId,
                'payment_type' => 'outstanding_to_recurring',
                'amount_per_payment' => $request->input('amount'),
                'total_amount_due' => $request->input('totalAmount'),
                'initial_amount_paid' => $request->input('amount'),
                'amount_paid' => $request->input('amount'), // Set first payment as paid
                'payments_completed' => 1, // Set first payment as completed
                'number_of_payments' => $numPayments,
                'start_date' => $paymentDate,
                'end_date' => $endDate,
                'cancel_at' => $endDate,
                'program_ids' => $playerProgram ? array_keys($playerProgram->toArray()) : null,
                'player_ids' => $playerProgram ? array_values($playerProgram->toArray())[0] ?? null : null,
                'pending_amounts_by_program' => $pendingAmountsByProgram ?? null,
                'billing_address' => $request->input('address'),
                'billing_state' => $request->input('state'),
                'description' => 'Outstanding balance converted to recurring payments',
            ]);

            $this->scheduleSubscriptionCancellationforPaymentOption(
                $stripeSubscription->id,
                $endDate
            );

            return response()->json(['subscriptionId' => $stripeSubscription->id]);
        } catch (\Exception $e) {
            Log::error('Error setting up subscription: ' . $e->getMessage());
            Log::info('end date: ' . $endDate->format('Y-m-d'));
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }










    protected function scheduleSubscriptionCancellationforPaymentOption($subscriptionId, DateTime $endDate)
    {
        try {

            $subscription = Subscription::retrieve($subscriptionId);

            $subscription->cancel_at = $endDate->getTimestamp();
            $subscription->save();
        } catch (\Exception $e) {
            throw $e;
        }
    }


    public function recurringPaymentStatusForPaymentOptions(Request $request)
    {
        try {
            $user = auth()->user();
            $validated = $request->validate([
                'subscriptionId' => 'required|string',
                'address' => 'required|string',
                'state' => 'required|string',
                'status' => 'required|string',
                'amount' => 'required|numeric',
                'totalAmount' => 'required|numeric',
                'paymentDate' => 'required|date',
                'numberOfPayments' => 'required|integer',
                'paymentMethodId' => 'required|string',
                'createdAt' => 'required|date',
            ]);

            $guardianPayments = GuardianPayment::where('user_id', $user->id)
                ->get();

            $totalBalanceDue = $guardianPayments->sum('pending_amount');


            $paymentDate = new DateTime($validated['paymentDate'], new DateTimeZone('UTC'));
            $currentDate = new DateTime('now', new DateTimeZone('UTC'));


            if ($paymentDate->format('Y-m-d') == $currentDate->format('Y-m-d')) {
                foreach ($guardianPayments as $payment) {
                    $payment->update(['pending_amount' => 0.00]);
                }
            } else {


                try {
                    foreach ($guardianPayments as $payment) {
                        $payment->update(['pending_amount' => 0.00]);
                    }


                    $end_date_timestamp = strtotime("+" . ($validated['numberOfPayments'] - 1) . " months", strtotime($validated['paymentDate']));
                    $end_date = date('Y-m-d', $end_date_timestamp);


                    $outStandingRecurringPayments = DB::table('outstanding_recurring_payments')->insert([
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'stripe_subscription_id' => $validated['subscriptionId'],
                        'initial_amount_paid' => 0.00,
                        'amount_paid' => 0.00,
                        'start_date' => $validated['paymentDate'],
                        'end_date' => $end_date,
                        'total_amount_due' => $validated['totalAmount'],
                        'payment_type' => 'recurring',
                        'number_of_payments' => $validated['numberOfPayments'],
                        'created_at' => now(),
                    ]);
                } catch (\Exception $e) {

                    Log::error('Failed to insert outstanding recurring payment record.', [
                        'error_message' => $e->getMessage(),
                        'user_id' => $user->id ?? null,
                        'validated_data' => $validated,
                    ]);
                    throw $e;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Recurring payment status updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the recurring payment: ' . $e->getMessage(),
            ], 500);
        }
    }




    public function setupRecurringPayments(Request $request)
    {
        $user = auth()->user();
        Stripe::setApiKey(env('STRIPE_SECRET'));

        $playerIds = $request->input('player_ids');
        $playerIdsString = json_encode($playerIds);

        try {
            // Create Stripe Customer if not already exists
            $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);

            // Attach the payment method to the customer
            $paymentMethodId = $request->input('paymentMethodId');
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $customerId]);

            // Set the default payment method for the customer
            \Stripe\Customer::update($customerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);

            // Calculate the total and the paid amount
            $totalAmountToBePaid = $request->input('totalAmountToBePaid');
            $paidAmount = $request->input('paidAmount');
            $numberOfPayments = ceil($totalAmountToBePaid / $paidAmount);
            $productId = $this->createStripeProduct(
                'Recurring Payment',
                'Monthly subscription for recurring payments'
            );

            // Dates for subscription
            $startDate = time();
            $numPayments = $numberOfPayments - 1;
            $endDate = strtotime("+$numPayments months", $startDate);

            // DO NOT create a PaymentIntent or Invoice for the first payment. Stripe will handle the first charge automatically when the subscription is created.
            $subscription = \Stripe\Subscription::create([
                'customer' => $customerId,
                'items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product' => $productId,
                        'unit_amount' => $paidAmount * 100,
                        'recurring' => ['interval' => 'month'],
                    ],
                ]],
                'proration_behavior' => 'none',
                'payment_behavior' => 'allow_incomplete',
                'metadata' => [
                    'user_id' => $user->id,
                    'card_holder' => $request->input('cardHolderName'),
                    'address' => $request->input('address'),
                    'state' => $request->input('state'),
                    'program_id' => $request->input('program_id'),
                    'payment_type' => $request->input('payment_type'),
                    'player_ids' => $playerIdsString,
                    'town' => $user->town ?? null,
                    'team_id' => $request->input('team_id') ?: null,
                    'email' => $request->input('email'),
                    'number_of_payments' => $numberOfPayments,
                    'initial_paid_amount' => $paidAmount,
                    'total_amount_due' => $totalAmountToBePaid,
                    'start_date' => date('Y-m-d', $startDate),
                    'end_date' => date('Y-m-d', $endDate),
                ],
            ]);

            // Schedule cancellation after the last payment
            $this->scheduleSubscriptionCancellation(
                $subscription->id,
                new DateTime("@$endDate")
            );

            // Create local subscription record
            $localSubscription = $this->createLocalSubscription($subscription, $user, [
                'stripe_product_id' => $productId,
                'payment_type' => 'recurring',
                'amount_per_payment' => $paidAmount,
                'total_amount_due' => $totalAmountToBePaid,
                'initial_amount_paid' => $paidAmount,
                'number_of_payments' => $numberOfPayments,
                'start_date' => Carbon::createFromTimestamp($startDate),
                'end_date' => Carbon::createFromTimestamp($endDate),
                'cancel_at' => Carbon::createFromTimestamp($endDate),
                'program_ids' => [$request->input('program_id')],
                'player_ids' => $playerIds,
                'customer_email' => $request->input('email') ?: $user->email,
                'customer_name' => $request->input('cardHolderName') ?: "{$user->firstName} {$user->lastName}",
                'billing_address' => $request->input('address'),
                'billing_state' => $request->input('state'),
                'default_payment_method' => $paymentMethodId,
                'description' => 'Recurring payment subscription for program registration',
            ]);

            return response()->json([
                'subscriptionId' => $subscription->id,
                'start_date' => date('Y-m-d', $startDate),
                'number_of_payments' => $numberOfPayments,
                'end_date' => date('Y-m-d', $endDate),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }








    protected function scheduleSubscriptionCancellation($subscriptionId,  DateTime $endDate)
    {
        $cancellationDate = clone $endDate;

        Subscription::update($subscriptionId, [
            'cancel_at' => $cancellationDate->getTimestamp(),
        ]);
    }

    /**
     * Create a local subscription record from Stripe subscription data
     */
    protected function createLocalSubscription($stripeSubscription, $user, $additionalData = [])
    {
        $defaultData = [
            'user_id' => $user->id,
            'stripe_subscription_id' => $stripeSubscription->id,
            'stripe_customer_id' => $stripeSubscription->customer,
            'status' => $stripeSubscription->status,
            'currency' => $stripeSubscription->currency ?? 'usd',
            'interval' => $stripeSubscription->items->data[0]->price->recurring->interval ?? 'month',
            'interval_count' => $stripeSubscription->items->data[0]->price->recurring->interval_count ?? 1,
            'current_period_start' => $stripeSubscription->current_period_start ? Carbon::createFromTimestamp($stripeSubscription->current_period_start) : null,
            'current_period_end' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            'next_payment_date' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end ?? false,
            'canceled_at' => $stripeSubscription->canceled_at ? Carbon::createFromTimestamp($stripeSubscription->canceled_at) : null,
            'cancel_at' => $stripeSubscription->cancel_at ? Carbon::createFromTimestamp($stripeSubscription->cancel_at) : null,
            'customer_email' => $user->email,
            'customer_name' => "{$user->firstName} {$user->lastName}",
            'billing_city' => $user->town,
            'metadata' => $stripeSubscription->metadata->toArray() ?? [],
            'last_synced_at' => now(),
        ];

        // Merge with additional data
        $subscriptionData = array_merge($defaultData, $additionalData);

        return LocalSubscription::create($subscriptionData);
    }

    /**
     * Update local subscription record from Stripe subscription data
     */
    protected function updateLocalSubscription($stripeSubscription, $additionalData = [])
    {
        $localSubscription = LocalSubscription::where('stripe_subscription_id', $stripeSubscription->id)->first();

        if (!$localSubscription) {
            Log::warning("Local subscription not found for Stripe subscription: {$stripeSubscription->id}");
            return null;
        }

        $updateData = [
            'status' => $stripeSubscription->status,
            'current_period_start' => $stripeSubscription->current_period_start ? Carbon::createFromTimestamp($stripeSubscription->current_period_start) : null,
            'current_period_end' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            'next_payment_date' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end ?? false,
            'canceled_at' => $stripeSubscription->canceled_at ? Carbon::createFromTimestamp($stripeSubscription->canceled_at) : null,
            'cancel_at' => $stripeSubscription->cancel_at ? Carbon::createFromTimestamp($stripeSubscription->cancel_at) : null,
            'ended_at' => $stripeSubscription->ended_at ? Carbon::createFromTimestamp($stripeSubscription->ended_at) : null,
            'metadata' => $stripeSubscription->metadata->toArray() ?? [],
            'last_synced_at' => now(),
        ];

        // Merge with additional data
        $updateData = array_merge($updateData, $additionalData);

        $localSubscription->update($updateData);

        return $localSubscription;
    }

    /**
     * Handle subscription payment success - increment payments completed and amount paid
     */
    protected function handleSubscriptionPaymentSuccess($stripeSubscription, $amountPaid)
    {
        $localSubscription = LocalSubscription::where('stripe_subscription_id', $stripeSubscription->id)->first();

        if (!$localSubscription) {
            Log::warning("Local subscription not found for payment success: {$stripeSubscription->id}");
            return null;
        }

        $localSubscription->increment('payments_completed');
        $localSubscription->increment('amount_paid', $amountPaid);
        $localSubscription->update(['last_synced_at' => now()]);

        return $localSubscription;
    }




    public function createStripeProduct($name, $description = null)
    {

        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {

            $product = \Stripe\Product::create([
                'name' => $name,
                'description' => $description,
                'type' => 'service',
            ]);

            return $product->id;
        } catch (\Exception $e) {

            return response()->json(['error' => $e->getMessage()], 500);
        }
    }


    // public function recurringPaymentStatus(Request $request){

    //     $user=auth()->user();

    //     $validated = $request->validate([
    //             'subscriptionId' => 'required|string',
    //             'status' => 'required|string',
    //             'amount' => 'required|numeric',
    //             'paymentDate' => 'required|date',
    //             'numberOfPayments' => 'required|integer',
    //             'paymentMethodId' => 'required|string',
    //         'playerIds' => 'required|array',
    //         'playerIds.*' => 'integer',
    //         'programId' => 'required|integer',
    //             'createdAt' => 'required|date',
    //         ]);



    //                 $playerIds = $validated['playerIds'];
    //                 $programId = $validated['programId'];

    //     $updateCount=0;

    //         foreach ($playerIds as $playerId) {

    //         $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


    //     PlayerProgram::create([
    //         'player_id' => $playerId,
    //         'program_id' => $programId,
    //     ]);


    //     $existingRegistration = ProgramRegistration::where('user_id', $user->id)
    //         ->where('player_id', $playerId)
    //         ->where('program_id', $programId)
    //         ->first();

    //     if ($existingRegistration) {

    //         $existingRegistration->update([
    //             'is_paid' => true,
    //         ]);
    //     } else {

    //         ProgramRegistration::create([
    //             'user_id' => $user->id,
    //             'program_id' => $programId,
    //             'amount' => $amountForPerPlayerByCost,
    //             'player_id' => $playerId,
    //             'is_paid' => true,
    //         ]);
    //     }

    //     $updateCount++;
    // }

    //     if ($updateCount > 0) {
    //         return response()->json(['message' => 'Payment status updated successfully.']);
    //     } else {
    //         return response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
    //     }
    //     }



    public function recurringPaymentDetails(Request $request)
    {
        try {
            $user = auth()->user();

            $validated = $request->validate([
                'subscriptionId' => 'required|string',
                'payment_type' => 'required|string',
                'status' => 'required|string',
                'amount' => 'required|numeric',
                'paymentMethodId' => 'required|string',
                'paidAmount' => 'required|numeric',
                'playerIds' => 'required|array',
                'playerIds.*' => 'integer',
                'programId' => 'required|integer',
                'createdAt' => 'required|date',
                'team_id' => 'nullable'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => 'Validation error', 'details' => $e->errors()], 422);
        }

        if (!isset($validated['team_id'])) {

            try {
                $playerIds = $validated['playerIds'];
                $programId = $validated['programId'];
                $updateCount = 0;

                foreach ($playerIds as $playerId) {
                    $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

                    PlayerProgram::create([
                        'player_id' => $playerId,
                        'program_id' => $programId,
                    ]);

                    $existingRegistration = ProgramRegistration::where('user_id', $user->id)
                        ->where('player_id', $playerId)
                        ->where('program_id', $programId)
                        ->first();

                    if ($existingRegistration) {
                        $existingRegistration->update(['is_paid' => true]);
                    } else {
                        ProgramRegistration::create([
                            'user_id' => $user->id,
                            'program_id' => $programId,
                            'amount' => $amountForPerPlayerByCost,
                            'player_id' => $playerId,
                            'is_paid' => true,
                        ]);
                    }

                    $updateCount++;
                }
            } catch (\Exception $e) {
                return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
            }
        } else {

            $playerIds = $validated['playerIds'];
            $programId = $validated['programId'];
            $updateCount = 0;

            foreach ($playerIds as $playerId) {
                $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

                PlayerProgram::create([
                    'player_id' => $playerId,
                    'program_id' => $programId,
                ]);

                TeamPlayer::create([
                    'team_id' => $validated['team_id'],
                    'player_id' => $playerId,
                ]);

                $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

                $balanceDue = $validated['amount'] - $validated['paidAmount'];

                // Only update balance_due, status will be updated after successful payment
                $playerInvitation->update([
                    'balance_due' => $balanceDue
                ]);
            }
            $updateCount++;
        }


        try {
            $totalAmount = $validated['amount'];
            $paidAmount = $validated['paidAmount'];
            $pendingAmount = $totalAmount - $paidAmount;
            if ($validated['payment_type'] === 'recurringPayments') {
                $paymentType = 'recurring';
            } elseif ($validated['payment_type'] === 'split') {
                $paymentType = "split";
            } else {
                $paymentType = 'full';
            }

            GuardianPayment::create([
                'user_id' => $user->id,
                'payment_id' => $validated['paymentMethodId'],
                'paid_amount' => $paidAmount,
                'pending_amount' => $pendingAmount,
                'created_at' => $validated['createdAt'],
                'payment_type' => $paymentType,
                'program_id' => $programId,
                'player_ids' => json_encode($playerIds),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Error saving payment details', 'details' => $e->getMessage()], 500);
        }

        return $updateCount > 0
            ? response()->json(['message' => 'Payment status updated successfully.'])
            : response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
    }



    public function specificAmountPayment(Request $request)
    {
        $user = auth()->user();

        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));
            $user = auth()->user();

            $validated = $request->validate([
                'payment_type' => 'required|string',
                'amount' => 'required|numeric',
                'paymentMethodId' => 'required|string',
                'paidAmount' => 'required|numeric',
                'player_ids' => 'required|array',
                'player_ids.*' => 'integer',
                'program_id' => 'required|integer',
                'createdAt' => 'required|date',
                'cardHolderName' => 'required|string',
                'state' => 'required|string',
                'address' => 'required|string',
                'team_id' => 'nullable'
            ]);


            $paymentIntent = PaymentIntent::create([
                'amount' => $validated['paidAmount'] * 100,
                'currency' => 'usd',
                'payment_method' => $validated['paymentMethodId'],
                'confirm' => true,
                'description' => 'Program payment for user ID: ' . $user->id,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',
                ],
                'metadata' => [
                    'cardHolderName' => $validated['cardHolderName'],
                    'address' => $validated['address'],
                    'state' => $validated['state'],
                    'user_id' => $user->id,
                    'program_id' => $validated['program_id'],
                    'player_ids' => json_encode($validated['player_ids']),
                    'payment_type' => $validated['payment_type'],
                    'paid_amount' => $validated['paidAmount'],
                    'town' => $user->town ?? null,
                    'email' => $user->email,
                    'payment_id' => $validated['paymentMethodId']
                ],
            ]);

            if ($paymentIntent->status === 'requires_action') {
                return response()->json([
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret,
                ]);
            }

            // If payment is successful, create charge record immediately
            // if ($paymentIntent->status === 'succeeded') {
            //     $this->createChargeFromPaymentIntent($paymentIntent, $user, $validated);
            // }

            if (!isset($validated['team_id'])) {
                try {
                    $playerIds = $validated['player_ids'];
                    $programId = $validated['program_id'];
                    $updateCount = 0;

                    foreach ($playerIds as $playerId) {

                        $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


                        PlayerProgram::create(['player_id' => $playerId, 'program_id' => $programId]);


                        ProgramRegistration::updateOrCreate(
                            [
                                'user_id' => $user->id,
                                'player_id' => $playerId,
                                'program_id' => $programId,
                            ],
                            ['is_paid' => true, 'amount' => $amountForPerPlayerByCost]
                        );

                        $updateCount++;
                    }
                    GuardianPayment::create([
                        'user_id' => $user->id,
                        'payment_id' => $validated['paymentMethodId'],
                        'paid_amount' => $validated['paidAmount'],
                        'pending_amount' => $validated['amount'] - $validated['paidAmount'],
                        'created_at' => $validated['createdAt'],
                        'payment_type' => ($validated['payment_type'] === 'specificAmount') ? 'split' : 'split',
                        'program_id' => $programId,
                        'player_ids' => json_encode($playerIds),
                    ]);

                    return response()->json(['message' => 'Payment status updated successfully.']);
                } catch (\Exception $e) {
                    return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
                }
            } else {


                try {
                    $playerIds = $validated['player_ids'];
                    $programId = $validated['program_id'];
                    $updateCount = 0;

                    foreach ($playerIds as $playerId) {

                        $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


                        PlayerProgram::create(['player_id' => $playerId, 'program_id' => $programId]);

                        TeamPlayer::create([
                            'team_id' => $validated['team_id'],
                            'player_id' => $playerId,
                        ]);



                        $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

                        $balanceDue = $validated['amount'] - $validated['paidAmount'];

                        // Only update balance_due, status will be updated after successful payment
                        $playerInvitation->update([
                            'balance_due' => $balanceDue
                        ]);

                        $updateCount++;
                    }



                    GuardianPayment::create([
                        'user_id' => $user->id,
                        'payment_id' => $validated['paymentMethodId'],
                        'paid_amount' => $validated['paidAmount'],
                        'pending_amount' => $validated['amount'] - $validated['paidAmount'],
                        'created_at' => $validated['createdAt'],
                        'payment_type' => ($validated['payment_type'] === 'specificAmount') ? 'split' : 'split',
                        'program_id' => $programId,
                        'player_ids' => json_encode($playerIds),
                    ]);

                    return response()->json(['message' => 'Payment status updated successfully.']);
                } catch (\Exception $e) {
                    return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
                }
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Payment processing error', 'details' => $e->getMessage()], 500);
        }
    }




    public function acceptInvitePayment(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            $validated = $request->validate([
                'paymentMethodId' => 'required|string',
                'amount' => 'required|numeric|min:1',
                'coach' => 'required|integer|exists:users,id',
                'team' => 'required|integer|exists:teams,id',
                'program' => 'required|integer|exists:programs,id',
                'user' => 'required|integer|exists:users,id',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422);
        }

        try {
            $amountInCents = $validated['amount'] * 100;

            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => 'usd',
                'payment_method' => $validated['paymentMethodId'],
                'confirm' => true,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',  // Prevent redirects
                ],
            ]);;

            $guardian = auth()->user();

            // If payment is successful, create charge record immediately
            // if ($paymentIntent->status === 'succeeded') {
            //     $this->createChargeFromPaymentIntent($paymentIntent, $guardian, [
            //         'payment_type' => 'invite_payment',
            //         'program_id' => $validated['program'],
            //         'player_ids' => [$validated['user']],
            //     ]);
            // }

            $playerInvitation = PlayerInvitation::where('coach_id', $validated['coach'])
                ->where('user_id', $validated['user'])
                ->where('program_id', $validated['program'])
                ->firstOrFail();

            $balanceDue = $validated['amount'] - $playerInvitation->balance_due;

            // Only update balance_due, status will be updated after successful payment
            $playerInvitation->update([
                'balance_due' => $balanceDue,
            ]);

            GuardianPayment::create([
                'user_id' => $guardian->id,
                'payment_id' => $paymentIntent->id,
                'paid_amount' => $validated['amount'],
                'pending_amount' => $balanceDue,
            ]);

            return response()->json([
                'success' => true,
                'paymentIntent' => $paymentIntent,
                'redirectUrl' => route('payment.success'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage(),
            ], 400);
        }
    }


    //  public function storeRecurringPayments(Request $request)
    // {
    //     Stripe::setApiKey(env('STRIPE_SECRET'));
    //     $payload = $request->getContent();
    //     $sigHeader = $request->header('Stripe-Signature');
    //     $endpointSecret = env('STRIPE_WEBHOOK_SECRET');

    //     try {
    //         $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
    //         if ($event->type == 'invoice.payment_succeeded') {
    //             $invoice = $event->data->object;
    //             $metadata = $invoice->lines->data[0]->metadata ?? null;

    //             if ($metadata) {
    //                 $playerIds = json_encode($metadata['player_ids'] ?? []);
    //                 $paymentRecord = DB::table('recurring_payments')
    //                     ->where('user_id', $metadata['user_id'] ?? null)
    //                     ->where('program_id', $metadata['program_id'] ?? null)
    //                     ->where('team_id', $metadata['team_id'] ?? null)
    //                     ->whereJsonContains('player_ids', $playerIds)
    //                     ->first();

    //                 if ($paymentRecord) {
    //                     DB::table('recurring_payments')
    //                         ->where('id', $paymentRecord->id)
    //                         ->update([
    //                             'paid_amount' => DB::raw("paid_amount + {$invoice->amount_paid} / 100"),
    //                             'updated_at' => now(),
    //                         ]);
    //                 } else {
    //                     DB::table('recurring_payments')->insert([
    //                         'user_id' => $metadata['user_id'] ?? null,
    //                         'card_holder' => $metadata['card_holder'] ?? null,
    //                         'address' => $metadata['address'] ?? null,
    //                         'state' => $metadata['state'] ?? null,
    //                         'program_id' => $metadata['program_id'] ?? null,
    //                         'player_ids' => $playerIds,
    //                         'team_id' => $metadata['team_id'] ?? null,
    //                         'email' => $metadata['email'] ?? null,
    //                         'number_of_payments' => $metadata['number_of_payments'] ?? null,
    //                         'initial_paid_amount' => $metadata['initial_paid_amount'] ?? null,
    //                         'total_amount_due' => $metadata['total_amount_due'] ?? null,
    //                         'paid_amount' => $invoice->amount_paid / 100,
    //                         'start_date' => $metadata['start_date'] ?? null,
    //                         'end_date' => $metadata['end_date'] ?? null,
    //                         'created_at' => now(),
    //                         'updated_at' => now(),
    //                     ]);
    //                 }
    //             } else {
    //                 Log::error('No metadata found in the invoice', ['invoice' => $invoice]);
    //             }
    //         }
    //         return response()->json(['status' => 'success']);
    //     } catch (SignatureVerificationException $e) {

    //         Log::error('Webhook signature verification failed', ['error' => $e->getMessage()]);
    //         return response()->json(['error' => 'Webhook signature verification failed'], 400);
    //     } catch (\Exception $e) {
    //         Log::error('Error processing webhook', ['error' => $e->getMessage()]);
    //         return response()->json(['error' => $e->getMessage()], 500);
    //     }
    // }


    public function subscriptionCreated(Request $request)
    {

        return response()->json([
            'success' => true,
            'message' => 'recurring payments setted successfully'
        ]);
    }



    public function storeRecurringPayments(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = env('STRIPE_WEBHOOK_SECRET');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);

            //TODO: it will trigger when first payment is made but when next month payment will come the event will be payment succeeded
            //it will save the one time payment efficiently but we also have to store recurring payments here so we will think something
            //to fix it.
            if ($event->type === 'invoice.created') {
                Log::info('event is invoice created');
                $invoice = $event->data->object; // Stripe\Invoice object
                $metadata = $invoice->metadata;

                // If no metadata, try to get it from subscription
                if (!$metadata && $invoice->subscription) {
                    try {
                        $subscription = \Stripe\Subscription::retrieve($invoice->subscription);
                        $metadata = $subscription->metadata;
                        Log::info('Retrieved metadata from subscription for invoice.created', ['metadata' => $metadata]);
                    } catch (\Exception $e) {
                        Log::error('Failed to retrieve subscription metadata for invoice.created', ['error' => $e->getMessage()]);
                    }
                }

                if (!$metadata) {
                    Log::info('no metadata is in invoice created');
                    return response('OK', 200);
                }

                // Only create invoice records for recurring payments
                $paymentType = $this->normalizePaymentType($metadata['payment_type'] ?? null);
                if ($paymentType !== 'recurring' && $paymentType !== 'outstanding_to_recurring') {
                    Log::info('Skipping invoice creation for non-recurring payment', [
                        'invoice_id' => $invoice->id,
                        'payment_type' => $paymentType,
                        'original_payment_type' => $metadata['payment_type'] ?? null
                    ]);
                    return response('OK', 200);
                }

                // Create invoice record in our database for recurring payments only
                $this->createInvoiceFromStripe($invoice->id);

                if ($metadata && $metadata['payment_type'] === 'Outstanding to recurring') {

                    $paymentId = $metadata['payment_id'] ?? $invoice->payment_intent;

                    $pendingAmountsByProgram = json_decode($metadata['pendingAmountsByProgram'], true);
                    $playerProgram = json_decode($metadata['player_program'], true);
                    $initialAmountPaid = (float)$metadata['initial_amount_paid'];
                    $remainingAmount = $initialAmountPaid;

                    foreach ($pendingAmountsByProgram as $programId => $pendingAmount) {
                        $pendingAmount = (float)$pendingAmount;
                        $amountToDeduct = min($remainingAmount, $pendingAmount);
                        $remainingAmount -= $amountToDeduct;

                        $playerIds = $playerProgram[$programId] ?? [];

                        $playerIds = array_map(function ($id) {
                            return trim($id, '[]"');
                        }, $playerIds);

                        foreach ($playerIds as $playerId) {
                            DB::table('all_payments')->insert([
                                'user_id' => $metadata['user_id'],
                                'payment_id' => $paymentId,
                                'town' => $metadata['town'] ?? null,
                                'paid_amount' => $amountToDeduct,
                                'program_ids' => json_encode([$programId]),
                                'player_ids' => json_encode([$playerId]),
                                'payment_type' => $metadata['payment_type'],
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                        if ($remainingAmount <= 0) {
                            break;
                        }
                    }
                }

                Log::info('Invoice Metadata:',  ['metadata' => $metadata]);
            }

            if ($event->type == "charge.succeeded") {

                Log::info('Event Object', ['event' => $event]);

                $charge = $event->data->object;
                $paymentIntent = $charge->payment_intent;

                // Get metadata from the charge, payment intent, or subscription
                $metadata = $charge->metadata ?? null;
                if (!$metadata && $paymentIntent) {
                    try {
                        $paymentIntentObj = PaymentIntent::retrieve($paymentIntent);
                        $metadata = $paymentIntentObj->metadata ?? null;
                    } catch (\Exception $e) {
                        Log::error('Failed to retrieve payment intent metadata', ['error' => $e->getMessage()]);
                    }
                }

                // If still no metadata and this is from a subscription, try to get metadata from subscription
                if (!$metadata && $charge->invoice) {
                    try {
                        $invoice = \Stripe\Invoice::retrieve($charge->invoice);
                        if ($invoice->subscription) {
                            $subscription = \Stripe\Subscription::retrieve($invoice->subscription);
                            $metadata = $subscription->metadata ?? null;
                            Log::info('Retrieved metadata from subscription for charge', ['metadata' => $metadata]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to retrieve subscription metadata for charge', ['error' => $e->getMessage()]);
                    }
                }

                Log::info('Charge metadata', ['metadata' => $metadata]);

                // Check if charge record already exists
                $existingCharge = \App\Models\Charge::where('stripe_charge_id', $charge->id)->first();
                if ($existingCharge) {
                    Log::info('Charge record already exists, skipping creation', [
                        'charge_id' => $existingCharge->id,
                        'stripe_charge_id' => $charge->id,
                    ]);
                    $chargeRecord = $existingCharge;
                } else {
                    // Only create charge record if we have metadata (to avoid duplicates from invoice webhooks)
                    if ($metadata && isset($metadata['user_id'])) {
                        $chargeController = new \App\Http\Controllers\ChargeController();
                        $chargeRecord = $chargeController->createChargeFromStripe($charge->id);
                    } else {
                        Log::info('Skipping charge creation - no metadata or user_id found', [
                            'stripe_charge_id' => $charge->id,
                            'has_metadata' => !empty($metadata)
                        ]);
                        return response('OK', 200);
                    }
                }

                // Also update any temporary charge records that might exist
                $this->updateTemporaryChargeRecord($charge);

                if ($metadata) {

                    // Normalize payment type to keep consistency in the DB
                    if (isset($metadata['payment_type'])) {
                        $metadata['payment_type'] = $this->normalizePaymentType($metadata['payment_type']);
                    }
                    if (isset($metadata['affected_payments'])) {
                        $affectedPayments = json_decode($metadata['affected_payments'], true);


                        foreach ($affectedPayments as $payment) {

                            $programId = $payment['program_id'];
                            $playerIds = $payment['player_ids'];
                            $amount = $payment['amount'];


                            foreach ($playerIds as $playerId) {
                                DB::table('all_payments')->insert([
                                    'user_id' => $metadata['user_id'],
                                    'payment_id' => $metadata['payment_id'] ?? null,
                                    'paid_amount' => $amount,
                                    'program_ids' => json_encode([$programId]),
                                    'player_ids' => json_encode([$playerId]),
                                    'town' => $metadata['town'] ?? null,
                                    'payment_type' => $metadata['payment_type'] ?? null,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                            }

                            // Update invitation statuses for affected payments
                            $paymentMetadata = [
                                'user_id' => $metadata['user_id'],
                                'program_id' => $programId,
                                'player_ids' => $playerIds
                            ];
                            $this->updateInvitationStatusesAfterPayment($paymentMetadata);
                        }
                    } else if (isset($metadata['payment_type']) && $metadata['payment_type'] != "Outstanding to recurring") {
                        Log::info('inserting into all payments table', ['metdata' => $metadata]);
                        DB::table('all_payments')->insert([
                            'user_id' => $metadata['user_id'],
                            'payment_id' => $metadata['payment_id'] ?? null,
                            'paid_amount' => $metadata['paid_amount'] ?? 0,
                            'program_ids' => isset($metadata['program_id']) ? json_encode([$metadata['program_id']]) : null,
                            'player_ids' => isset($metadata['player_ids']) ? json_encode(json_decode($metadata['player_ids'], true)) : null,
                            'town' => $metadata['town'] ?? null,
                            'payment_type' => $this->normalizePaymentType($metadata['payment_type'] ?? null),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        // Update invitation statuses to 'accepted' after successful payment
                        $this->updateInvitationStatusesAfterPayment($metadata);

                        // Apply credit if there was credit to apply after payment
                        if (isset($metadata['has_credit_to_apply']) && $metadata['has_credit_to_apply'] && isset($metadata['credit_amount'])) {
                            $user = User::find($metadata['user_id']);
                            if ($user) {
                                $playerIds = json_decode($metadata['player_ids'], true);
                                $creditApplied = $this->applyCreditAfterPayment(
                                    $user,
                                    $metadata['program_id'],
                                    $playerIds,
                                    $metadata['credit_amount']
                                );

                                if ($creditApplied > 0) {
                                    Log::info('Credit applied after successful payment', [
                                        'user_id' => $user->id,
                                        'credit_applied' => $creditApplied,
                                        'program_id' => $metadata['program_id']
                                    ]);
                                }
                            }
                        }
                    }
                } else {
                    Log::error('No metadata found for this event');
                }
            }

            if ($event->type == 'invoice.payment_succeeded') {
                Log::info('Payment successful');
                $invoice = $event->data->object;
                $paymentIntent = $invoice->payment_intent;
                $metadata = $invoice->lines->data[0]->metadata ?? $invoice->metadata ?? null;

                // Update subscription next payment date
                if ($invoice->subscription) {
                    try {
                        $subscription = Subscription::where('stripe_subscription_id', $invoice->subscription)->first();
                        if ($subscription) {
                            $subscriptionService = new \App\Services\SubscriptionService();
                            $subscriptionService->updateNextPaymentDate($subscription);

                            // Update amount_paid and payments_completed for each successful payment
                            $subscription->increment('amount_paid', $invoice->amount_paid / 100);
                            $subscription->increment('payments_completed');
                            $subscription->last_synced_at = now();
                            $subscription->save();

                            // Update recurring_payments table
                            db::table('recurring_payments')
                                ->where('user_id', $subscription->user_id)
                                ->whereJsonContains('program_ids', $subscription->program_ids[0] ?? null)
                                ->increment('paid_amount', $invoice->amount_paid / 100);

                            // Update existing program payment transaction for subscription
                            $existingTransaction = ProgramPaymentTransaction::where('stripe_subscription_id', $subscription->stripe_subscription_id)
                                ->where('transaction_type', 'down_payment')
                                ->first();

                            if ($existingTransaction) {
                                $existingTransaction->update([
                                    'amount' => $existingTransaction->amount + ($invoice->amount_paid / 100),
                                    'installment_number' => $subscription->payments_completed,
                                    'next_payment_date' => $subscription->next_payment_date,
                                    'metadata' => array_merge($existingTransaction->metadata ?? [], [
                                        'last_payment_invoice' => $invoice->id,
                                        'total_payments_made' => $subscription->payments_completed
                                    ])
                                ]);
                            }
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to update subscription next payment date', [
                            'subscription_id' => $invoice->subscription,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                Log::info('Payment is successful', ['metadata' => $metadata]);

                // Create or update invoice record
                $this->createInvoiceFromStripe($invoice->id);

                if ($metadata) {
                    Log::info('Metadata found in the invoice', ['metadata' => $metadata]);
                    if ($metadata['payment_type'] === 'Outstanding to recurring') {
                        Log::info('Payment type is outstanding to recurring', ['metadata' => $metadata]);
                        // Handle outstanding recurring payments
                        $paymentRecord = DB::table('outstanding_recurring_payments')
                            ->where('user_id', $metadata['user_id'] ?? null)
                            ->where('stripe_subscription_id', $metadata['subscription_id'])
                            ->first();

                        if ($paymentRecord) {
                            Log::info('Payment record found, updating...', ['payment_record' => $paymentRecord]);

                            DB::table('outstanding_recurring_payments')
                                ->where('id', $paymentRecord->id)
                                ->update([
                                    'initial_amount_paid' => $metadata['initial_amount_paid'] ?? null,
                                    'amount_paid' => DB::raw("amount_paid + {$metadata['initial_amount_paid']}"),
                                    'updated_at' => now(),
                                ]);
                        } else {
                            Log::info('No payment record found, inserting new record...', ['metadata' => $metadata]);

                            DB::table('outstanding_recurring_payments')->insert([
                                'user_id' => $metadata['user_id'] ?? null,
                                'address' => $metadata['address'] ?? null,
                                'state' => $metadata['state'] ?? null,
                                'email' => $metadata['email'] ?? null,
                                'number_of_payments' => $metadata['number_of_payments'] ?? null,
                                'initial_amount_paid' => $metadata['initial_amount_paid'] ?? null,
                                'total_amount_due' => $metadata['total_amount_due'] ?? null,
                                'amount_paid' => DB::raw("amount_paid + {$metadata['initial_amount_paid']}"),
                                'payment_type' => 'recurring',
                                'start_date' => $metadata['start_date'] ?? null,
                                'end_date' => $metadata['end_date'] ?? null,
                                'stripe_customer_id' => $metadata['customer_id'] ?? null,
                                'stripe_subscription_id' => $metadata['subscription_id'] ?? null,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                    } else {
                        // Handle recurring payments
                        $playerIds = json_encode($metadata['player_ids'] ?? []);
                        $paymentRecord = DB::table('recurring_payments')
                            ->where('user_id', $metadata['user_id'] ?? null)
                            ->where('program_id', $metadata['program_id'] ?? null)
                            ->where('team_id', $metadata['team_id'] ?? null)
                            ->where('email', $metadata['email'] ?? null)
                            ->whereJsonContains('player_ids', $playerIds)
                            ->first();

                        if ($paymentRecord) {
                            DB::table('recurring_payments')
                                ->where('id', $paymentRecord->id)
                                ->update([
                                    'paid_amount' => DB::raw("paid_amount + {$invoice->amount_paid} / 100"),
                                    'updated_at' => now(),
                                ]);
                        } else {
                            if (!isset($metadata['user_id']) || empty($metadata['user_id'])) {
                                Log::error('User ID is missing in metadata:', ['metadata' => $metadata]);


                                if ($metadata instanceof \Stripe\StripeObject) {
                                    Log::info('Complete metadata:', $metadata->toArray());
                                } else {
                                    Log::info('Complete metadata:', $metadata);
                                }
                            }

                            Log::info('here is metadata', ['metadata' => $metadata]);


                            DB::table('recurring_payments')->insert([
                                'user_id' => $metadata['user_id'] ?? null,
                                'card_holder' => $metadata['card_holder'] ?? null,
                                'address' => $metadata['address'] ?? null,
                                'state' => $metadata['state'] ?? null,
                                'program_id' => $metadata['program_id'] ?? null,
                                'player_ids' => $playerIds,
                                'team_id' => $metadata['team_id'] ?? null,
                                'email' => $metadata['email'] ?? null,
                                'number_of_payments' => $metadata['number_of_payments'] ?? null,
                                'initial_paid_amount' => $metadata['initial_paid_amount'] ?? null,
                                'total_amount_due' => $metadata['total_amount_due'] ?? null,
                                'paid_amount' => $invoice->amount_paid / 100,
                                'start_date' => $metadata['start_date'] ?? null,
                                'end_date' => $metadata['end_date'] ?? null,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                    }
                } else {
                    Log::error('No metadata found in the invoice', ['invoice' => $invoice]);
                }
            }

            return response()->json(['status' => 'success']);
        } catch (SignatureVerificationException $e) {
            Log::error('Webhook signature verification failed', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Webhook signature verification failed'], 400);
        } catch (\Exception $e) {
            Log::error('Error processing webhook', ['error' => $e->getMessage()]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Create invoice record from Stripe invoice
     */
    private function createInvoiceFromStripe($stripeInvoiceId)
    {
        try {
            $invoiceController = new \App\Http\Controllers\InvoiceController();
            return $invoiceController->createInvoiceFromStripe($stripeInvoiceId);
        } catch (\Exception $e) {
            Log::error('Error creating invoice from Stripe', [
                'stripe_invoice_id' => $stripeInvoiceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Normalize payment type to match database enum values
     */
    private function normalizePaymentType($paymentType)
    {
        $mapping = [
            'recurringPayments' => 'recurring',
            'fullAmount' => 'full',
            'specificAmount' => 'split',
            'splitPayments' => 'split',
            'Outstanding to recurring' => 'outstanding_to_recurring',
        ];

        return $mapping[$paymentType] ?? $paymentType;
    }

    /**
     * Update invitation statuses to 'accepted' after successful payment
     */
    private function updateInvitationStatusesAfterPayment($metadata)
    {
        try {
            if (!isset($metadata['user_id']) || !isset($metadata['program_id']) || !isset($metadata['player_ids'])) {
                Log::warning('Missing required metadata for updating invitation statuses', ['metadata' => $metadata]);
                return;
            }

            $userId = $metadata['user_id'];
            $programId = is_array($metadata['program_id']) ? $metadata['program_id'] : json_decode($metadata['program_id'], true);
            $playerIds = is_array($metadata['player_ids']) ? $metadata['player_ids'] : json_decode($metadata['player_ids'], true);

            // Handle single program ID
            if (!is_array($programId)) {
                $programId = [$programId];
            }

            // Handle single player ID
            if (!is_array($playerIds)) {
                $playerIds = [$playerIds];
            }

            foreach ($programId as $progId) {
                foreach ($playerIds as $playerId) {
                    // Update PlayerInvitation status
                    PlayerInvitation::where('user_id', $playerId)
                        ->where('program_id', $progId)
                        ->where('invitation_status', 'pending')
                        ->update(['invitation_status' => 'accepted']);

                    // Update AdminInvitesPlayerForProgram status
                    \App\Models\AdminInvitesPlayerForProgram::where('user_id', $playerId)
                        ->where('program_id', $progId)
                        ->where('status', 'pending')
                        ->update(['status' => 'accepted']);
                }
            }

            Log::info('Successfully updated invitation statuses after payment', [
                'user_id' => $userId,
                'program_ids' => $programId,
                'player_ids' => $playerIds
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating invitation statuses after payment', [
                'error' => $e->getMessage(),
                'metadata' => $metadata
            ]);
        }
    }

    /**
     * Create charge record from successful PaymentIntent
     */
    private function createChargeFromPaymentIntent($paymentIntent, $user, $validated)
    {
        try {
            // Try to get charges with a small delay to ensure they're available
            $maxAttempts = 3;
            $attempt = 0;
            $charges = null;

            while ($attempt < $maxAttempts && empty($charges)) {
                // Retrieve the PaymentIntent with expanded charges to ensure we have the charge data
                $expandedPaymentIntent = PaymentIntent::retrieve([
                    'id' => $paymentIntent->id,
                    'expand' => ['charges'],
                ]);

                // Check if charges property exists and has data
                if (isset($expandedPaymentIntent->charges) && $expandedPaymentIntent->charges !== null) {
                    $charges = $expandedPaymentIntent->charges->data;
                }

                if (empty($charges)) {
                    $attempt++;
                    if ($attempt < $maxAttempts) {
                        // Wait 500ms before retrying
                        usleep(500000);
                    }
                }
            }

            if (empty($charges)) {
                Log::warning('No charges found in PaymentIntent after retries - will be handled by webhook', [
                    'payment_intent_id' => $paymentIntent->id,
                    'attempts' => $attempt
                ]);

                // Create a temporary charge record with basic info - will be updated by webhook
                return $this->createTemporaryChargeRecord($paymentIntent, $user, $validated);
            }

            $charge = $charges[0]; // Get the first charge

            // Check if charge already exists
            $existingCharge = \App\Models\Charge::where('stripe_charge_id', $charge->id)->first();
            if ($existingCharge) {
                return $existingCharge;
            }

            // Create charge record
            $chargeRecord = \App\Models\Charge::create([
                'user_id' => $user->id,
                'stripe_charge_id' => $charge->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'amount' => $charge->amount / 100,
                'currency' => $charge->currency,
                'status' => $charge->status,
                'payment_type' => $validated['payment_type'] === 'fullAmount' ? 'full' : 'split',
                'program_ids' => [$validated['program_id']],
                'player_ids' => $validated['player_ids'],
                'metadata' => $paymentIntent->metadata->toArray(),
                'charge_date' => \Carbon\Carbon::createFromTimestamp($charge->created),
                'paid_at' => $charge->status === 'succeeded' ? \Carbon\Carbon::createFromTimestamp($charge->created) : null,
                'description' => $charge->description ?? 'Program payment',
                'receipt_url' => $charge->receipt_url,
                'receipt_number' => $charge->receipt_number,
            ]);

            Log::info('Charge created from PaymentIntent', [
                'charge_id' => $chargeRecord->id,
                'stripe_charge_id' => $charge->id,
                'payment_intent_id' => $paymentIntent->id,
            ]);

            return $chargeRecord;
        } catch (\Exception $e) {
            Log::error('Error creating charge from PaymentIntent', [
                'payment_intent_id' => $paymentIntent->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create a temporary charge record when full charge data is not immediately available
     */
    private function createTemporaryChargeRecord($paymentIntent, $user, $validated)
    {
        try {
            // Check if a temporary record already exists
            $existingCharge = \App\Models\Charge::where('stripe_payment_intent_id', $paymentIntent->id)->first();
            if ($existingCharge) {
                return $existingCharge;
            }

            // Create a temporary charge record with available data
            $chargeRecord = \App\Models\Charge::create([
                'user_id' => $user->id,
                'stripe_charge_id' => null, // Will be updated by webhook
                'stripe_payment_intent_id' => $paymentIntent->id,
                'amount' => $paymentIntent->amount / 100,
                'currency' => $paymentIntent->currency,
                'status' => 'pending', // Will be updated by webhook
                'payment_type' => $validated['payment_type'] === 'fullAmount' ? 'full' : 'split',
                'program_ids' => [$validated['program_id']],
                'player_ids' => $validated['player_ids'],
                'metadata' => $paymentIntent->metadata->toArray(),
                'charge_date' => \Carbon\Carbon::createFromTimestamp($paymentIntent->created),
                'paid_at' => null, // Will be updated by webhook
                'description' => 'Program payment (temporary)',
                'receipt_url' => null, // Will be updated by webhook
                'receipt_number' => null, // Will be updated by webhook
            ]);

            Log::info('Temporary charge record created - will be updated by webhook', [
                'charge_id' => $chargeRecord->id,
                'payment_intent_id' => $paymentIntent->id,
            ]);

            return $chargeRecord;
        } catch (\Exception $e) {
            Log::error('Error creating temporary charge record', [
                'payment_intent_id' => $paymentIntent->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Update any temporary charge records that might exist
     */
    private function updateTemporaryChargeRecord($charge)
    {
        try {
            // First check if a charge record already exists with this stripe_charge_id
            $existingCharge = \App\Models\Charge::where('stripe_charge_id', $charge->id)->first();
            if ($existingCharge) {
                Log::info('Charge record already exists with this stripe_charge_id', [
                    'charge_id' => $existingCharge->id,
                    'stripe_charge_id' => $charge->id,
                ]);
                return $existingCharge;
            }

            // Look for temporary charge records by payment intent ID
            $temporaryCharge = \App\Models\Charge::where('stripe_payment_intent_id', $charge->payment_intent)
                ->whereNull('stripe_charge_id')
                ->first();

            if ($temporaryCharge) {
                // Update the temporary charge record with the full charge data
                $temporaryCharge->update([
                    'stripe_charge_id' => $charge->id,
                    'amount' => $charge->amount / 100,
                    'currency' => $charge->currency,
                    'status' => $charge->status,
                    'charge_date' => \Carbon\Carbon::createFromTimestamp($charge->created),
                    'paid_at' => $charge->status === 'succeeded' ? \Carbon\Carbon::createFromTimestamp($charge->created) : null,
                    'description' => $charge->description ?? 'Program payment',
                    'receipt_url' => $charge->receipt_url,
                    'receipt_number' => $charge->receipt_number,
                ]);

                Log::info('Temporary charge record updated with full charge data', [
                    'charge_id' => $temporaryCharge->id,
                    'stripe_charge_id' => $charge->id,
                    'payment_intent_id' => $charge->payment_intent,
                ]);

                return $temporaryCharge;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error updating temporary charge record', [
                'stripe_charge_id' => $charge->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
