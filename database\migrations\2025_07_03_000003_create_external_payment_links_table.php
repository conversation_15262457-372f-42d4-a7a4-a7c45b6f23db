<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('external_payment_links', function (Blueprint $table) {
            $table->id();
            $table->string('token')->unique(); // Secure token for the payment link
            $table->foreignId('payment_transaction_id')->constrained('payment_transactions')->onDelete('cascade');
            $table->foreignId('requesting_user_id')->constrained('users')->onDelete('cascade'); // Guardian who requested external payment
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');

            // External Payment Details
            $table->string('external_email'); // Email where payment link was sent
            $table->decimal('amount_to_pay', 10, 2); // Amount to be paid via this link
            $table->json('player_ids'); // Players being registered
            $table->json('player_names'); // Player names for display

            // Link Status
            $table->enum('status', ['created', 'sent', 'opened', 'payment_initiated', 'payment_completed', 'expired', 'cancelled'])->default('created');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('opened_at')->nullable();
            $table->timestamp('payment_completed_at')->nullable();
            $table->timestamp('expires_at')->nullable();

            // Payment Processing
            $table->string('stripe_payment_intent_id')->nullable();
            $table->string('stripe_charge_id')->nullable();
            $table->json('payment_metadata')->nullable();

            // Tracking
            $table->integer('open_count')->default(0); // How many times link was opened
            $table->json('access_logs')->nullable(); // IP addresses, timestamps, etc.

            $table->timestamps();

            // Indexes
            $table->index('token');
            $table->index(['status', 'expires_at']);
            $table->index('external_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_payment_links');
    }
};
