<?php

namespace Database\Factories;

use App\Models\PaymentTransaction;
use App\Models\User;
use App\Models\Program;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PaymentTransactionFactory extends Factory
{
    protected $model = PaymentTransaction::class;

    public function definition(): array
    {
        return [
            'transaction_id' => 'TXN_' . strtoupper(Str::random(16)),
            'user_id' => User::factory(),
            'program_id' => Program::factory(),
            'player_ids' => [$this->faker->numberBetween(1, 100)],
            'total_amount' => $this->faker->randomFloat(2, 50, 500),
            'paid_amount' => 0,
            'credit_used' => 0,
            'pending_amount' => function (array $attributes) {
                return $attributes['total_amount'];
            },
            'payment_method' => $this->faker->randomElement(['card', 'credit', 'external_link', 'mixed']),
            'payment_type' => $this->faker->randomElement(['full', 'split', 'recurring', 'external']),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'cancelled']),
            'external_email' => $this->faker->optional()->safeEmail(),
            'external_payment_token' => $this->faker->optional()->regexify('[A-Z0-9]{32}'),
            'external_payment_status' => $this->faker->optional()->randomElement(['link_created', 'link_sent', 'link_opened', 'payment_completed']),
            'external_link_sent_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'stripe_payment_intent_id' => $this->faker->optional()->regexify('pi_[a-zA-Z0-9]{24}'),
            'stripe_charge_id' => $this->faker->optional()->regexify('ch_[a-zA-Z0-9]{24}'),
            'payment_completed_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'payment_metadata' => [],
        ];
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'paid_amount' => 0,
            'pending_amount' => $attributes['total_amount'] ?? 100,
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'paid_amount' => $attributes['total_amount'] ?? 100,
            'pending_amount' => 0,
            'payment_completed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'stripe_payment_intent_id' => 'pi_' . Str::random(24),
            'stripe_charge_id' => 'ch_' . Str::random(24),
        ]);
    }

    public function external(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'external_link',
            'payment_type' => 'external',
            'external_email' => $this->faker->safeEmail(),
            'external_payment_token' => 'EPL_' . strtoupper(Str::random(32)),
            'external_payment_status' => 'link_created',
        ]);
    }

    public function withCredit(): static
    {
        return $this->state(function (array $attributes) {
            $totalAmount = $attributes['total_amount'] ?? 100;
            $creditUsed = $this->faker->randomFloat(2, 10, $totalAmount);
            
            return [
                'payment_method' => $creditUsed >= $totalAmount ? 'credit' : 'mixed',
                'credit_used' => $creditUsed,
                'pending_amount' => max(0, $totalAmount - $creditUsed),
            ];
        });
    }
}
