<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Request - {{ $program->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .payment-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .payment-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .payment-button:hover {
            background-color: #0056b3;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            margin-top: 30px;
        }
        .player-list {
            margin: 10px 0;
        }
        .player-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Payment Request</h1>
        <p>Mass Premier Courts</p>
    </div>

    <div class="content">
        <h2>Hello!</h2>

        <p>You have received a payment request from <strong>{{ $requestingUser->firstName }} {{ $requestingUser->lastName }}</strong> for the following program:</p>

        <div class="payment-details">
            <h3>Program Details</h3>
            <p><strong>Program:</strong> {{ $program->name }}</p>
            <p><strong>Amount Due:</strong> ${{ number_format($amount, 2) }}</p>

            @if(!empty($playerNames))
            <p><strong>Players:</strong></p>
            <ul class="player-list">
                @foreach($playerNames as $playerName)
                <li>{{ $playerName }}</li>
                @endforeach
            </ul>
            @endif

            <p><strong>Program Start Date:</strong> {{ $program->start_date ? \Carbon\Carbon::parse($program->start_date)->format('M d, Y') : 'TBD' }}</p>
            <p><strong>Program End Date:</strong> {{ $program->end_date ? \Carbon\Carbon::parse($program->end_date)->format('M d, Y') : 'TBD' }}</p>
            @if($program->location)
            <p><strong>Location:</strong> {{ $program->location }}</p>
            @endif
        </div>

        <div style="text-align: center;">
            <a href="{{ $paymentUrl }}" class="payment-button">Complete Payment</a>
        </div>

        <div class="warning">
            <strong>Important:</strong> This payment link will expire on {{ $expiresAt->format('M d, Y \a\t g:i A') }}.
            Please complete your payment before this date to secure the registration.
        </div>

        <h3>What happens next?</h3>
        <ol>
            <li>Click the "Complete Payment" button above</li>
            <li>Enter your payment information securely</li>
            <li>Complete the payment process</li>
            <li>You and {{ $requestingUser->firstName }} will receive confirmation emails</li>
            <li>The player(s) will be registered for the program</li>
        </ol>

        <p>If you have any questions about this payment request, please contact {{ $requestingUser->firstName }} {{ $requestingUser->lastName }} directly at {{ $requestingUser->email }}.</p>

        <p>For technical support or questions about the payment process, please contact Mass Premier Courts support.</p>
    </div>

    <div class="footer">
        <p>This is an automated email from Mass Premier Courts.</p>
        <p>If you did not expect this payment request, please contact us immediately.</p>
        <p>&copy; {{ date('Y') }} Mass Premier Courts. All rights reserved.</p>
    </div>
</body>
</html>
