<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProgramPaymentTransaction extends Model
{
    protected $fillable = [
        'program_id', 'user_id', 'player_id', 'transaction_type', 'payment_method',
        'amount', 'original_amount', 'discount_amount', 'credit_used', 'coupon_code',
        'stripe_payment_intent_id', 'status', 'installment_number', 'total_installments',
        'monthly_amount', 'next_payment_date', 'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'next_payment_date' => 'date'
    ];

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function player()
    {
        return $this->belongsTo(Player::class);
    }

    public function scopeRecurring($query)
    {
        return $query->where('transaction_type', 'recurring');
    }

    public function scopeForProgram($query, $programId)
    {
        return $query->where('program_id', $programId);
    }

    public function getPaymentSummary()
    {
        return [
            'original_amount' => $this->original_amount,
            'discount_applied' => $this->discount_amount,
            'credit_used' => $this->credit_used,
            'final_amount' => $this->amount,
            'coupon_code' => $this->coupon_code,
            'payment_method' => $this->payment_method,
            'is_recurring' => $this->transaction_type === 'recurring'
        ];
    }
}