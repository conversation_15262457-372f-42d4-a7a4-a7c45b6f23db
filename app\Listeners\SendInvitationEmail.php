<?php

namespace App\Listeners;

use App\Events\PlayerInvited;
use App\Mail\SendInvitationToPlayer;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendInvitationEmail
{
    public function handle(PlayerInvited $event)
    {
        Log::info('PlayerInvited listener triggered', ['emails' => $event->emails]);
        foreach ($event->emails as $email) {

            Mail::to($email)->send(new SendInvitationToPlayer($event->player, $event->coach, $event->program, $event->team));
        }
    }
}
