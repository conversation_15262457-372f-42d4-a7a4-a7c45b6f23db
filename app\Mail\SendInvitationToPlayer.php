<?php

namespace App\Mail;

use App\Models\Program;
use App\Models\Team;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;

class SendInvitationToPlayer extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $coach;
    public $program;
    public $team;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, User $coach, Program $program, Team $team)
    {
        $this->user = $user;
        $this->coach = $coach;
        $this->program = $program;
        $this->team = $team;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invitation to Join Team',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.invitePlayerForTeam',
            with: [
                'user' => $this->user,
                'coach' => $this->coach,
                'program' => $this->program,
                'team' => $this->team,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
