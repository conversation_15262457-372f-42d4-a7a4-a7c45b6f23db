<?php

namespace App\Http\Controllers;

use App\Models\PlayerProgram;
use App\Models\Program;
use App\Models\ProgramPaymentTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\Subscription;
use Stripe\Price;
use Carbon\Carbon;
use App\Models\ProgramRegistration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    public function prepareCheckout(Request $request)
    {
        $paymentData = $request->all();
        $program = Program::findOrFail($paymentData['program_id']);

        // Adjust paidAmount if credit is used
        if (isset($paymentData['used_credit']) && $paymentData['used_credit'] > 0) {
            $paymentData['paidAmount'] = max(0, $paymentData['paidAmount'] - $paymentData['used_credit']);
        }

        // Store checkout data in session
        Session::put('checkout_data', [
            'program' => $program,
            'paymentData' => $paymentData
        ]);

        return response()->json([
            'success' => true,
            'checkout_url' => route('guardian.checkout')
        ]);
    }

    public function showCheckout()
    {
        $checkoutData = Session::get('checkout_data');

        if (!$checkoutData) {
            return redirect()->route('guardian.dashboard')->with('error', 'Checkout session expired');
        }

        return view('guardian.checkout', [
            'program' => $checkoutData['program'],
            'paymentData' => $checkoutData['paymentData']
        ]);
    }

    public function createPaymentIntent(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        $paymentData = $request->all();

        // For recurring payments, only charge the down payment
        if ($paymentData['payment_type'] === 'recurringPayments') {
            $amount = $paymentData['down_payment'] * 100; // Convert to cents
        } else {
            $amount = $paymentData['paidAmount'] * 100; // Convert to cents
        }

        $intentData = [
            'amount' => $amount,
            'currency' => 'usd',
            'metadata' => [
                'program_id' => $paymentData['program_id'],
                'user_id' => $paymentData['user_id'],
                'payment_type' => $paymentData['payment_type'],
                'player_ids' => json_encode($paymentData['player_ids']),
                'paid_amount' => $paymentData['payment_type'] === 'recurringPayments' ? $paymentData['down_payment'] : $paymentData['paidAmount'],
                'original_amount' => $paymentData['original_amount'],
                'discount_amount' => $paymentData['discount_amount'] ?? 0,
                'credit_used' => $paymentData['used_credit'] ?? 0,
                'coupon_code' => $paymentData['applied_coupon']['code'] ?? null,
                'installment_months' => $paymentData['installment_months'] ?? null,
                'monthly_payment' => $paymentData['monthly_payment'] ?? null,
                'email' => auth()->user()->email,
                'town' => auth()->user()->town ?? null,
                'pending_amount' => $paymentData['pending_amount'] ?? 0,
            ]
        ];

        // For recurring payments, set up future usage
        if ($paymentData['payment_type'] === 'recurringPayments') {
            $intentData['setup_future_usage'] = 'off_session';
        }

        $paymentIntent = PaymentIntent::create($intentData);

        return response()->json([
            'client_secret' => $paymentIntent->client_secret
        ]);
    }

    public function processCreditPayment(Request $request)
    {
        $paymentData = $request->all();

        // Create payment transaction record
        $transaction = ProgramPaymentTransaction::create([
            'program_id' => $paymentData['program_id'],
            'user_id' => $paymentData['user_id'],
            'player_id' => $paymentData['player_ids'][0] ?? null,
            'transaction_type' => $paymentData['payment_type'] === 'recurringPayments' ? 'down_payment' : 'full',
            'payment_method' => 'credit',
            'amount' => $paymentData['paidAmount'],
            'original_amount' => $paymentData['original_amount'],
            'discount_amount' => $paymentData['discount_amount'] ?? 0,
            'credit_used' => $paymentData['used_credit'] ?? 0,
            'coupon_code' => $paymentData['applied_coupon']['code'] ?? null,
            'status' => 'completed',
            'installment_number' => $paymentData['payment_type'] === 'recurringPayments' ? 1 : null,
            'total_installments' => $paymentData['installment_months'] ?? null,
            'monthly_amount' => $paymentData['monthly_payment'] ?? null,
            'next_payment_date' => $paymentData['payment_type'] === 'recurringPayments' ? now()->addMonth() : null,
            'metadata' => [
                'payment_breakdown' => [
                    'original_cost' => $paymentData['original_amount'],
                    'coupon_discount' => $paymentData['discount_amount'] ?? 0,
                    'credit_applied' => $paymentData['used_credit'] ?? 0,
                    'final_amount' => $paymentData['paidAmount']
                ]
            ]
        ]);

        return response()->json(['success' => true]);
    }

    public function paymentSuccess()
    {
        $checkoutData = Session::get('checkout_data');
        $paymentIntentId = request()->get('payment_intent');

        if ($checkoutData) {
            $paymentData = $checkoutData['paymentData'];

            // Deduct credit if used
            if (isset($paymentData['used_credit']) && $paymentData['used_credit'] > 0) {
                $this->useGuardianCredit($paymentData['user_id'], $paymentData['used_credit'], $paymentData);
            }

            // Create subscription for recurring payments
            if ($paymentData['payment_type'] === 'recurringPayments') {
                // Get payment intent ID from URL parameters


                if ($paymentIntentId && strpos($paymentIntentId, 'pi_') === 0) {
                    $paymentData['payment_intent_id'] = $paymentIntentId;
                    $this->createRecurringSubscription($paymentData);
                }
            }


            if ($paymentData['payment_type'] === 'specificAmount') {
                $this->createSplitPaymentTransaction($paymentData, $paymentIntentId);
            }




            // Store program registration data for non-recurring payments


            foreach ($paymentData['player_ids'] as $playerId) {
                PlayerProgram::create([
                    'player_id' => $playerId,
                    'program_id' => $paymentData['program_id'],
                ]);

                ProgramRegistration::create([
                    'user_id' => $paymentData['user_id'],
                    'program_id' => $paymentData['program_id'],
                    'amount' => $paymentData['paidAmount'],
                    'player_id' => $playerId,
                    'is_paid' => true,
                    'pending_amount' => 0,
                ]);
            }


            // Create payment transaction record for Stripe payments
            $chargedAmount = $paymentData['payment_type'] === 'recurringPayments' ? $paymentData['down_payment'] : $paymentData['paidAmount'];

            $transaction = ProgramPaymentTransaction::create([
                'program_id' => $paymentData['program_id'],
                'user_id' => $paymentData['user_id'],
                'player_id' => $paymentData['player_ids'][0] ?? null,
                'transaction_type' => $paymentData['payment_type'] === 'recurringPayments' ? 'down_payment' : 'full',
                'payment_method' => 'stripe',
                'amount' => $chargedAmount,
                'original_amount' => $paymentData['original_amount'],
                'discount_amount' => $paymentData['discount_amount'] ?? 0,
                'credit_used' => $paymentData['used_credit'] ?? 0,
                'coupon_code' => $paymentData['applied_coupon']['code'] ?? null,
                'status' => 'completed',
                'installment_number' => $paymentData['payment_type'] === 'recurringPayments' ? 1 : null,
                'total_installments' => $paymentData['installment_months'] ?? null,
                'monthly_amount' => $paymentData['monthly_payment'] ?? null,
                'next_payment_date' => $paymentData['payment_type'] === 'recurringPayments' ? now()->startOfMonth()->addMonth() : null,
                'metadata' => [
                    'payment_breakdown' => [
                        'original_cost' => $paymentData['original_amount'],
                        'coupon_discount' => $paymentData['discount_amount'] ?? 0,
                        'credit_applied' => $paymentData['used_credit'] ?? 0,
                        'final_amount' => $paymentData['paidAmount']
                    ]
                ]
            ]);

            // Log credit usage if credit was used
            if (isset($paymentData['used_credit']) && $paymentData['used_credit'] > 0) {
                $this->logCreditUsage($paymentData['user_id'], $paymentData['used_credit'], $transaction->id, $paymentData);
            }

            Session::forget('checkout_data');
        }

        return view('guardian.payment-success');
    }

    private function createRecurringSubscription($paymentData)
    {
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $user = \App\Models\User::find($paymentData['user_id']);
            Log::info('Creating subscription for user: ' . $user->id);

            // Get the payment intent to retrieve payment method
            $paymentIntent = PaymentIntent::retrieve($paymentData['payment_intent_id']);
            Log::info('Retrieved PaymentIntent: ' . $paymentIntent->id);

            if (!$paymentIntent->payment_method) {
                Log::error('PaymentIntent has no payment method attached');
                return null;
            }

            // Create or get Stripe customer
            if ($user->stripe_customer_id) {
                $customer = Customer::retrieve($user->stripe_customer_id);
                Log::info('Retrieved existing customer: ' . $customer->id);
            } else {
                $customer = Customer::create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id,
                        'program_id' => $paymentData['program_id']
                    ]
                ]);

                // Save customer ID to user
                $user->update(['stripe_customer_id' => $customer->id]);
                Log::info('Created new customer: ' . $customer->id);
            }

            // Attach payment method to customer
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentIntent->payment_method);
            $paymentMethod->attach(['customer' => $customer->id]);
            Log::info('Attached payment method to customer');

            // Set as default payment method
            Customer::update($customer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentIntent->payment_method
                ]
            ]);
            Log::info('Set default payment method');

            // Create price for monthly payment
            $price = Price::create([
                'unit_amount' => $paymentData['monthly_payment'] * 100,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => 'month',
                    'interval_count' => 1,
                ],
                'product_data' => [
                    'name' => 'Program Monthly Payment - ' . $paymentData['program_id'],
                ],
            ]);
            Log::info('Created price: ' . $price->id);

            // Calculate start date (first day of next month)
            $startDate = now()->startOfMonth()->addMonth()->timestamp;

            // Calculate end date (after specified number of installments)
            // If 6 months starting Feb 1, should end on Aug 1 (not Sep 1)
            $endDate = now()->startOfMonth()->addMonths($paymentData['installment_months'])->timestamp;

            // Create subscription with trial period to prevent immediate charge
            $subscription = Subscription::create([
                'customer' => $customer->id,
                'items' => [[
                    'price' => $price->id,
                ]],
                'trial_end' => $startDate, // Trial until billing starts
                'cancel_at' => $endDate,
                'default_payment_method' => $paymentIntent->payment_method,
                'proration_behavior' => 'none', // Prevent prorations
                'metadata' => [
                    'program_id' => $paymentData['program_id'],
                    'user_id' => $paymentData['user_id'],
                    'total_installments' => $paymentData['installment_months'],
                    'installment_count' => 0
                ]
            ]);

            Log::info('Created Stripe subscription: ' . $subscription->id);





            //store data in program registration table
            // ProgramRegistration::create([
            //     'user_id' => $paymentData['user_id'],
            //     'program_id' => $paymentData['program_id'],
            //     'amount' => $paymentData['down_payment'],
            //     'player_id' => $paymentData['player_ids'][0] ?? null,
            //     'is_paid' => true,
            //     'pending_amount' => $paymentData['monthly_payment'] * $paymentData['installment_months'],
            // ]);


            // foreach ($paymentData['player_ids'] as $playerId) {
            //     PlayerProgram::create([
            //         'player_id' => $playerId,
            //         'program_id' => $paymentData['program_id'],
            //     ]);
            // }

            // Only store in database if Stripe subscription was created successfully
            \App\Models\Subscription::create([
                'user_id' => $paymentData['user_id'],
                'stripe_subscription_id' => $subscription->id,
                'stripe_customer_id' => $customer->id,
                'stripe_price_id' => $price->id,
                'status' => $subscription->status,
                'payment_type' => 'recurring',
                'amount_per_payment' => $paymentData['monthly_payment'],
                'total_amount_due' => $paymentData['monthly_payment'] * $paymentData['installment_months'],
                'initial_amount_paid' => $paymentData['down_payment'],
                'amount_paid'   => $paymentData['down_payment'],
                'currency' => 'usd',
                'interval' => 'month',
                'interval_count' => 1,
                'number_of_payments' => $paymentData['installment_months'],
                'payments_completed' => 0,
                'start_date' => Carbon::createFromTimestamp($startDate),
                'end_date' => Carbon::createFromTimestamp($endDate),
                'next_payment_date' => Carbon::createFromTimestamp($startDate),
                'current_period_start' => Carbon::createFromTimestamp($startDate),
                'current_period_end' => Carbon::createFromTimestamp($startDate + (30 * 24 * 60 * 60)), // Add 30 days
                'cancel_at' => Carbon::createFromTimestamp($endDate),
                'program_ids' => [$paymentData['program_id']],
                'player_ids' => $paymentData['player_ids'] ?? [],
                'customer_email' => $user->email,
                'customer_name' => $user->firstName . ' ' . $user->lastName,
                'default_payment_method' => $paymentIntent->payment_method,
                'metadata' => [
                    'program_id' => $paymentData['program_id'],
                    'total_installments' => $paymentData['installment_months'],
                    'down_payment' => $paymentData['down_payment']
                ]
            ]);

            // Store in recurring_payments table
            DB::table('recurring_payments')->insert([
                'user_id' => $paymentData['user_id'],
                'program_id' => $paymentData['program_id'],
                'payment_type' => 'recurring',
                'player_ids' => json_encode($paymentData['player_ids'] ?? []),
                'email' => $user->email,
                'number_of_payments' => $paymentData['installment_months'],
                'initial_paid_amount' => $paymentData['down_payment'],
                'total_amount_due' => $paymentData['monthly_payment'] * $paymentData['installment_months'],

                'start_date' => Carbon::createFromTimestamp($startDate)->toDateString(),
                'end_date' => Carbon::createFromTimestamp($endDate)->toDateString(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);



            Log::info('Created local subscription record for Stripe subscription: ' . $subscription->id);
            return $subscription;
        } catch (\Exception $e) {
            Log::error('Subscription creation failed: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    private function useGuardianCredit(int $userId, float $amountToUse, $paymentData): bool
    {
        return DB::transaction(function () use ($userId, $amountToUse) {
            $credits = \App\Models\GuardianCredit::where('user_id', $userId)
                ->where('remaining_amount', '>', 0)
                ->orderBy('created_at')
                ->lockForUpdate()
                ->get();

            $remainingToDeduct = $amountToUse;

            foreach ($credits as $credit) {
                if ($remainingToDeduct <= 0) break;

                $available = $credit->remaining_amount;
                $deduct = min($available, $remainingToDeduct);

                $credit->remaining_amount -= $deduct;
                $credit->save();

                $remainingToDeduct -= $deduct;
            }

            if ($remainingToDeduct > 0) {
                throw new \Exception("Not enough credit");
            }

            return true;
        });
    }

    private function logCreditUsage(int $userId, float $totalCreditUsed, int $transactionId, $paymentData): void
    {
        $credits = \App\Models\GuardianCredit::where('user_id', $userId)
            ->where('remaining_amount', '>=', 0)
            ->orderBy('created_at')
            ->get();

        $remainingToLog = $totalCreditUsed;

        foreach ($credits as $credit) {
            if ($remainingToLog <= 0) break;

            $originalAmount = $credit->remaining_amount + min($credit->amount - $credit->remaining_amount, $remainingToLog);
            $usedFromThisCredit = min($originalAmount - $credit->remaining_amount, $remainingToLog);

            if ($usedFromThisCredit > 0) {
                // Skip logging for now due to foreign key constraint mismatch
                // TODO: Fix foreign key constraint to reference program_payment_transactions

                $remainingToLog -= $usedFromThisCredit;
            }
        }
    }

    public function createSplitPaymentTransaction($paymentData, $paymentIntentId)
    {


        $pendingAmount = $paymentData['original_amount'] - $paymentData['paidAmount'];

        if (isset($paymentData['discount_amount']) && $paymentData['discount_amount'] > 0) {
            $pendingAmount -= $paymentData['discount_amount'];
        }

        if (isset($paymentData['credit_used']) && $paymentData['credit_used'] > 0) {
            $pendingAmount -= $paymentData['credit_used'];
        }

        $pendingAmount = max(0, $pendingAmount);

        return \App\Models\GuardianPayment::create([
            'user_id' => $paymentData['user_id'],
            'payment_id' => $paymentIntentId,
            'paid_amount' => $paymentData['paidAmount'],
            'pending_amount' => $pendingAmount,
            'payment_type' => 'split',
            'program_id' => $paymentData['program_id'],
            'player_ids' => json_encode($paymentData['player_ids'] ?? [])
        ]);
    }
}
