<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_product_id',
        'stripe_price_id',
        'status',
        'payment_type',
        'amount_per_payment',
        'total_amount_due',
        'amount_paid',
        'initial_amount_paid',
        'currency',
        'interval',
        'interval_count',
        'number_of_payments',
        'payments_completed',
        'start_date',
        'end_date',
        'current_period_start',
        'current_period_end',
        'next_payment_date',
        'trial_start',
        'trial_end',
        'canceled_at',
        'ended_at',
        'cancel_at_period_end',
        'cancel_at',
        'cancellation_reason',
        'program_ids',
        'player_ids',
        'pending_amounts_by_program',
        'customer_email',
        'customer_name',
        'billing_address',
        'billing_state',
        'billing_city',
        'pause_collection',
        'pause_collection_behavior',
        'default_payment_method',
        'application_fee_percent',
        'metadata',
        'description',
        'discount_details',
        'last_synced_at',
        'sync_errors',
    ];

    protected $casts = [
        'amount_per_payment' => 'decimal:2',
        'total_amount_due' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'initial_amount_paid' => 'decimal:2',
        'application_fee_percent' => 'decimal:2',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'next_payment_date' => 'datetime',
        'trial_start' => 'datetime',
        'trial_end' => 'datetime',
        'canceled_at' => 'datetime',
        'ended_at' => 'datetime',
        'cancel_at' => 'datetime',
        'last_synced_at' => 'datetime',
        'program_ids' => 'array',
        'player_ids' => 'array',
        'pending_amounts_by_program' => 'array',
        'pause_collection_behavior' => 'array',
        'metadata' => 'array',
        'discount_details' => 'array',
        'sync_errors' => 'array',
        'cancel_at_period_end' => 'boolean',
        'pause_collection' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'stripe_subscription_id', 'stripe_subscription_id');
    }

    public function charges(): HasMany
    {
        return $this->hasMany(Charge::class, 'stripe_subscription_id', 'stripe_subscription_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('end_date', '<=', Carbon::now()->addDays($days))
                    ->where('status', 'active');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'past_due');
    }

    public function scopeRecurring($query)
    {
        return $query->where('payment_type', 'recurring');
    }

    public function scopeOutstandingToRecurring($query)
    {
        return $query->where('payment_type', 'outstanding_to_recurring');
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isCanceled(): bool
    {
        return $this->status === 'canceled';
    }

    public function isPastDue(): bool
    {
        return $this->status === 'past_due';
    }

    public function isExpired(): bool
    {
        return $this->end_date && $this->end_date->isPast();
    }

    public function getRemainingPayments(): int
    {
        return max(0, $this->number_of_payments - $this->payments_completed);
    }

    public function getRemainingAmount(): float
    {
        return max(0, $this->total_amount_due - $this->amount_paid);
    }

    public function getProgressPercentage(): float
    {
        if ($this->total_amount_due <= 0) {
            return 100;
        }
        return min(100, ($this->amount_paid / $this->total_amount_due) * 100);
    }

    public function getFormattedAmountPerPayment(): string
    {
        return '$' . number_format($this->amount_per_payment, 2);
    }

    public function getFormattedTotalAmountDue(): string
    {
        return '$' . number_format($this->total_amount_due, 2);
    }

    public function getFormattedAmountPaid(): string
    {
        return '$' . number_format($this->amount_paid, 2);
    }

    public function getFormattedRemainingAmount(): string
    {
        return '$' . number_format($this->getRemainingAmount(), 2);
    }

    public function getNextPaymentDateFormatted(): string
    {
        return $this->next_payment_date ? $this->next_payment_date->format('M j, Y') : 'N/A';
    }

    public function getEndDateFormatted(): string
    {
        return $this->end_date ? $this->end_date->format('M j, Y') : 'N/A';
    }

    public function canBeCanceled(): bool
    {
        return in_array($this->status, ['active', 'trialing', 'past_due']) && !$this->isCanceled();
    }

    public function canBePaused(): bool
    {
        return $this->status === 'active' && !$this->pause_collection;
    }

    public function canBeResumed(): bool
    {
        return $this->pause_collection;
    }

    // Static methods for creating subscriptions
    public static function createFromStripeSubscription($stripeSubscription, $userId, $additionalData = [])
    {
        $subscriptionData = [
            'user_id' => $userId,
            'stripe_subscription_id' => $stripeSubscription->id,
            'stripe_customer_id' => $stripeSubscription->customer,
            'status' => $stripeSubscription->status,
            'amount_per_payment' => ($stripeSubscription->items->data[0]->price->unit_amount ?? 0) / 100,
            'currency' => $stripeSubscription->currency ?? 'usd',
            'interval' => $stripeSubscription->items->data[0]->price->recurring->interval ?? 'month',
            'interval_count' => $stripeSubscription->items->data[0]->price->recurring->interval_count ?? 1,
            'current_period_start' => $stripeSubscription->current_period_start ? Carbon::createFromTimestamp($stripeSubscription->current_period_start) : null,
            'current_period_end' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null,
            'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end ?? false,
            'canceled_at' => $stripeSubscription->canceled_at ? Carbon::createFromTimestamp($stripeSubscription->canceled_at) : null,
            'cancel_at' => $stripeSubscription->cancel_at ? Carbon::createFromTimestamp($stripeSubscription->cancel_at) : null,
            'metadata' => $stripeSubscription->metadata->toArray() ?? [],
            'last_synced_at' => now(),
        ];

        // Merge additional data
        $subscriptionData = array_merge($subscriptionData, $additionalData);

        return static::create($subscriptionData);
    }
}
