@extends('layouts.app')

@section('title', 'program')

@section('content')

    @if (session('success'))
        <div class="alert alert-success text-center mx-auto" id="successSession" role="alert" style="width: 50%;">
            {{ session('success') }}
        </div>
    @endif

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">{{ @$program->sport }}</h1>
    </section>
    <section class="sec program-meta">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
                <h2 class="mt-5 mb-0">
                    {{ @$program->name }}<br />
                    {{ @$program->sub_program }}
                </h2>
            </div>
            <div class="program-box mt-4 mb-5">
                <div class="row">
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Season:</h2>
                            <p class="text-uppercase">{{ @$program->season }}</p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Starts:</h2>
                            <p class="text-uppercase">{{ \Carbon\Carbon::parse(@$program->start_date)->format('F j, Y') }}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Registration Dates:</h2>
                            <p class="text-uppercase">
                                {{ \Carbon\Carbon::parse(@$program->registration_opening_date)->format('F j Y') }}-{{ \Carbon\Carbon::parse(@$program->registration_closing_date)->format('F j Y') }}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Camper Fees REGULAR:</h2>

                            <p class="text-uppercase">
                                @if (isset($amount) && $amount < $program->cost)
                                    <span class="text-decoration-line-through text-muted">
                                        ${{ $program->cost }}
                                    </span>
                                    <span class="text-success font-weight-bold ms-2">
                                        ${{ $amount }} (Early Bird)
                                    </span>
                                @else
                                    <span class="font-weight-bold">
                                        ${{ $amount ?? $program->cost }}
                                    </span>
                                @endif
                            </p>

                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Location:</h2>
                            <p class="text-uppercase">{{ @$program->location }}</p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">ends:</h2>
                            <p class="text-uppercase">{{ \Carbon\Carbon::parse(@$program->end_date)->format('F j, Y') }}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">
                        <div class="box">
                            <h2 class="text-uppercase mb-1">Days:</h2>
                            <p class="text-uppercase">
                                {{ is_array(@$program->frequency_days) ? implode(', ', @$program->frequency_days) : @$program->frequency_days }}
                                <br>

                                {{ date('g:i A', strtotime(@$program->start_time)) }} -
                                {{ date('g:i A', strtotime(@$program->end_time)) }}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3 my-5">


                        @if (@$registrationClosed)

                            <div>
                                <!-- can add something else -->
                            </div>
                        @else
                            <input type="hidden" id="program_id" value={{ @$program->slug }}>
                            @if (isset($isGuest) && $isGuest)
                                <button id="registerButton" type="button" class="cta w-100" data-is-guest="true"
                                    data-program-type="{{ @$program->type }}">
                                    Register
                                </button>
                            @else
                                @if (!@$alreadyRegistered)
                                    <button type="button" class="cta w-100" id="registerProgram">Register</button>
                                @else
                                    <div class="cta w-100 text-center"
                                        style="color: rgb(64, 255, 0); background-color: #d7f8d8; border-color: #d5f5c6;">
                                        Already Registered
                                    </div>
                                @endif
                            @endif
                        @endif
                    </div>
                </div>
            </div>
            <p>{!! @$program->program_description !!}</p>
        </div>
    </section>

    <div class="modal fade" id="playerSelectModal" tabindex="-1" aria-labelledby="playerSelectModalLabel"
        aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <!-- Select Players Form -->
                    <form class="form row" id="playerSelectionForm"
                        action="{{ route('guardian.registerPlayerForProgram') }}" method="POST">
                        @csrf
                        <div id="playerSelectSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="playerSelectErrorMessage" class="alert alert-danger d-none" role="alert"></div>

                        <input type="hidden" name="program_id" value="{{ $program->slug }}">

                        <div class="col-md-12 mb-4">
                            <h5 class="text-uppercase text-center" id="headingChange"style="color:#062E69">Select a Player
                                to Enroll in the Program</h5>
                        </div>


                        <div id="playersList" class="col-md-12 mb-4">

                        </div>

                        <div class="col-md-12 text-center mb-4" id="buttons">
                            <button type="button" class="submit-btn" id="submitPlayerSelection">Submit</button>
                            <button type="button" class="submit-btn mx-2" id="submitTeamSelection"
                                style="display:none">Submit</button>
                            <div class="mt-3">
                                <span id="addNewPlayerButton" class="add-new-player-link"
                                    style="cursor:pointer; color:#0b4499; font-weight:500; text-decoration:underline; background:none; border:none; padding:0; font-size:1rem;">
                                    Add New Player<span style="font-size:1.2em; font-weight:bold; margin-left:0.2em;">?</span>
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>






    <div class="modal fade" id="playerAdd" tabindex="-1" aria-labelledby="playerAddLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">

            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>

                    <form class="form row" id="submitPlayerData">
                        <div id="successMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="errorMessage" class="alert alert-danger d-none" role="alert"></div>
                        @csrf
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerFirstName">Player First Name</label>
                            <input class="form-control" id="playerFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="firstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerLastName">Player Last Name</label>
                            <input class="form-control" id="playerLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="lastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="email">Email(optional)</label>
                            <input class="form-control" id="email" type="text" name="email" />
                            <div class="invalid-feedback" id="emailError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerGender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" name="gender" id="playerGender">
                                    <option value="">Select Gender</option>
                                    <option value="boy">Boy</option>
                                    <option value="girl">Girl</option>
                                </select>

                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>

                            </div>
                            <div class="invalid-feedback d-none" id="genderError"></div>
                        </div>





                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birthdate">Birth date</label>
                            <input class="form-control" id="birthdate" type="date" name="birthDate" />
                            <div class="invalid-feedback" id="birthDateError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gradeEnteringintheFall">Grade (Entering in the
                                Fall)</label>
                            <input class="form-control" id="gradeEnteringintheFall" type="text" name="grade" />
                            <div class="invalid-feedback" id="gradeError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="address">Street</label>
                            <input class="form-control" id="street" type="text" name="street" />
                            <div class="invalid-feedback" id="streetError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="town">Town</label>
                            <input class="form-control" id="town" type="text" name="town" />
                            <div class="invalid-feedback" id="townError"></div>
                        </div>

                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="state">State</label>
                            <input class="form-control" id="state" type="text" name="state" />
                            <div class="invalid-feedback" id="stateError"></div>
                        </div>

                        <div class="col-md-6">
                            <div class="upload-pic d-flex align-items-center">
                                <div class="form-label text-uppercase me-4">Profile Photo (optional)</div>
                                <div class="icon-upload" style="cursor: pointer">
                                    <img src="{{ asset('images/upload-icon.svg') }}" alt="Upload Icon" width="40"
                                        height="50" id="uploadIcon" />
                                </div>
                                <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*"
                                    style="display: none;" />
                                <div class="invalid-feedback" id="profilePhotoError"></div>
                            </div>
                            <div class="file-name mt-2" id="selectedFileName">No file selected</div>
                            <img id="imagePreview" src="" alt="Image Preview" class="mt-2"
                                style="max-width: 100px; max-height: 100px; display: none;" />
                        </div>

                        <!-- if the parent_id of current guardian is null then he is main guardian, if he has parent_id then we will make him the parent but the primary parent will be the main guardian -->
                        @if (@$user->parent_id == null)
                            <input type="hidden" name="parent_id" value="{{ @$user->id }}" id="guardianIdInput">
                            <input type="hidden" name="primary_parent_id" value="{{ @$user->id }}">
                        @else
                            <input type="hidden" name="parent_id" value="{{ @$user->id }}" id="guardianIdInput">
                            <input type="hidden" name="primary_parent_id" value="{{ @$user->primary_parent_id }}">
                        @endif

                        <div class="col-md-6 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="addPlayerButton">Add Player</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>

    @yield('js')
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const registerButton = document.getElementById('registerButton');

            if (registerButton) {
                registerButton.addEventListener('click', function() {
                    const isGuest = this.getAttribute('data-is-guest') === 'true';
                    const programType = this.getAttribute('data-program-type');

                    if (this.disabled) {
                        return;
                    }


                    if (isGuest) {
                        if (programType === 'Team') {
                            window.location.href = "{{ route('coach.signup') }}";
                        } else if (programType === 'Individual' || programType === 'AAU') {
                            window.location.href = "{{ route('guardian.signup') }}";
                        }
                    }
                });
                setTimeout(() => {
                    this.disabled = false;
                }, 1500);
            }
            let registerButtonForLoginUser = document.getElementById('registerProgram');
            registerButtonForLoginUser.addEventListener('click', function(event) {
                event.preventDefault();
                registerButtonForLoginUser.disabled = true;
                let programIdElement = document.getElementById('program_id');
                if (!programIdElement) {
                    console.error('Program ID element not found.');
                    return;
                }

                let programId = programIdElement.value;
                let url = route('selectPlayer', programId);

                // Check if there's an invited player parameter in the URL
                const urlParams = new URLSearchParams(window.location.search);
                const invitedPlayer = urlParams.get('invited_player');
                if (invitedPlayer) {
                    url += `?invited_player=${invitedPlayer}`;

                    // If there's an invited player, we should only allow registration of that specific player
                    // This prevents users from manually changing the URL to register other players
                    console.log('Invited player detected:', invitedPlayer);
                }

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            registerButtonForLoginUser.disabled = false;
                            throw new Error('Network response was not ok ' + response.statusText);

                        }
                        return response.json();
                    })
                    .then(data => {
                        let playersListDiv = document.getElementById('playersList');
                        playersListDiv.innerHTML = '';

                        if (data.success) {
                            registerButtonForLoginUser.disabled = false;
                            if (data.type === 'auto_select_player') {
                                // Auto-select the invited player and register directly
                                playersListDiv.style.display = 'none';

                                let player = data.players[0];
                                let registerUrl = route('guardian.registerPlayerForProgram');
                                let programId = document.getElementById('program_id').value;


                                let requestBody = {
                                    players: [player.id],
                                    program_id: programId
                                };


                                if (invitedPlayer) {
                                    requestBody.invited_player = invitedPlayer;
                                }

                                fetch(registerUrl, {
                                        method: "POST",
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.querySelector(
                                                'meta[name="csrf-token"]').getAttribute(
                                                'content')
                                        },
                                        body: JSON.stringify(requestBody)
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            window.location.href = route(
                                                'guardian.programPayment');
                                        } else {
                                            errorElement = document.getElementById(
                                                'playerSelectErrorMessage');
                                            errorElement.classList.remove('d-none');
                                            errorElement.innerHTML = data.message;
                                            setTimeout(() => {
                                                errorElement.classList.add('d-none');
                                            }, 2000);
                                        }
                                    });
                            } else if (data.type === 'player_list') {

                                if (data.players.length === 1) {
                                    playersListDiv.style.display = 'none';

                                    let player = data.players[0];


                                    let registerUrl = route('guardian.registerPlayerForProgram');

                                    let programId = document.getElementById('program_id').value;


                                    fetch(registerUrl, {
                                            method: "POST",
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRF-TOKEN': document.querySelector(
                                                    'meta[name="csrf-token"]').getAttribute(
                                                    'content')

                                            },
                                            body: JSON.stringify({
                                                players: [player.id],
                                                program_id: programId
                                            })
                                        })

                                        .then(response => response.json())

                                        .then(data => {
                                            if (data.success) {
                                                window.location.href = route(
                                                    'guardian.programPayment');
                                            } else {
                                                console.log('iam here');
                                                errorElement = document.getElementById(
                                                    'playerSelectErrorMessage');
                                                errorElement.classList.remove('d-none');
                                                errorElement.innerHTML = data.message;
                                                setTimeout(() => {
                                                    errorElement.classList.add('d-none');
                                                }, 2000);
                                            }
                                        });
                                } else {
                                    data.players.forEach(player => {
                                        let playerDiv = document.createElement('div');
                                        playerDiv.classList.add('form-check');
                                        playerDiv.innerHTML = `
                                <input class="styled-checkbox" type="checkbox" value="${player.id}" id="player-${player.id}" name="players[]">
                                <label class="form-check-label" for="player-${player.id}">
                                    ${player.firstName} ${player.lastName}
                                </label>
                            `;
                                        playersListDiv.appendChild(playerDiv);


                                    });
                                    new bootstrap.Modal(document.getElementById(
                                        'playerSelectModal')).show();
                                }
                            } else if (data.type === 'team_list') {

                                document.getElementById('addNewPlayerButton').style.display = 'none';
                                document.getElementById('submitPlayerSelection').style.display = "none";
                                document.getElementById('submitTeamSelection').style.display = "block";
                                data.teams.forEach(team => {
                                    let teamDiv = document.createElement('div');
                                    teamDiv.classList.add('form-check');
                                    teamDiv.innerHTML = `
                                    <input class="form-check-input" type="radio" value="${team.id}" id="team-${team.id}" name="team">
                                    <label class="form-check-label" for="team-${team.id}">
                                        ${team.name}
                                    </label>
                                `;
                                    playersListDiv.appendChild(teamDiv);
                                });
                                let submitTeamButton = document.getElementById("submitTeamSelection");

                                new bootstrap.Modal(document
                                    .getElementById(
                                        'playerSelectModal')).show();

                                if (submitTeamButton) {
                                    submitTeamButton.style.display = "block";

                                    document.getElementById('headingChange').textContent =
                                        "SELECT A TEAM TO REGISTER FOR PROGRAM";
                                    submitTeamButton.onclick = function() {
                                        let selectedTeam = document.querySelector(
                                            'input[name="team"]:checked');
                                        if (selectedTeam) {

                                            let programSlug = document.getElementById('program_id')
                                                .value;


                                            let selectedTeamId = selectedTeam.value;

                                            let registerUrl = route('registerTeam', {
                                                programSlug,
                                                selectedTeamId
                                            });
                                            fetch(registerUrl, {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                        'X-CSRF-TOKEN': document.querySelector(
                                                                'meta[name="csrf-token"]')
                                                            .getAttribute(
                                                                'content')
                                                    },
                                                })
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.success && data.type ===
                                                        'team_registration') {
                                                        let modal = document.getElementById(
                                                            'playerSelectModal');
                                                        playersListDiv.innerHTML =
                                                            `<p class="text-success text-center">${data.message}</p>`;
                                                        document.getElementById('headingChange')
                                                            .textContent =
                                                            "Team Registered Successfully";
                                                        document.getElementById(
                                                                'submitTeamSelection').style
                                                            .display = "none";

                                                        new bootstrap.Modal(document
                                                            .getElementById(
                                                                'playerSelectModal')).show();

                                                        setTimeout(() => {
                                                            window.location.href = data
                                                                .redirectUrl;
                                                        }, 2000);

                                                    }
                                                })
                                                .catch(error => console.error(
                                                    'Error registering team:',
                                                    error));
                                        } else {
                                            return;
                                        }
                                    };

                                    // playersListDiv.appendChild(submitTeamButton);
                                }
                            } else if (data.type === 'team_registration') {
                                document.getElementById('addNewPlayerButton').style.display = 'none';
                                document.getElementById('submitPlayerSelection').style.display = "none";
                                document.getElementById('submitTeamSelection').style.display = "none";


                                let successMessage = document.createElement('p');
                                successMessage.classList.add('text-success', 'text-center');
                                successMessage.textContent = data.message;
                                playersListDiv.appendChild(successMessage);
                                new bootstrap.Modal(document.getElementById('playerSelectModal'))
                                    .show();

                                setTimeout(() => {
                                    window.location.reload();
                                }, 3000);
                            }
                        } else {
                            playersListDiv.innerHTML = `
                                    <p style="text-align: center;">Message: ${data.message}</p>
                                `;

                            registerButtonForLoginUser.disabled = "false";

                            // document.getElementById('submitPlayerSelection').style.display = "none";
                            // document.getElementById('addNewPlayerButton').style.display = 'none';
                            // document.getElementById('headingChange').textContent =
                            //     "ALREADY REGISTERED!!";

                            if (data.message.includes('requires a coach')) {
                                document.getElementById('addNewPlayerButton').style.display = 'none';
                                document.getElementById('submitPlayerSelection').style.display = "none";
                                document.getElementById('headingChange').textContent =
                                    "REQUIRES COACH SIGN UP";
                                let signupLink = document.createElement('a');
                                signupLink.href = route('coach.signup');
                                signupLink.textContent = 'Sign up as a coach';
                                signupLink.classList.add('cta', 'text-center', 'd-block');
                                signupLink.id = "signupAsCoach";
                                playersListDiv.appendChild(signupLink);

                            }

                            if (data.message.includes('sign up as a Guardian')) {
                                document.getElementById('addNewPlayerButton').style.display = 'none';
                                document.getElementById('headingChange').textContent =
                                    "REQUIRES GUARDIAN SIGN UP";
                                document.getElementById('submitPlayerSelection').style.display = "none";
                                let signupLink = document.createElement('a');
                                signupLink.href = route('guardian.signup');
                                signupLink.textContent = 'Sign up as a guardian';
                                signupLink.classList.add('cta', 'text-center', 'd-block');
                                signupLink.id = "signupAsGuardian";
                                playersListDiv.appendChild(signupLink);

                            }

                            if (data.message.includes('All of your player')) {
                                document.getElementById('addNewPlayerButton').style.display = "block";
                                document.getElementById('headingChange').textContent = "ADD NEW PLAYER";
                                document.getElementById('submitPlayerSelection').style.display = "none";

                            }

                            if (data.message.includes('Switch to Coach')) {
                                document.getElementById('addNewPlayerButton').style.display = "none";
                                document.getElementById('headingChange').textContent =
                                    "SWITCH TO COACH MODE";
                                document.getElementById('submitPlayerSelection').style.display = "none";


                            }

                            if (data.message.includes('Switch to Guardian')) {
                                document.getElementById('addNewPlayerButton').style.display = "none";
                                document.getElementById('headingChange').textContent =
                                    "SWITCH TO GUARDIAN MODE";
                                document.getElementById('submitPlayerSelection').style.display = "none";


                            }

                            if (data.message.includes('All of your teams')) {
                                document.getElementById('addNewPlayerButton').style.display = "none";
                                document.getElementById('headingChange').textContent =
                                    "ALL TEAMS ARE REGISTERED";
                                document.getElementById('submitPlayerSelection').style.display = "none";
                            }

                            if (data.type === 'admin_invitation') {
                                document.getElementById('addNewPlayerButton').style.display = "none";
                                document.getElementById('headingChange').textContent =
                                    "ADMIN INVITATION PENDING";
                                document.getElementById('submitPlayerSelection').style.display = "none";

                                // Show invitation details
                                let invitationDetails = '<div class="text-center">';
                                invitationDetails += '<p class="mb-3">' + data.message + '</p>';
                                if (data.invited_players && data.invited_players.length > 0) {
                                    invitationDetails +=
                                        '<div class="mb-3"><strong>Invited Players:</strong></div>';
                                    data.invited_players.forEach(player => {
                                        invitationDetails +=
                                            `<div class="mb-2 p-2 border rounded">${player.firstName} ${player.lastName}</div>`;
                                    });
                                }
                                invitationDetails += '<div class="mt-3"><a href="' + route(
                                        'guardian.dashboard') +
                                    '" class="cta">Go to Dashboard</a></div>';
                                invitationDetails += '</div>';
                                playersListDiv.innerHTML = invitationDetails;
                            }

                            new bootstrap.Modal(document.getElementById('playerSelectModal'))
                                .show();
                        }


                    });
            });


            document.getElementById('submitPlayerSelection').addEventListener('click', function(event) {
                event.preventDefault();

                let checkboxes = document.querySelectorAll('#playersList input[type="checkbox"]:checked');
                if (checkboxes.length === 0) {
                    return;
                }


                let selectedPlayerIds = Array.from(checkboxes).map(checkbox => checkbox.value);


                let formData = new FormData();
                formData.append('program_id', document.querySelector('input[name="program_id"]').value);
                selectedPlayerIds.forEach(id => formData.append('players[]', id));

                let url = route('guardian.registerPlayerForProgram');
                fetch(url, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = route('guardian.programPayment');
                        } else {
                            document.getElementById('playerSelectErrorMessage').textContent =
                                'Failed to submit selection.';
                            document.getElementById('playerSelectErrorMessage').classList.remove(
                                'd-none');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('playerSelectErrorMessage').textContent =
                            'An error occurred. Please try again.';
                        document.getElementById('playerSelectErrorMessage').classList.remove('d-none');
                    });
            });



            document.getElementById('addNewPlayerButton').addEventListener('click', function() {
                $('#playerSelectModal').modal('hide');
                $('#playerAdd').modal('show');
            });
            document
                .getElementById("uploadIcon")
                .addEventListener("click", function() {
                    document.getElementById("profilePhoto").click();
                });

            document
                .getElementById("profilePhoto")
                .addEventListener("change", function() {
                    const fileInput = document.getElementById("profilePhoto");
                    const fileNameDisplay = document.getElementById("selectedFileName");
                    const imagePreview = document.getElementById("imagePreview");
                    const file = fileInput.files[0];

                    if (file) {
                        fileNameDisplay.textContent = file.name;

                        const reader = new FileReader();
                        reader.onload = function(e) {
                            imagePreview.src = e.target.result;
                            imagePreview.style.display = "block";
                        };
                        reader.readAsDataURL(file);
                    } else {
                        fileNameDisplay.textContent = "No file selected";
                        imagePreview.style.display = "none";
                    }
                });


            document
                .getElementById("addPlayerButton")
                .addEventListener("click", async function(event) {
                    event.preventDefault();

                    const form = document.getElementById("submitPlayerData");
                    const formData = new FormData(form);

                    const successMessage = document.getElementById("successMessage");
                    const errorMessage = document.getElementById("errorMessage");
                    form.querySelectorAll(".is-invalid").forEach((el) =>
                        el.classList.remove("is-invalid")
                    );
                    form.querySelectorAll(".invalid-feedback").forEach(
                        (el) => (el.textContent = "")
                    );

                    try {

                        let programIdElement = document.getElementById('program_id');
                        if (!programIdElement) {
                            console.error('Program ID element not found.');
                            return;
                        }

                        let programId = programIdElement.value;
                        let url = route("guardian.player.addToProgram", programId);
                        const response = await fetch(url, {
                            method: "POST",
                            headers: {
                                "X-CSRF-TOKEN": "{{ csrf_token() }}",
                                "X-Requested-With": "XMLHttpRequest",
                            },
                            body: formData,
                        });

                        const data = await response.json();

                        if (data.success) {
                            form.reset();

                            const imagePreview =
                                document.getElementById("imagePreview");
                            imagePreview.src = "";
                            imagePreview.style.display = "none";
                            const fileNameDisplay =
                                document.getElementById("selectedFileName");
                            fileNameDisplay.textContent = "No file selected";

                            successMessage.textContent = data.message;
                            successMessage.classList.remove("d-none");
                            errorMessage.classList.add("d-none");
                            form.reset();
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        } else {
                            if (data.errors) {
                                for (const [key, value] of Object.entries(
                                        data.errors
                                    )) {
                                    const inputElement = document.querySelector(
                                        `[name="${key}"]`
                                    );
                                    const errorElement = document.querySelector(
                                        `#${key}Error`
                                    );
                                    if (inputElement) {
                                        inputElement.classList.add("is-invalid");
                                    }
                                    if (errorElement) {
                                        errorElement.textContent = value[0];
                                        errorElement.classList.remove("d-none");
                                        errorElement.style.display = "block";
                                    }
                                }
                            } else {
                                errorMessage.textContent = data.message;
                                errorMessage.classList.remove("d-none");
                            }
                        }
                    } catch (error) {
                        errorMessage.textContent =
                            "An error occurred. Please try again.";
                        errorMessage.classList.remove("d-none");
                        successMessage.classList.add("d-none");
                    }
                });

            let successSession = document.getElementById('successSession');
            if (successSession) {

                setTimeout(() => {
                    successSession.style.display = 'none';
                }, 2000);
            }
        });
    </script>
@endsection

@section('css')
    <style>
        .add-new-player-link:hover {
            color: #09316b;
            text-decoration: underline wavy;
        }
    </style>
@endsection
