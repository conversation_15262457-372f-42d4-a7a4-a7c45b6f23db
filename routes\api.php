<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\PaymentController;

Route::get('/programs', [ApiController::class, 'allPrograms'])->name('allPrograms');

Route::post('/recurring/payments/details', [PaymentController::class, 'storeRecurringPayments']);

Route::post('/subscription/created', [PaymentController::class, 'subscriptionCreated']);
