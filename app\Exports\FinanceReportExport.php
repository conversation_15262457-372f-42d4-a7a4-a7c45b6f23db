<?php

namespace App\Exports;

use App\Models\ProgramRegistration;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Collection;

class FinanceReportExport implements FromCollection, WithHeadings
{
    public function collection(): Collection
    {
        return ProgramRegistration::with(['program', 'player'])
            ->get()
            ->map(function ($registration) {
                return [
                    'Program Name'   => $registration->program->name ?? 'N/A',
                    'Player Name'    => trim(optional($registration->player)->firstName . ' ' . optional($registration->player)->lastName),
                    'Player Email'   => optional($registration->player)->email,
                    'Total Amount'   => $registration->amount,
                    'Pending Amount' => $registration->pending_amount,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'Program Name',
            'Player Name',
            'Player Email',
            'Total Amount',
            'Pending Amount',
        ];
    }
}
