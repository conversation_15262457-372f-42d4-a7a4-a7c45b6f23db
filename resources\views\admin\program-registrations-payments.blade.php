@extends('layouts.app')
@section('title', 'Program Registrations & Payments')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Program Registrations & Payments</h1>
    </section>

    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="{{ route('admin.dashboard') }}">Back to Dashboard</a>
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>

    <section class="sec program-table">
        <div class="container">
            @if($errors->any())
                <div class="alert alert-danger">
                    {{ $errors->first() }}
                </div>
            @endif

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <h5 class="card-title">Total Programs</h5>
                            <h3 class="card-value">{{ $programsData->count() }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5 class="card-title">Total Registrations</h5>
                            <h3 class="card-value">{{ $programsData->sum('total_registrations') }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="card-title">Paid Registrations</h5>
                            <h3 class="card-value">{{ $programsData->sum('paid_registrations') }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="card-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h5 class="card-title">Pending Payments</h5>
                            <h3 class="card-value">{{ $programsData->sum('pending_registrations') }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Programs Table -->
            <div class="table-responsive">
                <table class="table custom-table program-management-table">
                    <thead>
                        <tr>
                            <th class="py-4">Program Name</th>
                            <th class="py-4">Type</th>
                            <th class="py-4">Sport</th>
                            <th class="py-4">Cost</th>
                            <th class="py-4">Dates</th>
                            <th class="py-4">Registrations</th>
                            <th class="py-4">Payment Status</th>
                            <th class="py-4">Revenue</th>
                            <th class="py-4">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($programsData as $program)
                            <tr>
                                <td class="py-3">
                                    <strong>{{ $program['name'] }}</strong>
                                </td>
                                <td class="py-3">
                                    <span class="badge custom-badge">{{ ucfirst($program['type']) }}</span>
                                </td>
                                <td class="py-3">{{ ucfirst($program['sport']) }}</td>
                                <td class="py-3">
                                    <span class="cost-text">${{ number_format($program['cost'], 2) }}</span>
                                </td>
                                <td class="py-3">
                                    <small class="date-text">
                                        {{ \Carbon\Carbon::parse($program['start_date'])->format('M j, Y') }} -
                                        {{ \Carbon\Carbon::parse($program['end_date'])->format('M j, Y') }}
                                    </small>
                                </td>
                                <td class="py-3">
                                    <div class="d-flex flex-column">
                                        <span class="total-reg"><strong>{{ $program['total_registrations'] }}</strong> Total</span>
                                        <small class="paid-reg">{{ $program['paid_registrations'] }} Paid</small>
                                        <small class="pending-reg">{{ $program['pending_registrations'] }} Pending</small>
                                    </div>
                                </td>
                                <td class="py-3">
                                    @php
                                        $paidPercentage = $program['total_registrations'] > 0
                                            ? ($program['paid_registrations'] / $program['total_registrations']) * 100
                                            : 0;
                                    @endphp
                                    <div class="progress custom-progress" style="height: 20px;">
                                        <div class="progress-bar custom-progress-bar" role="progressbar"
                                             style="width: {{ $paidPercentage }}%">
                                            {{ round($paidPercentage) }}%
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    @if(isset($program['payment_stats']['total_revenue']))
                                        <div class="d-flex flex-column">
                                            <strong class="revenue-text">${{ number_format($program['payment_stats']['total_revenue'], 2) }}</strong>
                                            @if(isset($program['payment_stats']['total_credit_used']) && $program['payment_stats']['total_credit_used'] > 0)
                                                <small class="credit-text">
                                                    Credit: ${{ number_format($program['payment_stats']['total_credit_used'], 2) }}
                                                </small>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-muted">No payments</span>
                                    @endif
                                </td>
                                <td class="py-3">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.program.payment.details', $program['id']) }}"
                                           class="btn btn-sm btn-primary custom-btn-primary">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                        <a href="{{ route('admin.export.program.payment.data', $program['id']) }}"
                                           class="btn btn-sm btn-secondary custom-btn-secondary">
                                            <i class="fas fa-download"></i> Export
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No programs found.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Quick Stats Section -->
            @if($programsData->count() > 0)
                <div class="row mt-5">
                    <div class="col-12">
                        <h4 class="section-title">Payment Method Breakdown</h4>
                        <div class="row">
                            @php
                                $totalPaymentStats = [
                                    'card_payments' => 0,
                                    'credit_payments' => 0,
                                    'mixed_payments' => 0,
                                    'external_payments' => 0,
                                ];

                                foreach($programsData as $program) {
                                    if(isset($program['payment_stats']['payment_methods'])) {
                                        foreach($program['payment_stats']['payment_methods'] as $method => $count) {
                                            if(isset($totalPaymentStats[$method . '_payments'])) {
                                                $totalPaymentStats[$method . '_payments'] += $count;
                                            }
                                        }
                                    }
                                }
                            @endphp

                            <div class="col-md-3">
                                <div class="card payment-method-card">
                                    <div class="card-body text-center">
                                        <div class="payment-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <h5>Card Payments</h5>
                                        <h3 class="payment-count">{{ $totalPaymentStats['card_payments'] }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card payment-method-card">
                                    <div class="card-body text-center">
                                        <div class="payment-icon">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <h5>Credit Payments</h5>
                                        <h3 class="payment-count">{{ $totalPaymentStats['credit_payments'] }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card payment-method-card">
                                    <div class="card-body text-center">
                                        <div class="payment-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <h5>Mixed Payments</h5>
                                        <h3 class="payment-count">{{ $totalPaymentStats['mixed_payments'] }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card payment-method-card">
                                    <div class="card-body text-center">
                                        <div class="payment-icon">
                                            <i class="fas fa-external-link-alt"></i>
                                        </div>
                                        <h5>External Links</h5>
                                        <h3 class="payment-count">{{ $totalPaymentStats['external_payments'] }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <style>
        :root {
            --primary-color: #0b4499;
            --secondary-color: #c1b05c;
            --background-color: #fff;
            --text-color: #333;
            --light-gray: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        /* Summary Cards */
        .summary-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(11, 68, 153, 0.1);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            background: var(--background-color);
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(11, 68, 153, 0.15);
        }

        .summary-card .card-body {
            padding: 1.5rem;
            position: relative;
        }

        .summary-card .card-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 2rem;
            color: var(--primary-color);
            opacity: 0.3;
        }

        .summary-card .card-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #666;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .summary-card .card-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
            line-height: 1;
        }

        /* Main Table */
        .custom-table {
            background: var(--background-color);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(11, 68, 153, 0.1);
            border: 1px solid var(--border-color);
        }

        .custom-table thead {
            background: var(--primary-color);
            color: white;
        }

        .custom-table thead th {
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            padding: 1rem;
        }

        .custom-table tbody tr {
            transition: all 0.2s ease;
        }

        .custom-table tbody tr:hover {
            background-color: rgba(11, 68, 153, 0.05);
        }

        .custom-table tbody td {
            border-top: 1px solid var(--border-color);
            vertical-align: middle;
            font-size: 0.9rem;
        }

        /* Badge Styling */
        .custom-badge {
            background: var(--secondary-color);
            color: var(--background-color);
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.35em 0.8em;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        /* Text Styling */
        .cost-text {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .date-text {
            color: #666;
            font-size: 0.8rem;
        }

        .total-reg {
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .paid-reg {
            color: var(--success-color);
            font-size: 0.8rem;
        }

        .pending-reg {
            color: var(--warning-color);
            font-size: 0.8rem;
        }

        .revenue-text {
            color: var(--primary-color);
            font-size: 1rem;
        }

        .credit-text {
            color: var(--secondary-color);
            font-size: 0.8rem;
        }

        /* Progress Bar */
        .custom-progress {
            background-color: rgba(11, 68, 153, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .custom-progress-bar {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            text-align: center;
            line-height: 20px;
            transition: width 0.3s ease;
        }

        /* Buttons */
        .custom-btn-primary {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .custom-btn-primary:hover {
            background: #083666;
            border-color: #083666;
            transform: translateY(-1px);
        }

        .custom-btn-secondary {
            background: var(--secondary-color);
            border: 1px solid var(--secondary-color);
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .custom-btn-secondary:hover {
            background: #a89849;
            border-color: #a89849;
            transform: translateY(-1px);
        }

        .btn-group .btn {
            margin-right: 5px;
        }

        /* Payment Method Cards */
        .payment-method-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(11, 68, 153, 0.08);
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: var(--background-color);
        }

        .payment-method-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(11, 68, 153, 0.12);
        }

        .payment-method-card .card-body {
            padding: 1.5rem;
        }

        .payment-method-card .payment-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .payment-method-card h5 {
            font-size: 0.9rem;
            font-weight: 600;
            color: #666;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .payment-method-card .payment-count {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
            line-height: 1;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .summary-card .card-value {
                font-size: 2rem;
            }

            .payment-method-card .payment-count {
                font-size: 1.5rem;
            }

            .custom-table {
                font-size: 0.8rem;
            }

            .btn-group {
                flex-direction: column;
            }

            .btn-group .btn {
                margin-bottom: 5px;
                margin-right: 0;
            }
        }
    </style>

@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Add loading states for export buttons
            $('.custom-btn-secondary').click(function() {
                const button = $(this);
                const originalText = button.html();
                button.html('<i class="fas fa-spinner fa-spin"></i> Exporting...');

                // Reset button after 3 seconds (adjust as needed)
                setTimeout(function() {
                    button.html(originalText);
                }, 3000);
            });

            // Add subtle animations to progress bars
            $('.custom-progress-bar').each(function() {
                const width = $(this).css('width');
                $(this).css('width', '0%');
                $(this).animate({
                    width: width
                }, 1000);
            });
        });
    </script>
@endsection
