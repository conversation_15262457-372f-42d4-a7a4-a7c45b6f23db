<?php

namespace App\Events;

use App\Models\Program;
use App\Models\Team;
use App\Models\TeamProgram;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PlayerInvited
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $player;
    public $emails;
    public $coach;

    public $program;
    public $team;

    public function __construct(User $player, array $emails, User $coach, Program|TeamProgram $program, Team $team)
    {
        $this->player = $player;
        $this->emails = $emails;
        $this->coach = $coach;
        $this->program = $program instanceof TeamProgram ? $program->program : $program;
        $this->team = $team;
    }
}
