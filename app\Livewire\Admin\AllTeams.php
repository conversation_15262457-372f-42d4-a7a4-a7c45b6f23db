<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Team;
use App\Models\PlayerInvitation;

class AllTeams extends Component
{
    use WithPagination;

    public $filter = '';
    public $search = '';
    protected $updatesQueryString = ['filter', 'search', 'page'];

    public function updatingFilter() { $this->resetPage(); }
    public function updatingSearch() { $this->resetPage(); }

    public function render()
    {
        $query = Team::with([
            'coaches',
            'programs' => function($q) {
                $q->where('registration_closing_date', '>', now());
            }
        ]);

        // Filter
        if ($this->filter) {
            if ($this->filter === 'Coach') {
                $query->whereHas('coaches', function($q) {
                    $q->where('team_coach.is_primary', 1);
                });
            } elseif ($this->filter === 'Assistant Coach') {
                $query->whereHas('coaches', function($q) {
                    $q->where('team_coach.is_primary', 0);
                });
            } elseif ($this->filter === 'Program') {
                $query->whereHas('programs', function($q) {
                    $q->where('registration_closing_date', '>', now());
                });
            }
        }

        // Search
        if ($this->search) {
            $search = $this->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%$search%")
                  ->orWhereHas('coaches', function($q2) use ($search) {
                      $q2->where('firstName', 'like', "%$search%")
                          ->orWhere('lastName', 'like', "%$search%")
                          ->orWhere('email', 'like', "%$search%")
                          ;
                  })
                  ->orWhereHas('programs', function($q2) use ($search) {
                      $q2->where('name', 'like', "%$search%")
                          ->orWhere('slug', 'like', "%$search%")
                          ;
                  });
            });
        }

        $teams = $query->paginate(10);

        // Attach current_program, primary_coach, assistant_coach, and current_players for each team
        foreach ($teams as $team) {
            $team->current_program = $team->programs->first();
            $team->primary_coach = $team->coaches->where('pivot.is_primary', 1)->first();
            $team->assistant_coach = $team->coaches->where('pivot.is_primary', 0)->first();
            if ($team->current_program) {
                $team->current_players = PlayerInvitation::with('player')
                    ->where('team_id', $team->id)
                    ->where('program_id', $team->current_program->id)
                    ->get();
            } else {
                $team->current_players = collect();
            }
        }

        return view('livewire.admin.all-teams', [
            'teams' => $teams
        ]);
    }
}
