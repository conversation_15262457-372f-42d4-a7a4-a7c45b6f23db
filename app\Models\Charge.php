<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Charge extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stripe_charge_id',
        'stripe_payment_intent_id',
        'amount',
        'currency',
        'status',
        'payment_type',
        'program_ids',
        'player_ids',
        'metadata',
        'charge_date',
        'paid_at',
        'refunded_amount',
        'refund_status',
        'description',
        'receipt_url',
        'receipt_number',
    ];

    protected $casts = [
        'program_ids' => 'array',
        'player_ids' => 'array',
        'metadata' => 'array',
        'charge_date' => 'datetime',
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function refunds(): MorphMany
    {
        return $this->morphMany(Refund::class, 'refundable');
    }

    public function getProgramsAttribute()
    {
        return Program::whereIn('id', $this->program_ids ?? [])->get();
    }

    public function getPlayersAttribute()
    {
        return User::whereIn('id', $this->player_ids ?? [])->get();
    }

    public function getRemainingAmountAttribute()
    {
        return $this->amount - $this->refunded_amount;
    }

    public function canBeRefunded()
    {
        return $this->status === 'succeeded' && $this->remaining_amount > 0;
    }

    public function getRefundableAmountAttribute()
    {
        return $this->remaining_amount;
    }

    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getFormattedRefundedAmountAttribute()
    {
        return '$' . number_format($this->refunded_amount, 2);
    }

    public function getFormattedRemainingAmountAttribute()
    {
        return '$' . number_format($this->remaining_amount, 2);
    }
}
