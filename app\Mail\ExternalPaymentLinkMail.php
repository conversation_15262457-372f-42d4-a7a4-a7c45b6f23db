<?php

namespace App\Mail;

use App\Models\ExternalPaymentLink;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ExternalPaymentLinkMail extends Mailable
{
    use Queueable, SerializesModels;

    public ExternalPaymentLink $paymentLink;

    /**
     * Create a new message instance.
     */
    public function __construct(ExternalPaymentLink $paymentLink)
    {
        $this->paymentLink = $paymentLink;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Payment Request - ' . $this->paymentLink->program->name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.external-payment-link',
            with: [
                'paymentLink' => $this->paymentLink,
                'program' => $this->paymentLink->program,
                'requestingUser' => $this->paymentLink->requestingUser,
                'playerNames' => $this->paymentLink->player_names,
                'amount' => $this->paymentLink->amount_to_pay,
                'paymentUrl' => $this->paymentLink->getPaymentUrl(),
                'expiresAt' => $this->paymentLink->expires_at,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
