<?php

namespace App\Http\Controllers;

use App\Mail\SendInvitationToAdditionalGuardian;
use App\Models\PlayerInvitation;
use App\Models\GuardianInvitationByCoach;
use App\Mail\GuardianAcceptedInvitation;
use App\Models\GuardianPayment;
use App\Models\Program;
use App\Models\Team;
use App\Models\User;
use App\Models\PlayerProgram;
use App\Models\TeamPlayer;
use App\Models\TeamProgram;
use App\Models\UserGuardian;
use App\Models\Subscription;
use App\Notifications\Inivitation;
use App\Services\PaymentTransactionService;
use DateTime;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\Role;
use Illuminate\Support\Facades\Crypt;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;
use App\Models\ProgramRegistration;
use App\Models\TeamCoach;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class GuardianController extends Controller
{
    /**
     * Search guardians by email, first name, or last name (for modal search).
     * Returns JSON: { success: true, guardians: [ ... ] }
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        if (!$query) {
            return response()->json(['success' => false, 'guardians' => [], 'message' => 'No query provided.']);
        }

        $currentUserId = Auth::id();

        $guardians = User::whereHas('roles', function ($q) {
            $q->where('name', 'guardian');
        })
            ->where('id', '!=', $currentUserId)
            ->where(function ($q) use ($query) {
                $q->where('email', 'like', "%$query%")
                    ->orWhere('firstName', 'like', "%$query%")
                    ->orWhere('lastName', 'like', "%$query%")
                ;
            })
            ->limit(10)
            ->get(['id', 'firstName', 'lastName', 'email', 'parent_id', 'primary_parent_id']);

        return response()->json([
            'success' => true,
            'guardians' => $guardians
        ]);
    }
    protected $paymentService;

    public function __construct(PaymentTransactionService $paymentService)
    {
        $this->paymentService = $paymentService;
    }


    // this function is bit redundant and not so easy to read  so to fix this i have written other code  below this function
    // which is more readable.
    // Also comments are added to that code which makes that code more readable,in any case
    // that doesn't work then we can use this code.(but it is not going to happen as the both are same (:)


    // public function signup(Request $request, $token=null)
    // {
    //     if($request->isMethod('post')){
    //         $request->validate([
    //             'firstName' => 'required|string',
    //             'lastName' => 'required|string',
    //             'email' => 'required|email',
    //             'password' => 'required|min:6',
    //             'mobile_number' => [
    //                 'required',
    //                 'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
    //         ],
    //         ]);

    //          $isCoach = $request->has('makeCoach');

    //          if($isCoach){

    //      $request->validate([
    //         'town'=>'required|string|max:255',
    //         'team'=>'required|string|max:255'
    //      ]);
    //     }

    //         $isInvited = User::where('email', $request->email)
    //         ->where('is_joined', false)
    //         ->whereHas('roles', function ($q) {
    //             $q->where('name', 'guardian');
    //         })
    //         ->first();

    //         if ($isInvited) {

    //             $isInvited->update([
    //                 'password' => Hash::make($request->password),
    //                  'mobile_number'=>$request->mobile_number,
    //                 'is_joined' => true,
    //                 'current_role'=>'guardian'

    //             ]);

    //              if($isCoach){

    //             $isInvited->town=$request->town;
    //             $isInvited->teamName=$request->team;
    //             $isInvited->is_coach=true;

    //             $isInvited->roles()->attach(Role::COACH);
    //             $isInvited->save();

    //             $team=Team::create([

    //                 'user_id'=>$isInvited->id,
    //                 'name'=> $request->team,

    //    ]);

    //          TeamCoach::create([
    //             'coach_id'=>$isInvited->id,
    //             'team_id'=>$team->id,
    //             'is_primary'=>true,
    //          ]);
    //            }
    //             return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');
    //         }


    //         $isInvitedByCoach=GuardianInvitationByCoach::where('guardian_email', $request->email)
    //         ->where('status', 'pending')
    //         ->first();

    //         if($isInvitedByCoach){

    //              $request->validate([
    //             'firstName' => 'required|string',
    //             'lastName' => 'required|string',
    //              'email'=>'required|unique:users,email',
    //             'password' => 'required|min:6',
    //               'mobile_number' => [
    //               'required',
    //                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
    //     ],
    //         ]);


    //            $isInvitedByCoach->update([
    //             'status' => 'accepted',
    //         ]);
    //           $user = User::create([
    //             'firstName' => $request->firstName,
    //             'lastName' => $request->lastName,
    //             'email' => $request->email,
    //             'password' => Hash::make($request->password),
    //             'mobile_number'=>$request->mobile_number,
    //             'is_joined'=>true,
    //             'current_role'=>'guardian',
    //         ]);
    //         $user->roles()->attach(Role::GUARDIAN);

    //          $coach = User::find($isInvitedByCoach->coach_id);
    //     if ($coach) {
    //         Mail::to($coach->email)->send(new GuardianAcceptedInvitation($isInvitedByCoach));
    //     }

    //      if($isCoach){

    //             $user->town=$request->town;
    //             $user->teamName=$request->team;
    //             $user->is_coach=true;

    //             $user->roles()->attach(Role::COACH);
    //             $user->save();

    //             $team=Team::create([

    //                 'user_id'=>$user->id,
    //                 'name'=> $request->team,

    //    ]);

    //          TeamCoach::create([
    //             'coach_id'=>$user->id,
    //             'team_id'=>$team->id,
    //             'is_primary'=>true,
    //          ]);
    //            }

    //         return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');

    //         }


    //         if(!$isInvited){
    //             $request->validate([
    //             'firstName' => 'required|string',
    //             'lastName' => 'required|string',
    //              'email'=>'required|unique:users,email',
    //             'password' => 'required|min:6',
    //               'mobile_number' => [
    //               'required',
    //                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
    //     ],
    //         ]);

    //         $user = User::create([
    //             'firstName' => $request->firstName,
    //             'lastName' => $request->lastName,
    //             'email' => $request->email,
    //             'password' => Hash::make($request->password),
    //             'mobile_number'=>$request->mobile_number,
    //             'is_joined'=>true,
    //             'current_role'=>'guardian'
    //         ]);
    //         $user->roles()->attach(Role::GUARDIAN);

    //          if($isCoach){

    //             $user->town=$request->town;
    //             $user->teamName=$request->team;
    //             $user->is_coach=true;

    //             $user->roles()->attach(Role::COACH);
    //             $user->save();

    //             $team=Team::create([

    //                 'user_id'=>$user->id,
    //                 'name'=> $request->team,

    //    ]);
    //         TeamCoach::create([
    //             'coach_id'=>$user->id,
    //             'team_id'=>$team->id,
    //             'is_primary'=>true,
    //         ]);
    //            }
    //         return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');
    //     }
    // }



    //     if($token){
    //         $id = Crypt::decryptString($token);
    //         $additionalGuardian=User::where("id",$id)->first();
    //          return view('guardian.signup', compact('additionalGuardian'));
    //     }


    //     if(!$token){
    //         return view('guardian.signup');
    //     }
    // }



    public function signup(Request $request, $token = null)
    {
        if ($request->isMethod('post')) {
            $isCoach = $request->has('makeCoach');
            $isInvited = User::where('email', $request->email)
                ->where('is_joined', false)
                ->whereHas('roles', function ($q) {
                    $q->where('name', 'guardian');
                })
                ->first();
            if ($isInvited) {

                $this->validateRequestforExistingInvitation($request, $isCoach);
                if ($user = $this->handleExistingInvitation($request, $isCoach)) {
                    return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');
                }
            } else {



                $this->validateRequest($request);
            }



            if ($isCoach) {
                $this->validateCoachRequest($request);
            }



            if ($user = $this->handleCoachInvitation($request, $isCoach)) {
                return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');
            }

            $user = $this->createNewUser($request, $isCoach);
            return redirect()->route('loginPage')->with('success', 'Guardian account created successfully.');
        }

        return $this->handleSignupView($token);
    }

    /**
     * Validate general user input.
     */
    private function validateRequest(Request $request)
    {
        $request->validate([
            'firstName' => 'required|string',
            'lastName' => 'required|string',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
            'mobile_number' => [
                'required',
                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
            ],
        ]);
    }

    /**
     * Validate existing invitation input.
     */
    private function validateRequestforExistingInvitation(Request $request, bool $isCoach)
    {
        $request->validate([
            'firstName' => 'required|string',
            'lastName' => 'required|string',
            'password' => 'required|min:6',
            'email' => 'required|email',
            'mobile_number' => [
                'required',
                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
            ],
        ]);
    }

    /**
     * Validate coach-specific input.
     */
    private function validateCoachRequest(Request $request)
    {
        $request->validate([
            'town' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);
    }

    /**
     * Handle existing invitation for a guardian.
     */
    private function handleExistingInvitation(Request $request, bool $isCoach)
    {
        $user = User::where('email', $request->email)
            ->where('is_joined', false)
            ->whereHas('roles', function ($q) {
                $q->where('name', 'guardian');
            })
            ->first();

        if ($user) {
            $user->update([
                'password' => Hash::make($request->password),
                'mobile_number' => $request->mobile_number,
                'is_joined' => true,
                'current_role' => 'guardian'
            ]);

            if ($isCoach) {
                $this->assignCoachRole($user, $request);
            }
        }

        return $user;
    }

    /**
     * Handle invitation by a coach.
     */
    private function handleCoachInvitation(Request $request, bool $isCoach)
    {
        $invitation = GuardianInvitationByCoach::where('guardian_email', $request->email)
            ->where('status', 'pending')
            ->first();

        if ($invitation) {
            $invitation->update(['status' => 'accepted']);

            $user = $this->createUser($request);
            $user->roles()->attach(Role::GUARDIAN);

            $coach = User::find($invitation->coach_id);
            if ($coach) {
                Mail::to($coach->email)->send(new GuardianAcceptedInvitation($invitation));
            }

            if ($isCoach) {
                $this->assignCoachRole($user, $request);
            }

            return $user;
        }

        return null;
    }

    /**
     * Create a new user.
     */
    private function createNewUser(Request $request, bool $isCoach)
    {
        $user = $this->createUser($request);
        $user->roles()->attach(Role::GUARDIAN);

        if ($isCoach) {
            $this->assignCoachRole($user, $request);
        }

        return $user;
    }

    /**
     * Create a user model instance.
     */
    private function createUser(Request $request)
    {
        return User::create([
            'firstName' => $request->firstName,
            'lastName' => $request->lastName,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'mobile_number' => $request->mobile_number,
            'is_joined' => true,
            'current_role' => 'guardian'
        ]);
    }

    /**
     * Assign coach role and create associated team.
     */
    private function assignCoachRole(User $user, Request $request)
    {
        $user->update([
            'town' => $request->town,
            'teamName' => $request->team,
            'is_coach' => true
        ]);

        $user->roles()->attach(Role::COACH);

        $team = Team::create([
            'user_id' => $user->id,
            'name' => $request->team,
        ]);

        TeamCoach::create([
            'coach_id' => $user->id,
            'team_id' => $team->id,
            'is_primary' => true,
        ]);
    }

    /**
     * Handle the signup view logic.
     */
    private function handleSignupView($token)
    {
        if ($token) {
            $id = Crypt::decryptString($token);
            $additionalGuardian = User::where("id", $id)->first();
            return view('guardian.signup', compact('additionalGuardian'));
        }

        return view('guardian.signup');
    }

    public function guardianSignupInvitedByCoach(Request $request, $coachInviteToken = null)
    {

        if ($coachInviteToken) {

            //  $email = Crypt::decryptString($coachInviteToken);


            $invitation = GuardianInvitationByCoach::where('token', $coachInviteToken)
                ->where('status', 'pending')
                ->first();

            if ($invitation) {

                return view('guardian.signup', compact('invitation'));
            }
        }
    }

    public function index(Request $request)
    {
        $user = $request->user();

        // Get all active subscriptions for the user
        $subscriptions = Subscription::where('user_id', $user->id)
            ->whereIn('status', ['active', 'trialing', 'past_due'])
            ->orderBy('next_payment_date')
            ->get();

        $additionalGuardians = User::where(function ($query) use ($user) {
            $query->where('parent_id', $user->id)
                ->orWhere('id', $user->primary_parent_id)
                ->orWhere('id', $user->parent_id)
                ->orWhere('primary_parent_id', $user->id);
        })



            // ->where('is_joined', true)
            ->withRole('guardian')
            ->paginate(6);




        $allPlayers = User::where('primary_parent_id', $user->id)
            ->orWhere('primary_parent_id', $user->primary_parent_id)
            ->withRole('player')
            ->get();

        $playerIds = $allPlayers->pluck('id');

        $playerPrograms = PlayerProgram::whereIn('player_id', $playerIds)->get();

        $programIds = $playerPrograms->pluck('program_id')->unique();

        $pastPrograms = Program::whereIn('id', $programIds)
            ->where('end_date', '<', now())
            ->distinct()
            ->get();

        // remove the unpaid program registrations
        ProgramRegistration::where('user_id', $user->id)
            ->where('is_paid', false)
            ->delete();

        $adminNotifications = [];
        $notificationsByAdmin = DB::table('admin_invites_player_for_program')
            ->whereIn('user_id', $playerIds)->where('status', 'pending')
            ->get();

        foreach ($notificationsByAdmin as $singleNotification) {

            $programId = $singleNotification->program_id;


            $program = DB::table('programs')->where('id', $programId)->first();

            if ($program) {

                $registrationClosingDate = $program->registration_closing_date;
                $currentDate = now();

                if ($currentDate->lte($registrationClosingDate)) {

                    $playerJoined = DB::table('player_program')
                        ->where('program_id', $programId)
                        ->where('player_id', $singleNotification->user_id)
                        ->exists();
                    if (!$playerJoined) {


                        $player = DB::table('users')
                            ->where('id', $singleNotification->user_id)
                            ->first();


                        $adminNotifications[] = [
                            'firstName' => $player->firstName,
                            'lastName' => $player->lastName,
                            'player_id' => $singleNotification->user_id,
                            'program_slug' => $program->slug,
                            'message' => 'Player has been invited.',
                        ];
                    }
                }
            }
        }

        $notifications = PlayerInvitation::whereIn('user_id', $playerIds)
            ->where('invitation_status', 'pending')
            ->get();

        $players = User::where('primary_parent_id', $user->id)
            ->orWhere('primary_parent_id', $user->primary_parent_id)
            ->withRole('player')
            ->paginate(2);


        $notifications = $notifications->filter(function ($notification) {
            $program = Program::find($notification->program_id);
            if (!$program || ($program->registration_closing_date && $program->registration_closing_date < now()->toDateString())) {
                return false;
            }
            return true;
        });



        foreach ($notifications as $notification) {
            $coach = User::find($notification->coach_id);
            $player = User::find($notification->user_id);
            $team = Team::find($notification->team_id);
            $program = Program::find($notification->program_id);

            // Check if this is a post-tryout program invitation
            $isPostTryout = false;
            if ($program && $program->type === 'Tryout' && $program->end_date < now()->toDateString()) {
                $isPostTryout = true;
            }

            $notification->coach_name = $coach->firstName . ' ' . $coach->lastName;
            $notification->player_name = $player->firstName . ' ' . $player->lastName;
            $notification->player_id = $player->id;
            $notification->team_name = $team->name;
            $notification->team_id = $team->id;
            $notification->is_post_tryout = $isPostTryout;
            $notification->program_name = $program ? $program->name : '';

            // For post-tryout programs, use admin name from invited_by field instead of coach name
            if ($isPostTryout && $notification->invited_by) {
                $inviter = User::find($notification->invited_by);
                $notification->inviter_name = $inviter ? ($inviter->firstName . ' ' . $inviter->lastName) : $notification->coach_name;
            } else {
                $notification->inviter_name = $notification->coach_name;
            }
        }



        $playerAges = [];

        foreach ($players as $player) {
            if ($player->birthDate) {
                $birthDate = new DateTime($player->birthDate);
                $currentDate = new DateTime();
                $interval = $currentDate->diff($birthDate);

                $playerAges[] = [
                    'age' => $interval->y
                ];
            }
        }




        if ($playerAges) {
            $today = Carbon::today()->toDateString();
            try {
                $availablePrograms = Program::where(function ($query) use ($playerAges, $players) {
                    foreach ($playerAges as $index => $age) {
                        $player = $players[$index];
                        $query->orWhere(function ($q) use ($age, $player) {
                            // Age restriction check
                            $q->where(function ($q) use ($age) {
                                $q->where(function ($q) use ($age) {
                                    $q->whereNotNull('age_restriction_from')
                                        ->whereNotNull('age_restriction_to')
                                        ->where('age_restriction_from', '<=', $age['age'])
                                        ->where('age_restriction_to', '>=', $age['age']);
                                })
                                    ->orWhere(function ($q) {
                                        $q->whereNull('age_restriction_from')
                                            ->whereNull('age_restriction_to');
                                    });
                            });

                            // Birth date cutoff check
                            $q->where(function ($q) use ($player) {
                                $q->whereNull('birth_date_cutoff')
                                    ->orWhere('birth_date_cutoff', '<=', $player->birthDate);
                            });

                            // Gender check
                            $q->where(function ($q) use ($player) {
                                $playerGender = strtolower($player->gender);
                                if ($playerGender === 'boy') {
                                    $q->where(function ($gq) {
                                        $gq->where('gender', 'boys')
                                            ->orWhere('gender', 'coed');
                                    });
                                } elseif ($playerGender === 'girl') {
                                    $q->where(function ($gq) {
                                        $gq->where('gender', 'girls')
                                            ->orWhere('gender', 'coed');
                                    });
                                }
                            });



                            // Grade check
                            // $q->where(function ($q) use ($player) {
                            //     $q->whereNull('grade')
                            //         ->orWhere('grade', 'LIKE', "%{$player->grade}%");
                            // });
                        });
                    }
                })
                    ->whereIn('type', ['Individual', 'AAU'])
                    ->where('registration_closing_date', '>=', $today)
                    ->where('is_draft', false)
                    ->where('status', 'public')
                    ->orderBy('created_at', 'desc')
                    ->paginate(20);
            } catch (Exception $e) {

                Log::error('Error fetching available programs: ' . $e->getMessage());
                $availablePrograms = collect();
            }
        } else {
            $availablePrograms = collect();
        }






        //FIXME:nothing to fix
        if ($playerIds->isNotEmpty()) {
            $totalAmount = GuardianPayment::where('user_id', $user->id)
                ->where('payment_type', '!=', 'recurring')
                ->sum('pending_amount');
        } else {
            $totalAmount = null;
        }

        $recurringPayments = DB::table('recurring_payments')
            ->where('user_id', $user->id)
            ->select('id', 'total_amount_due', 'paid_amount', DB::raw('total_amount_due - paid_amount as remaining_due'))
            ->get();
        $oldestRecurringPaymentStartDate = DB::table('recurring_payments')
            ->where('user_id', $user->id)
            ->where('paid_amount', '>', 'total_amount_due')
            ->orderBy('start_date', 'asc')
            ->value('start_date');

        $oldestRecurringPaymentNextPaymentDate = $oldestRecurringPaymentStartDate
            ? Carbon::parse($oldestRecurringPaymentStartDate)->addMonth()
            : null;
        $recurringAmount = null;

        foreach ($recurringPayments as $payment) {
            $recurringAmount = $recurringPayments->sum('remaining_due');
        }


        $outStandingBalanceToRecurring = DB::table('outstanding_recurring_payments')
            ->where('user_id', $user->id)
            // ->where(function ($query) {
            //     $query->where('initial_amount_paid', '!=', 0)
            //           ->orWhere('amount_paid', '!=', 0);
            // })
            ->select('total_amount_due', DB::raw('total_amount_due - amount_paid as remaining_due'))
            ->get();

        $balance = 0;
        foreach ($outStandingBalanceToRecurring as $recurringPayment) {
            $balance = $outStandingBalanceToRecurring->sum('remaining_due');
        }



        $recurringAmount = $recurringAmount + $balance;

        return view('guardian.dashboard', compact('user', 'players', 'availablePrograms', 'notifications', 'additionalGuardians', 'totalAmount', 'pastPrograms', 'recurringAmount', 'adminNotifications', 'oldestRecurringPaymentNextPaymentDate', 'subscriptions'));
    }


    public function loadMoreGuardians(Request $request)
    {
        $user = Auth::user();
        $lastGuardianId = $request->input('lastGuardianId');

        $query = User::where(function ($query) use ($user) {
            $query->where('parent_id', $user->id)
                ->orWhere('id', $user->primary_parent_id)
                ->orWhere('id', $user->parent_id);
        })
            ->where('is_joined', true)
            ->where('id', '!=', $lastGuardianId)
            ->withRole('guardian');

        $additionalGuardians = $query->paginate(1);
        return response()->json([
            'additionalGuardians' => $additionalGuardians->items(),
            'additionalGuardiansNextPage' => $additionalGuardians->nextPageUrl(),
        ]);
    }



    public function loadMorePlayers(Request $request)
    {
        $user = Auth::user();
        $loadedPlayerIds = explode(',', $request->input('loadedPlayerIds', ''));


        $query = User::where('primary_parent_id', $user->id)
            ->orWhere('primary_parent_id', $user->primary_parent_id)
            ->withRole('player');

        if (!empty($loadedPlayerIds[0])) {

            $query->whereNotIn('id', $loadedPlayerIds);
        }

        $perPage = 2;
        $page = $request->input('page', 1);


        $additionalPlayers = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'players' => $additionalPlayers->items(),
            'playersNextPage' => $additionalPlayers->nextPageUrl(),
        ]);
    }






    public function player_add(Request $request)
    {
        $guardian = Auth::user();

        $request->validate([
            'firstName' => 'required',
            'lastName' => 'required',
            'email' => 'nullable|email|unique:users,email',
            'birthDate' => 'required|date',
            'grade' => 'required',
            'gender' => 'required',
            'street' => 'required',
            'town' => 'required|string',
            'state' => 'required|string',
            'profilePhoto' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'parent_id' => 'required|exists:users,id',
            'primary_parent_id' => 'required|exists:users,id'
        ]);

        try {

            if ($request->hasFile('profilePhoto')) {
                $profilePhoto = $request->file('profilePhoto');
                $fileName = time() . '.' . $profilePhoto->getClientOriginalExtension();
                $profilePhoto->storeAs('public', $fileName);
                $profilePhotoPath = $fileName;
            } else {
                $profilePhotoPath = null;
            }


            $birthDate = Carbon::parse($request->input('birthDate'));
            $age = $birthDate->age;

            $email = $request->input('email') ?: null;

            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $email,
                'birthDate' => $request->input('birthDate'),
                'age' => $age,
                'grade' => $request->input('grade'),
                'gender' => $request->input('gender'),
                'street' => $request->input('street'),
                'town' => $request->input('town'),
                'state' => $request->input('state'),
                'profilePhoto' => $profilePhotoPath,
                'parent_id' => $request->input('parent_id'),
                'primary_parent_id' => $request->input('primary_parent_id'),
            ]);


            $user->roles()->attach(Role::PLAYER);

            // $user->guardians()->attach($guardian);

            return response()->json(['success' => true, 'message' => 'Player account created successfully.']);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the player account. Please try again.']);
        }
    }

    public function guardian_edit($id)
    {
        $guardian = User::find($id);

        if ($guardian) {
            return response()->json([
                'success' => true,
                'guardian' => $guardian,
            ]);
        } else {
            return response()->json(['success' => false], 404);
        }
    }


    public function guardian_update(Request $request)
    {

        $guardianId = $request->input('guardian_id');

        $request->validate([
            'guardian_id' => 'required',
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users')->ignore($guardianId),
            ],
            'mobile_number' => [
                'required',
                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
            ],
        ]);
        $guardian = User::find($guardianId);


        if ($guardian) {
            $guardian->firstName = $request->input('firstName');
            $guardian->lastName = $request->input('lastName');
            $guardian->email = $request->input('email');
            $guardian->mobile_number = $request->input('mobile_number');

            $guardian->save();

            return response()->json(['success' => true]);
        } else {

            return response()->json(['success' => false], 404);
        }
    }




    public function edit($id)
    {
        $user = auth()->user();
        $player = User::find($id);

        if (!$player) {
            return response()->json(['success' => false, 'message' => 'Player not found'], 404);
        }

        if ($player->primary_parent_id == $user->id || $player->parent_id == $user->id) {
            $profilePhotoUrl = $player->profilePhoto
                ? url(Storage::url($player->profilePhoto))
                : '';


            return response()->json([
                'success' => true,
                'player' => [
                    'id' => $player->id,
                    'firstName' => $player->firstName,
                    'lastName' => $player->lastName,
                    'email' => $player->email,
                    'gender' => $player->gender,
                    'birthDate' => $player->birthDate,
                    'grade' => $player->grade,
                    'street' => $player->street,
                    'town' => $player->town,
                    'state' => $player->state,
                    'profilePhoto' => $profilePhotoUrl
                ],
            ]);
        } else {
            return response()->json(['success' => false, 'message' => 'Unauthorized access'], 403);
        }
    }



    public function addAdditionalGuardian(Request $request)
    {


        $request->validate([
            'guardian_id' => 'required|exists:users,id',
            'primary_parent_id' => 'required|exists:users,id'
        ]);

        try {
            $guardian = User::find($request->guardian_id);
            $guardian->primary_parent_id = $request->primary_parent_id;
            $guardian->save();
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the guardian. Please try again later.'], 500);
        }

        return response()->json(['success' => true, 'message' => 'Guardian updated successfully.']);
    }








    public function update(Request $request)
    {
        $request->validate([
            'player_id' => 'required|exists:users,id',
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' =>  'nullable|email',
            'gender' => 'required',
            'birthDate' => 'required|date',
            'grade' => 'required|string',
            'street' => 'required|string|max:255',
            'town' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'profilePhoto' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $player = User::find($request->input('player_id'));

        if (!$player) {
            return response()->json([
                'success' => false,
                'message' => 'Player not found.',
            ], 404);
        }

        $birthDate = Carbon::parse($request->input('birthDate'));
        $age = $birthDate->age;
        $email = $request->input('email') ?: null;


        $player->firstName = $request->input('firstName');
        $player->lastName = $request->input('lastName');
        $player->email = $email;
        $player->gender = $request->input('gender');
        $player->birthDate = $request->input('birthDate');
        $player->grade = $request->input('grade');
        $player->street = $request->input('street');
        $player->town = $request->input('town');
        $player->state = $request->input('state');
        $player->age = $age;

        if ($request->hasFile('profilePhoto')) {
            $profilePhoto = $request->file('profilePhoto');
            $fileName = time() . '.' . $profilePhoto->getClientOriginalExtension();
            $profilePhoto->storeAs('public', $fileName);
            $player->profilePhoto = $fileName;
        }
        try {
            $player->save();

            return response()->json([
                'success' => true,
                'message' => 'Player details updated successfully.',
                'data' => $player,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the player details. Please try again later.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }



    public function sendInvitationToAdditionalGuardian(Request $request)
    {
        $user = auth()->user();

        try {
            $request->validate([
                'firstName' => 'required',
                'email' => 'required|unique:users,email',
                'lastName' => 'required',
            ]);

            if ($user->primary_parent_id) {
                $primary_parent_id = $user->primary_parent_id;
            } else {
                $primary_parent_id = $user->id;
            }

            $additionalGuardian = User::create([
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'email' => $request->email,
                'parent_id' => $user->id,
                'primary_parent_id' => $primary_parent_id,
            ]);

            $additionalGuardian->roles()->attach(Role::GUARDIAN);

            try {
                $toEmailAddress = $request->input('email');
                $token = Crypt::encryptString($additionalGuardian->id);

                Log::info("Attempting to send email to: {$toEmailAddress}");

                Mail::to($toEmailAddress)->send(new SendInvitationToAdditionalGuardian($user, $token));

                Log::info("Email sent successfully to: {$toEmailAddress}");

                return response()->json(['success' => true, 'message' => 'Invitation sent successfully.']);
            } catch (Exception $e) {
                Log::error("Error sending email: " . $e->getMessage());

                return response()->json(['success' => false, 'message' => 'Please enter valid E-mail Address'], 500);
            }
        } catch (ValidationException $e) {
            $errors = $e->validator->errors();
            if ($errors->has('email')) {
                Log::error("Validation error: email already exists.");
                return response()->json(['success' => false, 'message' => 'The email address already exists.'], 422);
            }
            Log::error("Validation error: " . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Validation error.', 'errors' => $errors], 422);
        } catch (Exception $e) {
            Log::error("Unexpected error: " . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An unexpected error occurred. Please try again later.'], 500);
        }
    }




    // public function show_payment_option(){
    //     return view('guardian.payment');
    // }



    public function showPlayerProfile($playerId)
    {


        $user = auth()->user();

        // $playerId = Crypt::decrypt($playerId);

        $player = User::findOrFail($playerId);

        if ($player) {

            if ($player->primary_parent_id == $user->id || $player->parent_id == $user->id) {



                $playerPrograms = PlayerProgram::where('player_id', $player->id)->get();


                $programIds = $playerPrograms->pluck('program_id');


                $today = now();


                $currentPrograms = Program::whereIn('id', $programIds)
                    ->where('end_date', '>=', $today)
                    ->get();

                $pastPrograms = Program::whereIn('id', $programIds)
                    ->where('end_date', '<', $today)
                    ->get();

                $teamIds = TeamPlayer::where('player_id', $player->id)
                    ->pluck('team_id');

                $teams = Team::whereIn('id', $teamIds)
                    ->get(['id', 'name']);
                $playerTeams = $teams->pluck('name');

                return view('player.dashboard', compact('player', 'currentPrograms', 'pastPrograms', 'playerTeams'));
            } else {

                abort(404, 'Player not found.');
            }
        }
    }



    public function player_addToProgram(Request $request, $programSlug)
    {

        if (!$programSlug) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to find program.',
            ], 404);
        }

        $guardian = Auth::user();

        $request->validate([
            'firstName' => 'required',
            'lastName' => 'required',
            'email' => 'nullable|email|unique:users,email',
            'gender' => 'required',
            'birthDate' => 'required|date',
            'grade' => 'required',
            'street' => 'required|string|max:255',
            'town' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'profilePhoto' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'parent_id' => 'required',
            'primary_parent_id' => 'required',
        ]);

        try {

            if ($request->hasFile('profilePhoto')) {
                $profilePhoto = $request->file('profilePhoto');
                $fileName = time() . '.' . $profilePhoto->getClientOriginalExtension();
                $profilePhoto->storeAs('public', $fileName);
                $profilePhotoPath = $fileName;
            } else {
                $profilePhotoPath = null;
            }


            $birthDate = Carbon::parse($request->input('birthDate'));
            $age = $birthDate->age;
            $email = $request->input('email') ?: null;


            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $email,
                'gender' => $request->input('gender'),
                'birthDate' => $request->input('birthDate'),
                'age' => $age,
                'grade' => $request->input('grade'),
                'street' => $request->input('street'),
                'town' => $request->input('town'),
                'state' => $request->input('state'),
                'profilePhoto' => $profilePhotoPath,
                'parent_id' => $request->input('parent_id'),
                'primary_parent_id' => $request->input('primary_parent_id'),
            ]);


            $user->roles()->attach(Role::PLAYER);


            //  $decryptedProgramId = Crypt::decrypt($programId);

            //  $program=Program::where('id', $decryptedProgramId);
            //   $amount=$user->calculateAmountForProgram($decryptedProgramId);



            //   $registration=ProgramRegistration::create([
            //     'user_id' => $guardian->id,
            //     'program_id' => $decryptedProgramId,
            //     'amount'=>$amount,
            //     'player_id'=>$user->id,
            //   ]);


            //     $playerRegistration=PlayerProgram::create([
            //         'player_id'=>$user->id,
            //         'program_id'=>$decryptedProgramId,


            // ]);

            //  if(!$registration){
            //     return response()->json([
            //     'success' => false,
            //     'message' => 'Unable to register user for program.',
            // ], 404);


            //  }

            return response()->json([
                'success' => true,
                'message' => 'Player account created successfully.',
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the player account. Please try again.']);
        }
    }



    //TODO write new logic for it
    public function registerPlayer(Request $request)
    {
        $user = auth()->user();

        if (!$user) {

            return redirect()->back()->withErrors('UNAUTHORIZED ACCESS');
        }

        $selectedPlayerIds = $request->input('players', []);
        $programSlug = $request->input('program_id');

        $amount = 0;
        $totalAmount = 0;
        $registeredPlayerIds = [];

        $program = Program::where('slug', $programSlug)->first();
        $programId = $program->id;
        $programPayment = $program->payment;

        foreach ($selectedPlayerIds as $playerId) {

            $amount = $user->calculateAmountForProgram($programId);
            $totalAmount += $amount;
            // PlayerProgram::create([
            //     'player_id' => $playerId,
            //     'program_id' => $programId,
            // ]);


            ProgramRegistration::create([
                'user_id' => $user->id,
                'program_id' => $programId,
                'amount' => $amount,
                'player_id' => $playerId,
                'is_paid' => false
            ]);
            $registeredPlayerIds[] = $playerId;


            PlayerProgram::create([
                'player_id' => $playerId,
                'program_id' => $programId,
            ]);
        }

        $paymentData = [
            'user_id' => $user->id,
            'program_id' => $programId,
            'totalAmountToBePaid' => $totalAmount,
            'player_ids' => $registeredPlayerIds,
            'programPayment' => $programPayment,
        ];

        if ($programPayment === 'recurring') {
            $paymentData['minimum_recurring_amount'] = $program->minimum_recurring_amount;
        }

        session()->put('payment_data', $paymentData);



        return response()->json(['success' => true]);
    }



    public function registerPlayerForProgram(Request $request)
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json(['error' => 'UNAUTHORIZED ACCESS'], 401);
        }

        $selectedPlayerIds = $request->input('players', []);
        $programSlug = $request->input('program_id');
        $invitedPlayerId = $request->input('invited_player');

        $program = Program::where('slug', $programSlug)->firstOrFail();
        $programId = $program->id;
        $programPayment = $program->payment;


        if ($invitedPlayerId) {
            if (count($selectedPlayerIds) !== 1 || $selectedPlayerIds[0] != $invitedPlayerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only register the specifically invited player.'
                ], 403);
            }

            $adminInvitation = \App\Models\AdminInvitesPlayerForProgram::where('user_id', $invitedPlayerId)
                ->where('program_id', $programId)
                ->where('status', 'pending')
                ->first();

            if (!$adminInvitation) {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid invitation found for this player.'
                ], 403);
            }

            // Verify the player belongs to the current guardian
            $invitedPlayer = User::find($invitedPlayerId);
            if (!$invitedPlayer || ($invitedPlayer->primary_parent_id != $user->id && $invitedPlayer->primary_parent_id != $user->primary_parent_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to register this player.'
                ], 403);
            }
        }

        $totalAmount = 0;
        $registeredPlayerIds = [];

        foreach ($selectedPlayerIds as $playerId) {
            $amount = $user->calculateAmountForProgram($programId);
            $totalAmount += $amount;
            $registeredPlayerIds[] = $playerId;

            // Check if this player was invited by admin - status will be updated after successful payment
            $adminInvitation = \App\Models\AdminInvitesPlayerForProgram::where('user_id', $playerId)
                ->where('program_id', $programId)
                ->where('status', 'pending')
                ->first();

            // Status will be updated to 'accepted' after successful payment in webhook handler
            // if ($adminInvitation) {
            //     $adminInvitation->update(['status' => 'accepted']);
            // }
        }

        $paymentData = [
            'user_id' => $user->id,
            'program_id' => $programId,
            'totalAmountToBePaid' => $totalAmount,
            'player_ids' => $registeredPlayerIds,
            'programPayment' => $programPayment,
        ];

        if ($programPayment === 'recurring') {
            $paymentData['minimum_recurring_amount'] = $program->minimum_recurring_amount;
        }

        session()->put('payment_data', $paymentData);

        return response()->json(['success' => true]);
    }




    public function acceptInvitation(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'invitation_id' => 'required|exists:player_invitations,team_id',
            'player_id' => 'required',
            'program_id' => 'required',
        ]);

        $invitation = PlayerInvitation::where('team_id', $request->input('invitation_id'))
            ->where('user_id', $request->input('player_id'))
            ->where('program_id', $request->input('program_id'))
            ->first();

        $amount = $invitation->balance_due;
        $playerId = $invitation->user_id;
        $programId = $invitation->program_id;
        $program = Program::findOrFail($programId);
        $programPayment = $program->payment;
        $teamId = $request->input('invitation_id');


        $paymentData = [
            'user_id' => $user->id,
            'program_id' => $programId,
            'totalAmountToBePaid' => $amount,
            'player_ids' => [$playerId],
            'programPayment' => $programPayment,
            'teamId' => $teamId,
            'amount' => $amount
        ];

        if ($programPayment === 'recurring') {
            $paymentData['minimum_recurring_amount'] = $program->minimum_recurring_amount;
        }

        session()->put('payment_data_forAcceptInvite', $paymentData);

        return redirect()->route('guardian.programPaymentforAcceptInvite');
    }



    public function paymentMethod(Request $request)
    {


        try {
            $validatedData = $request->validate([
                'user_id' => 'required|integer',
                'program_id' => 'required|integer',
                'payment_type' => 'required|string|in:fullAmount,specificAmount,recurringPayments,otherGuardian',
                'paidAmount' => 'required|numeric|',
                'totalAmountToBePaid' => 'required|numeric|min:0',
                'player_ids' => 'required|array',
                'email' => 'nullable|email|required_if:payment_type,otherGuardian',
                'team_id' => 'nullable',
            ]);

            // if ($request->has('applied_coupon')) {
            //     $validator = Validator::make($request->all(), [
            //         'applied_coupon.id'             => 'required|integer|exists:coupons,id',
            //         'applied_coupon.code'           => 'required|string|max:255',
            //         'applied_coupon.discount_value' => 'required|numeric|min:0',
            //         'applied_coupon.description'    => 'nullable|string|max:500',
            //         'discount_amount'               => 'required|numeric|min:0',
            //     ]);

            //     if ($validator->fails()) {
            //         return response()->json([
            //             'status' => 'error',
            //             'errors' => $validator->errors(),
            //         ], 422);
            //     }
            // }



            $program = Program::find($validatedData['program_id']);
            if (!$program) {
                return response()->json([
                    'error' => 'Program not found',
                ], 404);
            }

            // totalAmountToBePaid should be the program origin cost

            $validatedData['totalAmountToBePaid'] = $program->cost;

            $user = User::find($validatedData['user_id']);
            if (!$user) {
                return response()->json([
                    'error' => 'User not found',
                ], 404);
            }

            // Handle external payment (otherGuardian)
            if ($validatedData['payment_type'] === 'otherGuardian') {
                return $this->handleExternalPayment($user, $program, $validatedData);
            }

            $programPayment = $program->payment;

            // Validate payment type against program payment settings
            if ($programPayment === 'recurring') {
                if (!in_array($validatedData['payment_type'], ['recurringPayments', 'fullAmount', 'otherGuardian'])) {
                    return response()->json([
                        'message' => 'Invalid payment type for recurring program',
                    ], 422);
                }
            }

            if ($programPayment === "full") {
                if (!in_array($validatedData['payment_type'], ['fullAmount', 'otherGuardian'])) {
                    return response()->json([
                        'message' => 'Invalid payment type for full payment program',
                    ], 422);
                }
            }

            if ($programPayment === "split") {
                if (!in_array($validatedData['payment_type'], ['specificAmount', 'fullAmount', 'otherGuardian'])) {
                    return response()->json([
                        'message' => 'Invalid payment type for split payment program',
                    ], 422);
                }
            }

            session()->put('payment_data', $validatedData);

            if ($request->has('team_id')) {
                session()->put('team_id', $request->team_id);
            }

            return response()->json([
                'success' => true,
                'redirect_url' => route('guardian.cardDetails'),
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'details' => $e->errors(),
            ], 422);
        }
    }

    /**
     * Handle external payment by creating payment transaction and external link
     */
    private function handleExternalPayment($user, $program, $validatedData)
    {
        try {
            // Create payment transaction data
            $transactionData = [
                'user_id' => $user->id,
                'program_id' => $program->id,
                'player_ids' => $validatedData['player_ids'],
                'total_amount' => $validatedData['totalAmountToBePaid'],
                'pending_amount' => $validatedData['totalAmountToBePaid'],
                'payment_method' => 'external_link',
                'payment_type' => 'external',
                'external_email' => $validatedData['email'],
                'payment_metadata' => [
                    'original_payment_type' => $validatedData['payment_type'],
                    'requested_amount' => $validatedData['paidAmount'],
                    'total_amount' => $validatedData['totalAmountToBePaid'],
                ]
            ];

            // Create payment transaction
            $transaction = $this->paymentService->createPaymentTransaction($transactionData);

            // Create external payment link
            $externalLink = $this->paymentService->createExternalPaymentLink($transaction, $validatedData['email']);

            // Send external payment link
            $this->paymentService->sendExternalPaymentLink($externalLink);

            return response()->json([
                'success' => true,
                'external_payment_sent' => true,
                'transaction_id' => $transaction->transaction_id,
                'external_email' => $validatedData['email'],
                'message' => 'Payment link has been sent to ' . $validatedData['email'],
                'redirect_url' => route('guardian.dashboard'),
            ]);
        } catch (\Exception $e) {
            Log::error('External payment creation failed', [
                'user_id' => $user->id,
                'program_id' => $program->id,
                'external_email' => $validatedData['email'],
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to create external payment link. Please try again.',
            ], 500);
        }
    }

    public function cardDetails(Request $request)
    {

        $paymentData = session('payment_data');


        if (!$paymentData) {
            return redirect()->route('guardian.programPayment')->withErrors('Payment information is missing.');
        }

        return view('guardian.cardDetails', ['paymentData' => $paymentData]);
    }



    public function switchRoleToCoach(Request $request, $userId)
    {
        $user = User::findOrFail($userId);
        if ($user->roles()->where('name', 'coach')->exists()) {




            if ($user->is_coach) {
                $user->update(['current_role' => 'coach']);
                return response()->json([
                    'success' => true,
                    'redirectUrl' => route('coach.dashboard')
                ]);
            } else {

                $user->update(['current_role' => 'coach', 'is_coach' => true]);
                return response()->json([
                    'success' => true,
                    'redirectUrl' => route('coach.dashboard')
                ]);
            }
        }



        return response()->json([
            'success' => false,
            'message' => 'User does not have the coach role'
        ], 404);
    }



    public function guardiansForPayment(Request $request)
    {

        $user = auth()->user();


        $guardians = User::where(function ($query) use ($user) {
            $query->where('parent_id', $user->id)
                ->orWhere('id', $user->primary_parent_id)
                ->orWhere('id', $user->parent_id);
        })
            ->where('is_joined', true)
            ->withRole('guardian')
            ->get();


        if ($guardians->isEmpty()) {
            return response()->json([
                'message' => 'No guardians found for this user.',
                'data' => [],
            ], 404);
        }


        return response()->json([
            'message' => 'Guardians retrieved successfully.',
            'guardians' => $guardians,
        ], 200);
    }


    public function showPaymentPage()
    {

        $paymentData = session()->get('payment_data');


        return view('guardian.payment', ['paymentData' => $paymentData]);
    }



    public function showPaymentOptions()
    {
        $user = auth()->user();
        //      $additionalGuardians = User::where(function ($query) use ($user) {
        //      $query->where('parent_id', $user->id)
        //           ->orWhere('id', $user->primary_parent_id)
        //           ->orWhere('id', $user->parent_id);
        // })
        // ->where('is_joined', true)
        // ->withRole('guardian')
        // ->get();
        // $additionalGuardianIds = $additionalGuardians->pluck('id')->toArray();
        // $guardianPayments = GuardianPayment::where('user_id', $user->id)
        //     ->orWhereIn('user_id', $additionalGuardianIds)
        //     ->get();
        // $totalBalanceDue = $guardianPayments->sum('pending_amount');

        // $formattedBalanceDue = number_format($totalBalanceDue, 2, '.', '');
        $guardianPayments = GuardianPayment::where('user_id', $user->id)->where('payment_type', 'split')->get();

        $totalBalanceDue = $guardianPayments->sum('pending_amount');

        $formattedBalanceDue = number_format($totalBalanceDue, 2, '.', '');

        return view('guardian.paymentOptions', compact('formattedBalanceDue', 'user'));
    }


    public function programPayment(Request $request)
    {
        if (session()->has('payment_data')) {
            $paymentData = session('payment_data');
        } else {
            abort(404, 'Payment data not found.');
        }


        $user = User::find($paymentData['user_id']);
        $program = Program::find($paymentData['program_id']);

        if ($paymentData['totalAmountToBePaid']) {
            $amount = $paymentData['totalAmountToBePaid'];
        }
        $playerIds = $paymentData['player_ids'];

        return view('guardian.programPayment', compact('user', 'program', 'amount', 'playerIds'));
    }

    public function programPaymentForAcceptInvite(Request $request)
    {
        if (session()->has('payment_data_forAcceptInvite')) {
            $paymentData = session('payment_data_forAcceptInvite');
        } else {
            abort(404, 'Payment data not found.');
        }


        $user = User::find($paymentData['user_id']);
        $program = Program::find($paymentData['program_id']);

        if ($paymentData['totalAmountToBePaid']) {
            $amount = $paymentData['totalAmountToBePaid'];
        }
        $playerIds = $paymentData['player_ids'];

        return view('guardian.programPaymentForAcceptInvite', compact('user', 'program', 'amount', 'playerIds'));
    }

    /**
     * Check available credit for the authenticated guardian
     */
    public function checkCredit()
    {
        try {
            $user = auth()->user();

            if (!$user || !$user->hasRole('guardian')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access.'
                ], 401);
            }

            $availableCredit = $user->getTotalAvailableCredit();

            return response()->json([
                'success' => true,
                'available_credit' => $availableCredit
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while checking credit.'
            ], 500);
        }
    }

    /**
     * Apply coupon code and calculate discount
     */
    public function applyCoupon(Request $request)
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string',
                'program_id' => 'required|integer',
                'original_amount' => 'required|numeric|min:0'
            ]);

            $couponCode = strtoupper(trim($request->coupon_code));
            $programId = $request->program_id;
            $originalAmount = $request->original_amount;

            // Find the coupon for this specific program
            $coupon = \App\Models\ProgramCoupon::where('code', $couponCode)
                ->where('program_id', $programId)
                ->where('is_active', true)
                ->first();

            if (!$coupon) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid coupon code or coupon not applicable to this program.'
                ]);
            }

            // Check if coupon is still valid (not expired)
            if ($coupon->valid_until && now() > $coupon->valid_until) {
                return response()->json([
                    'success' => false,
                    'message' => 'This coupon has expired.'
                ]);
            }

            // Check usage limit
            if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
                return response()->json([
                    'success' => false,
                    'message' => 'This coupon has reached its usage limit.'
                ]);
            }

            // Calculate discount
            $discountAmount = 0;
            if ($coupon->discount_type === 'percentage') {
                $discountAmount = ($originalAmount * $coupon->discount_value) / 100;
            } else { // fixed amount
                $discountAmount = min($coupon->discount_value, $originalAmount);
            }

            // Ensure discount doesn't exceed original amount
            $discountAmount = min($discountAmount, $originalAmount);

            return response()->json([
                'success' => true,
                'coupon' => [
                    'id' => $coupon->id,
                    'code' => $coupon->code,
                    'discount_type' => $coupon->discount_type,
                    'discount_value' => $coupon->discount_value,
                    'description' => $coupon->description
                ],
                'discount_amount' => $discountAmount,
                'final_amount' => $originalAmount - $discountAmount
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while applying the coupon. Please try again.'
            ], 500);
        }
    }
}
