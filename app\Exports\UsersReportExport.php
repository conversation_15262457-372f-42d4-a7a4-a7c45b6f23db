<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UsersReportExport implements FromView, WithStyles
{
    protected $users;

    public function __construct()
    {
        $this->users = User::with(['roles', 'teams', 'programRegistrations'])
        ->whereDoesntHave('roles', function ($query) {
            $query->where('name', 'admin');
        })
        ->get();

    }

    // Return the Blade view with users data
    public function view(): View
    {
        return view('exports.users_report_excel', [
            'users' => $this->users,
        ]);
    }

    // Apply styles to the Excel sheet
   public function styles(Worksheet $sheet)
{
    // Title row styling - normal font, no bold, no background
    $sheet->getStyle('A1:F1')->applyFromArray([
        'font' => ['bold' => false, 'size' => 14],
        'alignment' => ['horizontal' => 'center'],
    ]);

    // Header row styling - normal font, no bold, no background
    $sheet->getStyle('A2:F2')->applyFromArray([
        'font' => ['bold' => false],
        // 'fill' removed
        'borders' => [
            'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],
        ],
    ]);

    foreach (range('A', 'F') as $column) {
        $sheet->getColumnDimension($column)->setAutoSize(true);
    }

    $row = 3;

    foreach ($this->users as $user) {
        // User row - no background
        $sheet->getStyle("A{$row}:F{$row}")->applyFromArray([
            'font' => ['bold' => false],
            // no fill
        ]);
        $row++;

        if ($user->programRegistrations->count()) {
            // Program header row - no background, no bold
            $sheet->getStyle("A{$row}:F{$row}")->applyFromArray([
                'font' => ['bold' => false],
                // no fill
            ]);
            $row++;

            // Program table headers - no background, no bold
            $sheet->getStyle("A{$row}:F{$row}")->applyFromArray([
                'font' => ['bold' => false],
                // no fill
            ]);
            $row++;

            // Program rows - no background
            foreach ($user->programRegistrations as $program) {
                $sheet->getStyle("A{$row}:F{$row}")->applyFromArray([
                    'font' => ['bold' => false],
                    // no fill
                ]);
                $row++;
            }
        } else {
            $row++;
        }
    }

    return [];
}

}
