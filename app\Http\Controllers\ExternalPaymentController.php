<?php

namespace App\Http\Controllers;

use App\Models\ExternalPaymentLink;
use App\Models\PaymentTransaction;
use App\Services\PaymentTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\PaymentIntent;

class ExternalPaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentTransactionService $paymentService)
    {
        $this->paymentService = $paymentService;
        Stripe::setApiKey(env('STRIPE_SECRET'));
    }

    /**
     * Show external payment page
     */
    public function showPaymentPage(string $token)
    {
        $link = ExternalPaymentLink::where('token', $token)->first();

        if (!$link) {
            return view('external.payment-not-found');
        }

        if ($link->isExpired()) {
            return view('external.payment-expired', compact('link'));
        }

        if (!$link->canProcessPayment()) {
            return view('external.payment-completed', compact('link'));
        }

        // Mark link as opened and log access
        $link->markAsOpened(request()->ip());

        $paymentData = [
            'link' => $link,
            'program' => $link->program,
            'requesting_user' => $link->requestingUser,
            'player_names' => $link->player_names,
            'amount_to_pay' => $link->amount_to_pay,
            'expires_at' => $link->expires_at,
        ];

        return view('external.payment-form', compact('paymentData'));
    }

    /**
     * Process external payment
     */
    public function processPayment(Request $request, string $token)
    {
        $validator = Validator::make($request->all(), [
            'payment_method_id' => 'required|string',
            'payer_name' => 'required|string|max:255',
            'payer_email' => 'required|email|max:255',
            'address' => 'required|string|max:500',
            'state' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $link = ExternalPaymentLink::where('token', $token)->first();

        if (!$link) {
            return response()->json(['success' => false, 'message' => 'Payment link not found.'], 404);
        }

        if (!$link->canProcessPayment()) {
            return response()->json(['success' => false, 'message' => 'This payment link is no longer valid.'], 400);
        }

        try {
            // Create Stripe PaymentIntent
            $paymentIntent = PaymentIntent::create([
                'amount' => $link->amount_to_pay * 100, // Convert to cents
                'currency' => 'usd',
                'payment_method' => $request->payment_method_id,
                'confirm' => true,
                'description' => 'External Payment - ' . $link->program->name,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',
                ],
                'metadata' => [
                    'external_payment_link_id' => $link->id,
                    'payment_transaction_id' => $link->payment_transaction_id,
                    'program_id' => $link->program_id,
                    'requesting_user_id' => $link->requesting_user_id,
                    'player_ids' => json_encode($link->player_ids),
                    'payer_name' => $request->payer_name,
                    'payer_email' => $request->payer_email,
                    'address' => $request->address,
                    'state' => $request->state,
                ],
            ]);

            if ($paymentIntent->status === 'succeeded') {
                // Mark external payment as completed
                $link->markAsPaymentCompleted($paymentIntent->charges->data[0]->id ?? null);

                // Update the main payment transaction
                $transaction = $link->paymentTransaction;
                $this->paymentService->completeTransaction($transaction, [
                    'stripe_payment_intent_id' => $paymentIntent->id,
                    'stripe_charge_id' => $paymentIntent->charges->data[0]->id ?? null,
                    'paid_amount' => $link->amount_to_pay,
                ]);

                // Update transaction with external payment details
                $transaction->update([
                    'external_payment_status' => 'payment_completed',
                    'payment_metadata' => array_merge($transaction->payment_metadata ?? [], [
                        'external_payer_name' => $request->payer_name,
                        'external_payer_email' => $request->payer_email,
                        'external_payment_completed_at' => now()->toISOString(),
                    ]),
                ]);

                Log::info('External payment completed', [
                    'link_id' => $link->id,
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $link->amount_to_pay,
                    'payer_email' => $request->payer_email,
                    'stripe_payment_intent_id' => $paymentIntent->id,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully!',
                    'redirect_url' => route('external.payment.success', ['token' => $token]),
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment was not successful. Please try again.',
            ], 400);
        } catch (\Exception $e) {
            Log::error('External payment processing failed', [
                'link_id' => $link->id,
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed. Please try again.',
            ], 500);
        }
    }

    /**
     * Show payment success page
     */
    public function showSuccessPage(string $token)
    {
        $link = ExternalPaymentLink::where('token', $token)->first();

        if (!$link) {
            return view('external.payment-not-found');
        }

        if ($link->status !== 'payment_completed') {
            return redirect()->route('external.payment', ['token' => $token]);
        }

        $paymentData = [
            'link' => $link,
            'program' => $link->program,
            'requesting_user' => $link->requestingUser,
            'player_names' => $link->player_names,
            'amount_paid' => $link->amount_to_pay,
            'completed_at' => $link->payment_completed_at,
        ];

        return view('external.payment-success', compact('paymentData'));
    }

    /**
     * Check payment status (AJAX endpoint)
     */
    public function checkStatus(string $token)
    {
        $link = ExternalPaymentLink::where('token', $token)->first();

        if (!$link) {
            return response()->json(['success' => false, 'message' => 'Payment link not found.'], 404);
        }

        return response()->json([
            'success' => true,
            'status' => $link->status,
            'is_expired' => $link->isExpired(),
            'can_process_payment' => $link->canProcessPayment(),
            'amount_to_pay' => $link->amount_to_pay,
            'expires_at' => $link->expires_at->format('c'),
        ]);
    }

    /**
     * Resend payment link (for admin use)
     */
    public function resendLink(Request $request, int $linkId)
    {
        // This would be called from admin panel
        $link = ExternalPaymentLink::findOrFail($linkId);

        if (!$link->isActive()) {
            return response()->json(['success' => false, 'message' => 'Payment link is no longer active.'], 400);
        }

        try {
            $sent = $this->paymentService->sendExternalPaymentLink($link);

            if ($sent) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment link resent successfully.',
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to resend payment link.',
            ], 500);
        } catch (\Exception $e) {
            Log::error('Failed to resend external payment link', [
                'link_id' => $linkId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resend payment link.',
            ], 500);
        }
    }
}
