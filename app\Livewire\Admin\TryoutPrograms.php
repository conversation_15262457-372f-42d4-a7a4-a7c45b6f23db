<?php

namespace App\Livewire\Admin;

use App\Models\Program;
use App\Models\ProgramRegistration;
use App\Models\PlayerInvitation;
use App\Models\AdminInvitesPlayerForProgram;
use App\Models\Team;
use App\Models\User;
use App\Events\PlayerInvitedByAdmin;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use GuzzleHttp\Psr7\Request;

class TryoutPrograms extends Component
{
    use WithPagination;

    public $search = '';
    public $filter = '';
    public $perPage = 10;
    public $showPostTryouts = false;
    public $selectedProgramId = null;

    // Regular program invitation properties
    public $expandedPrograms = [];
    public $resendInviteSuccess = null;

    protected $searchableFields = ['name', 'sport', 'grade', 'gender'];
    protected $filterableFields = ['sport' => 'sport', 'gender' => 'gender', 'grade' => 'grade'];
    protected $paginationTheme = 'bootstrap';
    protected $defaultOrderBy = 'created_at';






    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function render()
    {
        $programs = $this->buildQuery()->paginate($this->perPage);
        $postTryoutPrograms = $this->getPostTryoutPrograms();

        return view('livewire.admin.tryout-programs', compact('programs', 'postTryoutPrograms'));
    }

    protected function buildQuery()
    {
        return Program::query()
            ->where('type', 'Tryout')
            ->where('end_date', '>=', Carbon::today()) // Only show active/future tryout programs
            ->when($this->hasSearchTerm(), fn($query) => $this->applySearch($query))
            ->when($this->hasValidFilter(), fn($query) => $this->applyOrdering($query))
            ->unless($this->hasValidFilter(), fn($query) => $query->orderBy($this->defaultOrderBy));
    }

    protected function applySearch($query)
    {
        $searchTerm = $this->search;

        if ($this->hasValidFilter()) {
            return $this->applySpecificFieldSearch($query, $searchTerm);
        }

        return $this->applyGlobalSearch($query, $searchTerm);
    }

    protected function applySpecificFieldSearch($query, $searchTerm)
    {
        $field = $this->getFilterField();

        if ($field) {
            $query->where($field, 'like', "%{$searchTerm}%");
        } else {
            $this->applyDefaultSearch($query, $searchTerm);
        }

        return $query;
    }

    protected function applyGlobalSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            foreach ($this->searchableFields as $field) {
                $q->orWhere($field, 'like', "%{$searchTerm}%");
            }
        });
    }

    protected function applyDefaultSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'like', "%{$searchTerm}")
                ->orWhere('grade', 'like', "%{$searchTerm}%")
                ->orWhere('sport', 'like', "%{$searchTerm}%")
                ->orWhere('gender', 'like', "%{$searchTerm}%");
        });
    }

    protected function applyOrdering($query)
    {
        $field = $this->getFilterField() ?: $this->defaultOrderBy;

        // Fixed: Changed 'orderyBy' to 'orderBy'
        return $query->orderBy($field);
    }

    protected function getFilterField()
    {
        return $this->filterableFields[$this->filter] ?? null;
    }

    protected function hasSearchTerm()
    {
        return !empty($this->search);
    }

    protected function hasValidFilter()
    {
        return !empty($this->filter) && array_key_exists($this->filter, $this->filterableFields);
    }

    public function getAvailableFilters()
    {
        return array_keys($this->filterableFields);
    }

    public function getSearchableFields()
    {
        return $this->searchableFields;
    }

    public function addSearchableField($field)
    {
        if (!in_array($field, $this->searchableFields)) {
            $this->searchableFields[] = $field;
        }

        return $this;
    }

    public function addFilter($label, $field)
    {
        $this->filterableFields[$label] = $field;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->filter = '';
        $this->resetPage();
    }

    /**
     * Toggle post-tryout programs view
     */
    public function togglePostTryouts()
    {
        $this->showPostTryouts = !$this->showPostTryouts;
    }

    /**
     * Get post-tryout programs (programs where end date has passed)
     */
    public function getPostTryoutPrograms()
    {
        return Program::where('type', 'Tryout')
            ->where('end_date', '<', Carbon::today())
            ->with(['registrations.player'])
            ->orderBy('end_date', 'desc')
            ->get();
    }

    /**
     * Get registered players for a specific program with their invitation status
     */
    public function getPlayersForProgram($programId)
    {
        // Get registrations for the current program
        $registrations = ProgramRegistration::where('program_id', $programId)
            ->with(['player'])
            ->get();

        // Get all active programs (where registration is still open)
        $activePrograms = Program::where('registration_closing_date', '>=', now())
            ->where('type', '!=', 'Tryout') // Exclude tryout programs
            ->pluck('id');

        // Get all player invitations for active programs
        $playerInvitations = PlayerInvitation::whereIn('program_id', $activePrograms)
            ->with(['user', 'team', 'program'])
            ->get();

        return $registrations->map(function ($registration) use ($playerInvitations) {
            $player = $registration->player;

            // Find if this player has any active team invitation
            $invitation = $playerInvitations
                ->where('user_id', $player->id)
                ->sortByDesc('created_at')
                ->first();

            // Initialize status as not invited
            $invitationStatus = 'not_invited';

            if ($invitation) {
                $invitationStatus = $invitation->invitation_status ?? $invitation->status ?? 'pending';
            }

            return [
                'id' => $player->id,
                'name' => $player->firstName . ' ' . $player->lastName,
                'email' => $player->email,
                'invitation_status' => $invitationStatus,
                'invited_team' => $invitation && $invitation->team ? $invitation->team->name : null,
                'invited_program' => $invitation && $invitation->program ? $invitation->program->name : null,
                'invitation_id' => $invitation ? $invitation->id : null,
                'can_invite' => !$invitation, // This will be false if player has any active invitation (pending or accepted)
            ];
        });
    }



    /**
     * Move accepted player to team
     */
    public function movePlayerToTeam($playerId, $invitationId)
    {
        try {
            DB::beginTransaction();

            $invitation = PlayerInvitation::findOrFail($invitationId);

            // Check both status fields for accepted status
            $isAccepted = ($invitation->status === 'accepted') || ($invitation->invitation_status === 'accepted');

            if (!$isAccepted) {
                session()->flash('error', 'Player has not accepted the invitation yet.');
                return;
            }

            // Add player to team
            $team = Team::findOrFail($invitation->team_id);
            $player = User::findOrFail($playerId);

            // Check if player is already in the team
            if ($team->players()->where('player_id', $playerId)->exists()) {
                session()->flash('error', 'Player is already in this team.');
                return;
            }

            // Add player to team
            $team->players()->attach($playerId);

            // Update invitation status to completed
            $invitation->update([
                'status' => 'completed',
                'invitation_status' => 'completed'
            ]);

            DB::commit();

            session()->flash('success', 'Player successfully moved to team.');
        } catch (\Exception $e) {
            DB::rollback();
            session()->flash('error', 'Failed to move player to team: ' . $e->getMessage());
        }
    }



    /**
     * Toggle expanded state for a regular program
     */
    public function toggleProgramExpansion($programId)
    {
        if (in_array($programId, $this->expandedPrograms)) {
            $this->expandedPrograms = array_diff($this->expandedPrograms, [$programId]);
        } else {
            $this->expandedPrograms[] = $programId;
        }
    }

    /**
     * Get registered players for a tryout program with their invitation status
     * This includes both registered players and admin-invited players
     */
    public function getRegisteredPlayersForRegularProgram($programId)
    {
        $allPlayerIds = collect();


        $registeredPlayerIds = ProgramRegistration::where('program_id', $programId)
            ->pluck('player_id');


        $invitedPlayerIds = AdminInvitesPlayerForProgram::where('program_id', $programId)
            ->pluck('user_id');


        $allPlayerIds = $registeredPlayerIds->merge($invitedPlayerIds)->unique();


        return User::whereIn('id', $allPlayerIds)
            ->withRole('player')
            ->get()
            ->map(function ($player) use ($programId) {

                $adminInvitation = AdminInvitesPlayerForProgram::where('user_id', $player->id)
                    ->where('program_id', $programId)
                    ->first();


                $registration = ProgramRegistration::where('program_id', $programId)
                    ->where('player_id', $player->id)
                    ->first();


                $invitationStatus = 'not_invited';
                if ($adminInvitation) {
                    $invitationStatus = $adminInvitation->status;
                } elseif ($registration) {
                    $invitationStatus = 'registered';
                }

                return [
                    'id' => $player->id,
                    'name' => $player->firstName . ' ' . $player->lastName,
                    'email' => $player->email,
                    'invitation_status' => $invitationStatus,
                    'invitation_id' => $adminInvitation ? $adminInvitation->id : null,
                    'is_registered' => $registration ? true : false,
                    'is_paid' => $registration ? $registration->is_paid : false,
                ];
            });
    }

    /**
     * Resend invitation email to player and guardian
     */
    public function resendInvitation($playerId, $programId)
    {
        try {
            $player = \App\Models\User::findOrFail($playerId);
            $program = \App\Models\Program::findOrFail($programId);
            $guardian = \App\Models\User::find($player->primary_parent_id);
            if (!$guardian) {
                session()->flash('error', 'Guardian not found for this player.');
                return;
            }
            event(new \App\Events\PlayerInvitedByAdmin($guardian, $player, $program));
            $this->resendInviteSuccess = $playerId.'-'.$programId;
            $this->dispatchBrowserEvent('resend-invite-success', ['key' => $this->resendInviteSuccess]);
            session()->flash('success', 'Invitation email resent successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to resend invitation: ' . $e->getMessage());
        }
    }
}
