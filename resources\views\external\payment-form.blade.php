<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - {{ $paymentData['program']->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .payment-container {
            max-width: 600px;
            margin: 2rem auto;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        #card-element {
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            background-color: white;
        }
        .security-badge {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 0.375rem;
            padding: 0.75rem;
        }
        .expires-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.375rem;
            padding: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="payment-container">
            <!-- Header -->
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="fas fa-credit-card"></i> Secure Payment</h3>
                    <p class="mb-0">{{ $paymentData['program']->name }}</p>
                </div>
                <div class="card-body">
                    <!-- Payment Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-primary"></i> Payment Details</h6>
                            <p class="mb-1"><strong>Program:</strong> {{ $paymentData['program']->name }}</p>
                            <p class="mb-1"><strong>Players:</strong> 
                                @foreach($paymentData['player_names'] as $name)
                                    {{ $name }}@if(!$loop->last), @endif
                                @endforeach
                            </p>
                            <p class="mb-1"><strong>Requested by:</strong> {{ $paymentData['requesting_user']->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <div class="text-end">
                                <h4 class="text-primary">
                                    <strong>Amount: ${{ number_format($paymentData['amount_to_pay'], 2) }}</strong>
                                </h4>
                            </div>
                        </div>
                    </div>

                    <!-- Expiry Warning -->
                    <div class="expires-warning mb-4">
                        <i class="fas fa-clock text-warning"></i>
                        <strong>Payment Link Expires:</strong> 
                        {{ $paymentData['expires_at']->format('M j, Y \a\t g:i A') }}
                        <div id="countdown" class="mt-2"></div>
                    </div>

                    <!-- Payment Form -->
                    <form id="externalPaymentForm">
                        @csrf
                        
                        <!-- Payer Information -->
                        <h6><i class="fas fa-user text-primary"></i> Your Information</h6>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="payer_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="payer_name" name="payer_name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="payer_email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="payer_email" name="payer_email" 
                                       value="{{ $paymentData['link']->external_email }}" required>
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="mb-3">
                            <label for="address" class="form-label">Billing Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="Enter your billing address" required></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="state" class="form-label">State *</label>
                            <input type="text" class="form-control" id="state" name="state" 
                                   placeholder="Enter your state" required>
                        </div>

                        <!-- Card Information -->
                        <h6><i class="fas fa-credit-card text-primary"></i> Payment Information</h6>
                        <div class="mb-3">
                            <label class="form-label">Card Information *</label>
                            <div id="card-element">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                        </div>

                        <!-- Security Notice -->
                        <div class="security-badge mb-4">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <i class="fas fa-shield-alt fa-2x text-success"></i>
                                </div>
                                <div class="col">
                                    <strong>Your payment is secure</strong><br>
                                    <small>This payment is processed securely through Stripe. Your card information is encrypted and never stored on our servers.</small>
                                </div>
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms_accepted" required>
                            <label class="form-check-label" for="terms_accepted">
                                I agree to the terms and conditions and authorize this payment *
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="fas fa-lock"></i> Pay ${{ number_format($paymentData['amount_to_pay'], 2) }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-question-circle"></i> 
                    Need help? Contact {{ $paymentData['requesting_user']->name }} at {{ $paymentData['requesting_user']->email }}
                </small>
            </div>
        </div>
    </div>

    <!-- Processing Modal -->
    <div class="modal fade" id="processingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Processing...</span>
                    </div>
                    <p class="mt-2 mb-0">Processing your payment...</p>
                    <small class="text-muted">Please do not close this window</small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
    $(document).ready(function() {
        // Initialize Stripe
        const stripe = Stripe('{{ env("STRIPE_KEY") }}');
        const elements = stripe.elements();

        // Create card element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        });

        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });

        // Countdown timer
        const expiresAt = new Date('{{ $paymentData["expires_at"]->toISOString() }}');
        
        function updateCountdown() {
            const now = new Date();
            const timeLeft = expiresAt - now;
            
            if (timeLeft <= 0) {
                $('#countdown').html('<span class="text-danger"><strong>This payment link has expired</strong></span>');
                $('#submitBtn').prop('disabled', true);
                return;
            }
            
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            
            let countdownText = '';
            if (days > 0) countdownText += `${days}d `;
            if (hours > 0) countdownText += `${hours}h `;
            countdownText += `${minutes}m remaining`;
            
            $('#countdown').html(`<small class="text-muted">${countdownText}</small>`);
        }
        
        updateCountdown();
        setInterval(updateCountdown, 60000); // Update every minute

        // Handle form submission
        $('#externalPaymentForm').submit(function(e) {
            e.preventDefault();

            // Basic validation
            if (!$('#payer_name').val().trim()) {
                alert('Please enter your full name.');
                return;
            }

            if (!$('#payer_email').val().trim()) {
                alert('Please enter your email address.');
                return;
            }

            if (!$('#address').val().trim()) {
                alert('Please enter your billing address.');
                return;
            }

            if (!$('#state').val().trim()) {
                alert('Please enter your state.');
                return;
            }

            if (!$('#terms_accepted').is(':checked')) {
                alert('Please accept the terms and conditions.');
                return;
            }

            // Check if payment link is still valid
            if (new Date() >= expiresAt) {
                alert('This payment link has expired. Please contact the requester for a new link.');
                return;
            }

            // Disable submit button and show processing modal
            $('#submitBtn').prop('disabled', true);
            $('#processingModal').modal('show');

            // Create payment method
            stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
                billing_details: {
                    name: $('#payer_name').val(),
                    email: $('#payer_email').val(),
                    address: {
                        line1: $('#address').val(),
                        state: $('#state').val(),
                    },
                },
            }).then(function(result) {
                if (result.error) {
                    // Show error to customer
                    $('#processingModal').modal('hide');
                    $('#submitBtn').prop('disabled', false);
                    
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = result.error.message;
                } else {
                    // Submit payment method to server
                    processPayment(result.paymentMethod.id);
                }
            });
        });

        function processPayment(paymentMethodId) {
            $.ajax({
                url: '{{ route("external.payment.process", ["token" => $paymentData["link"]->token]) }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    payment_method_id: paymentMethodId,
                    payer_name: $('#payer_name').val(),
                    payer_email: $('#payer_email').val(),
                    address: $('#address').val(),
                    state: $('#state').val(),
                },
                success: function(response) {
                    $('#processingModal').modal('hide');
                    
                    if (response.success) {
                        // Redirect to success page
                        if (response.redirect_url) {
                            window.location.href = response.redirect_url;
                        } else {
                            alert(response.message || 'Payment completed successfully!');
                        }
                    } else {
                        $('#submitBtn').prop('disabled', false);
                        alert(response.message || 'Payment failed. Please try again.');
                    }
                },
                error: function(xhr) {
                    $('#processingModal').modal('hide');
                    $('#submitBtn').prop('disabled', false);
                    
                    let message = 'Payment processing failed. Please try again.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    
                    alert(message);
                }
            });
        }

        // Handle browser back button
        window.addEventListener('beforeunload', function(e) {
            if ($('#processingModal').hasClass('show')) {
                e.preventDefault();
                e.returnValue = 'Payment is being processed. Are you sure you want to leave?';
            }
        });
    });
    </script>
</body>
</html>
