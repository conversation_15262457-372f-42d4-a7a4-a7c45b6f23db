<?php

namespace Database\Factories;

use App\Models\ExternalPaymentLink;
use App\Models\PaymentTransaction;
use App\Models\User;
use App\Models\Program;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ExternalPaymentLinkFactory extends Factory
{
    protected $model = ExternalPaymentLink::class;

    public function definition(): array
    {
        return [
            'token' => 'EPL_' . strtoupper(Str::random(32)),
            'payment_transaction_id' => PaymentTransaction::factory(),
            'requesting_user_id' => User::factory(),
            'program_id' => Program::factory(),
            'external_email' => $this->faker->safeEmail(),
            'amount_to_pay' => $this->faker->randomFloat(2, 50, 500),
            'player_ids' => [$this->faker->numberBetween(1, 100)],
            'player_names' => [$this->faker->firstName() . ' ' . $this->faker->lastName()],
            'status' => $this->faker->randomElement(['created', 'sent', 'opened', 'payment_completed']),
            'sent_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'opened_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'payment_completed_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'expires_at' => $this->faker->dateTimeBetween('now', '+1 week'),
            'open_count' => $this->faker->numberBetween(0, 5),
            'access_logs' => [],
            'payment_metadata' => [],
        ];
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function opened(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'opened',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'opened_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'open_count' => $this->faker->numberBetween(1, 5),
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'payment_completed',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'opened_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'payment_completed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'stripe_charge_id' => 'ch_' . Str::random(24),
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-1 week', '-1 day'),
        ]);
    }
}
