@extends('layouts.app')

@section('title', 'Checkout')
@section('css')
<style>
body { background: #f7f8fa; }
.checkout-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
.checkout-header { background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }

.checkout-content { display: grid; grid-template-columns: 1fr 400px; gap: 24px; }
.payment-section { background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
.order-summary { background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); position: sticky; top: 20px; }
.program-card { display: flex; gap: 16px; padding: 16px; background: #f8f9fa; border-radius: 8px; margin-bottom: 20px; }
.program-image { width: 80px; height: 80px; background: linear-gradient(135deg, #0b4499, #1e5bb8); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; }
.program-details h6 { margin: 0 0 4px 0; font-weight: 600; }
.program-details small { color: #666; }
.payment-method { border: 2px solid #e0e0e0; border-radius: 12px; padding: 20px; margin-bottom: 16px; cursor: pointer; transition: all 0.3s ease; }
.payment-method:hover { border-color: #0b4499; transform: translateY(-2px); box-shadow: 0 4px 12px rgba(11,68,153,0.15); }
.payment-method.selected { border-color: #0b4499; background: linear-gradient(135deg, #f0f4ff, #e8f2ff); }
.payment-method-icon { width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 16px; }
.stripe-icon { background: linear-gradient(135deg, #635bff, #4f46e5); color: white; }
.credit-icon { background: linear-gradient(135deg, #10b981, #059669); color: white; }
.breakdown-container { border: 1px solid #e0e0e0; border-radius: 12px; overflow: hidden; }
.breakdown-item { padding: 16px 20px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #f0f0f0; }
.breakdown-item:last-child { border-bottom: none; }
.breakdown-item.total { background: linear-gradient(135deg, #0b4499, #1e5bb8); color: white; font-weight: 600; font-size: 18px; }
.breakdown-item.discount { color: #10b981; }
.breakdown-item.credit { color: #0ea5e9; }
.recurring-info { background: linear-gradient(135deg, #ecfdf5, #d1fae5); border: 1px solid #10b981; border-radius: 12px; padding: 20px; margin-top: 20px; }
.recurring-schedule { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-top: 12px; }
.schedule-item { background: white; padding: 12px; border-radius: 8px; text-align: center; }
.pay-button { background: linear-gradient(135deg, #0b4499, #1e5bb8); border: none; border-radius: 12px; padding: 16px 32px; color: white; font-weight: 600; font-size: 16px; width: 100%; transition: all 0.3s ease; }
.pay-button:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(11,68,153,0.3); }
.security-info { display: flex; align-items: center; justify-content: center; gap: 8px; margin-top: 16px; color: #666; font-size: 14px; }
.payment-loader { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999; display: none; align-items: center; justify-content: center; }
.loader-spinner { width: 60px; height: 60px; border: 4px solid #f3f3f3; border-top: 4px solid #0b4499; border-radius: 50%; animation: spin 1s linear infinite; }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
@media (max-width: 768px) { .checkout-content { grid-template-columns: 1fr; } }
</style>
@endsection

@section('content')
<div class="checkout-container">
    <!-- Header -->
    <div class="checkout-header">
        <h2 class="mb-0">Complete Your Payment</h2>

    </div>

    <!-- Main Content -->
    <div class="checkout-content">
        <!-- Payment Methods -->
        <div class="payment-section">
            <h5 class="mb-4">Payment Method</h5>

            @if($paymentData['payment_type'] !== 'creditPayment')
            <div class="payment-method selected" data-method="stripe">
                <div class="d-flex align-items-center mb-3">
                    <div class="payment-method-icon stripe-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">Credit/Debit Card</h6>
                        <small class="text-muted">Visa, Mastercard, American Express</small>
                        <div class="d-flex gap-2 mt-2">
                            <i class="fab fa-cc-visa text-primary" style="font-size: 24px;"></i>
                            <i class="fab fa-cc-mastercard text-warning" style="font-size: 24px;"></i>
                            <i class="fab fa-cc-amex text-info" style="font-size: 24px;"></i>
                        </div>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
                
                <!-- Stripe Card Element -->
                <div id="card-element" style="padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                    <!-- Stripe Elements will create form elements here -->
                </div>
                <div id="card-errors" role="alert" style="color: #dc3545; margin-top: 8px; font-size: 14px;"></div>
            </div>
            @endif



            @if($paymentData['payment_type'] === 'recurringPayments')
            <div class="recurring-info">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-calendar-alt me-2 text-success"></i>
                    <h6 class="mb-0">Recurring Payment Schedule</h6>
                </div>
                <p class="mb-3">After today's payment, you'll be automatically charged:</p>
                <div class="recurring-schedule">
                    <div class="schedule-item">
                        <strong>${{ number_format($paymentData['monthly_payment'], 2) }}</strong>
                        <small class="d-block text-muted">Monthly Amount</small>
                    </div>
                    <div class="schedule-item">
                        <strong>{{ $paymentData['installment_months'] }} months</strong>
                        <small class="d-block text-muted">Duration</small>
                    </div>
                    <div class="schedule-item">
                        <strong>{{ date('M j, Y', strtotime('first day of next month')) }}</strong>
                        <small class="d-block text-muted">Next Payment</small>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <!-- Program Card -->
            <div class="program-card">
                <div class="program-image">
                    <i class="fas fa-{{ $program->sport === 'Basketball' ? 'basketball-ball' : 'futbol' }}"></i>
                </div>
                <div class="program-details">
                    <h6>{{ $program->name }}</h6>
                    <small>{{ $program->location }}</small>
                    <small class="d-block">{{ $program->sport }} • {{ $program->gender }}</small>
                </div>
            </div>

            <!-- Bill Details -->
            <h6 class="mb-3">Bill Details</h6>
            <div class="breakdown-container">
                <div class="breakdown-item">
                    <span>Program Cost</span>
                    <span>${{ number_format($paymentData['original_amount'], 2) }}</span>
                </div>

                @if($paymentData['discount_amount'] > 0)
                <div class="breakdown-item discount">
                    <span>
                        <i class="fas fa-tag me-1"></i>
                        Coupon ({{ $paymentData['applied_coupon']['code'] ?? '' }})
                    </span>
                    <span>-${{ number_format($paymentData['discount_amount'], 2) }}</span>
                </div>
                @endif

                @if(isset($paymentData['used_credit']) && $paymentData['used_credit'] > 0)
                <div class="breakdown-item credit">
                    <span>
                        <i class="fas fa-wallet me-1"></i>
                        Credit Applied
                    </span>
                    <span>-${{ number_format($paymentData['used_credit'], 2) }}</span>
                </div>
                @endif

                @if($paymentData['payment_type'] === 'recurringPayments')
                <div class="breakdown-item">
                    <span>Down Payment (Today)</span>
                    <span>${{ number_format($paymentData['down_payment'], 2) }}</span>
                </div>
                <div class="breakdown-item">
                    <span>Monthly Payment × {{ $paymentData['installment_months'] }}</span>
                    <span>${{ number_format($paymentData['monthly_payment'], 2) }} each</span>
                </div>
                @endif

                <div class="breakdown-item total">
                    <span>{{ $paymentData['payment_type'] === 'recurringPayments' ? 'Pay Today' : 'Total Amount' }}</span>
                    <span>${{ number_format($paymentData['payment_type'] === 'recurringPayments' ? $paymentData['down_payment'] : $paymentData['paidAmount'], 2) }}</span>
                </div>
            </div>

            <!-- Payment Button -->
            <div class="mt-4">
                @if($paymentData['payment_type'] === 'creditPayment')
                <button class="pay-button" onclick="processPayment()">
                    <i class="fas fa-check me-2"></i>
                    Complete Registration
                </button>
                @else
                <button class="pay-button" onclick="processStripePayment()">
                    <i class="fas fa-lock me-2"></i>
                    Pay ${{ number_format($paymentData['payment_type'] === 'recurringPayments' ? $paymentData['down_payment'] : $paymentData['paidAmount'], 2) }}
                </button>
                @endif

                <div class="security-info">
                    <i class="fas fa-shield-alt text-success"></i>
                    <span>Secured by 256-bit SSL encryption</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Loader -->
<div class="payment-loader" id="paymentLoader">
    <div class="loader-spinner"></div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
const stripe = Stripe('{{ env('STRIPE_KEY') }}');
const elements = stripe.elements();
const paymentData = @json($paymentData);

// Create and mount card element
@if($paymentData['payment_type'] !== 'creditPayment')
const cardElement = elements.create('card', {
    style: {
        base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': { color: '#aab7c4' }
        }
    }
});
cardElement.mount('#card-element');

cardElement.on('change', ({error}) => {
    const displayError = document.getElementById('card-errors');
    displayError.textContent = error ? error.message : '';
});
@endif

function showLoader() {
    document.getElementById('paymentLoader').style.display = 'flex';
}

function hideLoader() {
    document.getElementById('paymentLoader').style.display = 'none';
}

async function processStripePayment() {
    const button = document.querySelector('.pay-button');
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    showLoader();
    
    try {
        const { error, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement
        });

        if (error) {
            document.getElementById('card-errors').textContent = error.message;
            button.disabled = false;
            const payAmount = paymentData.payment_type === 'recurringPayments' ? paymentData.down_payment : paymentData.paidAmount;
            button.innerHTML = `<i class="fas fa-lock me-2"></i>Pay $${payAmount.toFixed(2)}`;
            hideLoader();
            return;
        }

        const response = await fetch('{{ route("guardian.create-payment-intent") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(paymentData)
        });

        const { client_secret } = await response.json();

        const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(client_secret, {
            payment_method: paymentMethod.id
        });

        if (confirmError) {
            document.getElementById('card-errors').textContent = confirmError.message;
            button.disabled = false;
            const payAmount = paymentData.payment_type === 'recurringPayments' ? paymentData.down_payment : paymentData.paidAmount;
            button.innerHTML = `<i class="fas fa-lock me-2"></i>Pay $${payAmount.toFixed(2)}`;
            hideLoader();
        } else {
            window.location.href = '{{ route("guardian.payment-success") }}?payment_intent=' + paymentIntent.id;
        }
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('card-errors').textContent = 'Payment processing failed';
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-lock me-2"></i>Pay ${{ number_format($paymentData["paidAmount"], 2) }}';
        hideLoader();
    }
}

async function processPayment() {
    const button = document.querySelector('.pay-button');
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    showLoader();
    
    try {
        const response = await fetch('{{ route("guardian.process-credit-payment") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(paymentData)
        });

        const data = await response.json();
        if (data.success) {
            window.location.href = '{{ route("guardian.payment-success") }}';
        } else {
            alert('Payment failed: ' + data.message);
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-check me-2"></i>Complete Registration';
            hideLoader();
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Payment processing failed');
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-check me-2"></i>Complete Registration';
        hideLoader();
    }
}
</script>
@endsection
