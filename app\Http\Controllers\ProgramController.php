<?php

namespace App\Http\Controllers;

use App\Models\AdminInvitesPlayerForProgram;
use App\Models\Program;
use App\Models\User;
use App\Models\EarlyBirdPricing;
use App\Models\UserProgram;
use App\Models\ProgramRegistration;
use App\Models\PlayerProgram;
use App\Models\Team;
use App\Models\TeamCoach;
use App\Models\TeamProgram;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;


class ProgramController extends Controller
{


    public function index(Request $request)
    {
        $gender = $request->input('gender');
        $query = Program::query();


        if ($gender) {
            $query->where('gender', $gender);
        }


        $query->where('registration_closing_date', '>=', now()->toDateString());




        $programs = $query->paginate(10);

        return view('allPrograms', compact('programs'));
    }


    public function show(Request $request, $programSlug)
    {

        $program = Program::where('slug', $programSlug)->first();


        if (!$program) {
            return redirect()->back()->withErrors(['error' => 'Program not found.']);
        }


        $registrationClosed = Carbon::parse($program->registration_closing_date)->toDateString() < Carbon::today()->toDateString();


        if ($registrationClosed) {
            return view('program', compact('program', 'registrationClosed'));
        }


        $user = auth()->user();

        if (!$user) {
            return view('program', compact('program'))->with('isGuest', true);
        }


        $amount = $user->calculateAmountForProgram($program->id);


        $user->load('roles');
        $roles = $user->roles;
        $isGuardian = $roles->contains('name', 'guardian');
        $isCoach = $roles->contains('name', 'coach');


        if ($isGuardian) {
            $playerIds = User::where('parent_id', $user->id)
                ->withRole('player')
                ->pluck('id')
                ->toArray();


            $invitedPlayerId = $request->query('invited_player');


            if ($invitedPlayerId) {

                $adminInvitation = AdminInvitesPlayerForProgram::where('user_id', $invitedPlayerId)
                    ->where('program_id', $program->id)
                    ->where('status', 'pending')
                    ->first();

                if (!$adminInvitation) {
                    return redirect()->back()->withErrors(['error' => 'No valid invitation found for the specified player.']);
                }


                $invitedPlayer = User::find($invitedPlayerId);
                if (!$invitedPlayer || ($invitedPlayer->primary_parent_id != $user->id && $invitedPlayer->primary_parent_id != $user->primary_parent_id)) {
                    return redirect()->back()->withErrors(['error' => 'You are not authorized to register this player.']);
                }
            }

            return view('program', compact('program', 'user', 'playerIds', 'amount', 'invitedPlayerId'));
        }

        if ($isCoach) {
            return view('program', compact('program', 'user', 'amount'));
        }

        return redirect()->back()->withErrors(['error' => 'The program is only for a coach or guardian.']);
    }




    public function store(Program $program)
    {


        $user = auth()->user();

        if (!$user) {
            return redirect()->route('guardian.signup')->with('message', 'signup first');
        }
        $amount = $user->calculateAmountForProgram($program->id);
        ProgramRegistration::create([
            'user_id' => $user->id,
            'program_id' => $program->id,
            'amount' => $amount,
        ]);

        return redirect()->back()->with('success', 'Registered Successfully.');
    }



    public function registerTeam($programSlug, $selectedTeamId)
    {


        try {

            $user = auth()->user();

            $program = Program::where('slug', $programSlug)->first();


            $team = Team::findOrFail($selectedTeamId);
            $amount = $user->calculateAmountForProgram($program->id);

            $registerTeamForProgram = ProgramRegistration::create([
                'user_id' => $user->id,
                'program_id' => $program->id,
                'team_id' => $team->id,
                'amount' => $amount,
                'is_paid' => true
            ]);

            $teamProgram = TeamProgram::create([
                'team_id' => $selectedTeamId,
                'program_id' => $program->id,
            ]);

            return response()->json([
                'success' => true,
                'type' => 'team_registration',
                'message' => 'Registered team successfully',
                'redirectUrl' => route('coach.dashboard'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while registering the team: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function selectPlayerOrSelectTeam(Request $request, $programSlug)
    {
        $user = auth()->user();
        $program = Program::where('slug', $programSlug)->first();
        $programId = $program->id;

        // Check if there's a specific invited player
        $invitedPlayerId = $request->query('invited_player');

        $programType = $program->type;

        // If user is not authenticated, redirect based on program type
        if (!$user) {
            if ($programType == 'Team') {
                return redirect()->route('coach.signup');
            } elseif ($programType == 'Individual' || $programType == 'AAU') {
                return redirect()->route('guardian.signup');
            }
        }

        // Check user roles
        $user->load('roles');
        $roles = $user->roles;
        $isGuardian = $roles->contains('name', 'guardian');
        $isCoach = $roles->contains('name', 'coach');
        $isBoth = $isGuardian && $isCoach;


        if ($isBoth) {

            if ($user->current_role == "coach" && ($programType == 'Individual' || $programType == "AAU")) {


                return response()->json([
                    'success' => false,
                    'message' => 'Switch to Guardian Mode to join this program.'
                ]);
            }

            $invitedPlayer = User::find($invitedPlayerId);




            if ($user->current_role == "guardian" && ($programType == 'Individual' || $programType == "AAU")) {
                // Get all players
                $players = User::where('primary_parent_id', $user->id)
                    ->orWhere('primary_parent_id', $user->primary_parent_id)
                    ->withRole('player')
                    ->get(['id', 'firstName', 'lastName', 'email', 'birthDate', 'gender', 'grade']);



                // Filter eligible players
                $players = $players->filter(function ($player) use ($program) {
                    return $this->isPlayerEligible($player, $program);
                });

                if ($players->isEmpty()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You do not have any players yet. Please add players to register.',
                    ]);
                }


                $adminInvitations = AdminInvitesPlayerForProgram::whereIn('user_id', $players->pluck('id'))
                    ->where('program_id', $programId)
                    ->where('status', 'pending')
                    ->with('user')
                    ->get();


                if ($invitedPlayerId && $adminInvitations->isNotEmpty()) {
                    $specificInvitation = $adminInvitations->firstWhere('user_id', $invitedPlayerId);
                    if ($specificInvitation) {

                        $invitedPlayer = User::find($invitedPlayerId);
                        if ($invitedPlayer && ($invitedPlayer->primary_parent_id == $user->id || $invitedPlayer->primary_parent_id == $user->primary_parent_id || $invitedPlayer->parent_id == $user->id || $invitedPlayer->parent_id == $user->primary_parent_id)) {

                            return response()->json([
                                'success' => true,
                                'type' => 'auto_select_player',
                                'players' => [[
                                    'id' => $specificInvitation->user->id,
                                    'firstName' => $specificInvitation->user->firstName,
                                    'lastName' => $specificInvitation->user->lastName,
                                    'email' => $specificInvitation->user->email,
                                ]],
                            ]);
                        } else {

                            return response()->json([
                                'success' => false,
                                'message' => 'You are not authorized to register this player.',
                            ]);
                        }
                    } else {

                        return response()->json([
                            'success' => false,
                            'message' => 'No valid invitation found for the specified player.',
                        ]);
                    }
                }

                if ($adminInvitations->isNotEmpty()) {

                    $invitedPlayers = $adminInvitations->map(function ($invitation) {
                        return [
                            'id' => $invitation->user->id,
                            'firstName' => $invitation->user->firstName,
                            'lastName' => $invitation->user->lastName,
                            'email' => $invitation->user->email,
                            'invitation_id' => $invitation->id,
                        ];
                    });

                    return response()->json([
                        'success' => false,
                        'type' => 'admin_invitation',
                        'message' => 'You have pending admin invitations for this program. Please check your dashboard for invitation details.',
                        'invited_players' => $invitedPlayers,
                    ]);
                }

                $alreadyRegisteredPlayerIds = ProgramRegistration::whereIn('player_id', $players->pluck('id'))
                    ->where('program_id', $programId)
                    ->get();


                $unpaidRegistrations = $alreadyRegisteredPlayerIds->filter(function ($registration) {
                    return !$registration->is_paid;
                });


                ProgramRegistration::whereIn('id', $unpaidRegistrations->pluck('id'))->delete();


                PlayerProgram::whereIn('player_id', $unpaidRegistrations->pluck('player_id'))
                    ->where('program_id', $programId)
                    ->delete();


                $registeredAndPaidPlayerIds = $alreadyRegisteredPlayerIds
                    ->filter(fn($registration) => $registration->is_paid)
                    ->pluck('player_id')
                    ->toArray();


                $availablePlayers = $players->reject(function ($player) use ($registeredAndPaidPlayerIds) {
                    return in_array($player->id, $registeredAndPaidPlayerIds);
                });


                if ($availablePlayers->isEmpty()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'All of your players are already registered for this program.',
                    ]);
                }

                $playerArray = $availablePlayers->map(function ($player) {
                    return [
                        'id' => $player->id,
                        'firstName' => $player->firstName,
                        'lastName' => $player->lastName,
                        'email' => $player->email,
                    ];
                })->values();

                return response()->json([
                    'success' => true,
                    'type' => 'player_list',
                    'players' => $playerArray,
                ]);
            }

            if ($user->current_role == 'guardian' && $programType == 'Team') {

                return response()->json([
                    'success' => false,
                    'message' => 'Switch to Coach Mode to join this program',
                ]);
            }

            if ($user->current_role == "coach" && $programType == "Team") {

                $teamIds = TeamCoach::where('coach_id', $user->id)
                    ->pluck('team_id');


                $teams = Team::whereIn('id', $teamIds)->get();

                $registeredTeamIds = TeamProgram::where('program_id', $program->id)
                    ->pluck('team_id')
                    ->toArray();

                $teamsRegisteredInAnyProgram = TeamProgram::with('program')->get(['team_id', 'program_id']);

                $unregisteredTeams = $teams->reject(function ($team) use ($registeredTeamIds, $teamsRegisteredInAnyProgram) {
                    if (in_array($team->id, $registeredTeamIds)) {
                        return true;
                    }

                    $teamProgram = $teamsRegisteredInAnyProgram->firstWhere('team_id', $team->id);
                    if ($teamProgram) {
                        $programEndDate = $teamProgram->program->end_date;
                        if ($programEndDate && now()->lt($programEndDate)) {
                            return true;
                        }
                    }

                    return false;
                });

                if ($unregisteredTeams->isEmpty()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'All of your teams are already registered for this or another program.',
                    ]);
                }

                $teamArray = $unregisteredTeams->map(function ($team) {
                    return [
                        'id' => $team->id,
                        'name' => $team->name,
                    ];
                })->values();

                return response()->json([
                    'success' => true,
                    'type' => 'team_list',
                    'teams' => $teamArray,
                ]);
            }
        }

        if ($isGuardian && $programType == 'Team') {
            return response()->json([
                'success' => false,
                'message' => 'This program requires a coach. Please sign up as a coach to proceed.',
            ]);
        }

        if ($isCoach && $programType == 'Team') {

            $teamIds = TeamCoach::where('coach_id', $user->id)
                ->pluck('team_id');


            $teams = Team::whereIn('id', $teamIds)->get();


            $registeredTeamIds = TeamProgram::where('program_id', $program->id)
                ->pluck('team_id')
                ->toArray();

            $teamsRegisteredInAnyProgram = TeamProgram::with('program')->get(['team_id', 'program_id']);

            $unregisteredTeams = $teams->reject(function ($team) use ($registeredTeamIds, $teamsRegisteredInAnyProgram) {
                if (in_array($team->id, $registeredTeamIds)) {
                    return true;
                }

                $teamProgram = $teamsRegisteredInAnyProgram->firstWhere('team_id', $team->id);
                if ($teamProgram) {
                    $programEndDate = $teamProgram->program->end_date;
                    if ($programEndDate && now()->lt($programEndDate)) {
                        return true;
                    }
                }

                return false;
            });

            $teamArray = $unregisteredTeams->map(function ($team) {
                return [
                    'id' => $team->id,
                    'name' => $team->name,
                ];
            })->values();

            return response()->json([
                'success' => true,
                'type' => 'team_list',
                'teams' => $teamArray,
            ]);
        }


        if ($isCoach) {

            if ($programType == 'Individual' || $programType == "AAU") {
                return response()->json([
                    'success' => false,
                    'message' => 'This program is not for teams. Please sign up as a Guardian to proceed.',
                ]);
            }
        }





        $players = User::where('primary_parent_id', $user->id)
            ->orWhere('primary_parent_id', $user->primary_parent_id)
            ->withRole('player')
            ->get(['id', 'firstName', 'lastName', 'email', 'birthDate', 'gender', 'grade']);




        // Filter eligible players

        if (!$invitedPlayerId) {
            $players = $players->filter(function ($player) use ($program) {
                return $this->isPlayerEligible($player, $program);
            });
        }

        if ($players->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'None of the players are eligible to join this program.',
            ]);
        }


        $adminInvitations = AdminInvitesPlayerForProgram::whereIn('user_id', $players->pluck('id'))
            ->where('program_id', $programId)
            ->where('status', 'pending')
            ->with('user')
            ->get();


        if ($invitedPlayerId && $adminInvitations->isNotEmpty()) {
            $specificInvitation = $adminInvitations->firstWhere('user_id', $invitedPlayerId);
            if ($specificInvitation) {

                $invitedPlayer = User::find($invitedPlayerId);
                if ($invitedPlayer && ($invitedPlayer->primary_parent_id == $user->id || $invitedPlayer->primary_parent_id == $user->primary_parent_id)) {

                    return response()->json([
                        'success' => true,
                        'type' => 'auto_select_player',
                        'players' => [[
                            'id' => $specificInvitation->user->id,
                            'firstName' => $specificInvitation->user->firstName,
                            'lastName' => $specificInvitation->user->lastName,
                            'email' => $specificInvitation->user->email,
                        ]],
                    ]);
                } else {

                    return response()->json([
                        'success' => false,
                        'message' => 'You are not authorized to register this player.',
                    ]);
                }
            } else {

                return response()->json([
                    'success' => false,
                    'message' => 'No valid invitation found for the specified player.',
                ]);
            }
        }

        if ($adminInvitations->isNotEmpty()) {

            $invitedPlayers = $adminInvitations->map(function ($invitation) {
                return [
                    'id' => $invitation->user->id,
                    'firstName' => $invitation->user->firstName,
                    'lastName' => $invitation->user->lastName,
                    'email' => $invitation->user->email,
                    'invitation_id' => $invitation->id,
                ];
            });

            return response()->json([
                'success' => false,
                'type' => 'admin_invitation',
                'message' => 'You have pending admin invitations for this program. Please check your dashboard for invitation details.',
                'invited_players' => $invitedPlayers,
            ]);
        }

        $alreadyRegisteredPlayerIds = ProgramRegistration::whereIn('player_id', $players->pluck('id'))
            ->where('program_id', $programId)
            ->get();


        $unpaidRegistrations = $alreadyRegisteredPlayerIds->filter(function ($registration) {
            return !$registration->is_paid;
        });


        ProgramRegistration::whereIn('id', $unpaidRegistrations->pluck('id'))->delete();


        PlayerProgram::whereIn('player_id', $unpaidRegistrations->pluck('player_id'))
            ->where('program_id', $programId)
            ->delete();


        $registeredAndPaidPlayerIds = $alreadyRegisteredPlayerIds
            ->filter(fn($registration) => $registration->is_paid)
            ->pluck('player_id')
            ->toArray();


        $availablePlayers = $players->reject(function ($player) use ($registeredAndPaidPlayerIds) {
            return in_array($player->id, $registeredAndPaidPlayerIds);
        });

        if ($availablePlayers->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'All of your players are already registered for this program.',
            ]);
        }

        $playerArray = $availablePlayers->map(function ($player) {
            return [
                'id' => $player->id,
                'firstName' => $player->firstName,
                'lastName' => $player->lastName,
                'email' => $player->email,
            ];
        })->values();

        return response()->json([
            'success' => true,
            'type' => 'player_list',
            'players' => $playerArray,
        ]);
    }

    /**
     * Check if a player is eligible for a program based on age, gender, grade, and birth date restrictions
     */
    private function isPlayerEligible($player, $program)
    {

        if ($program->birth_date_cutoff && $player->birthDate < $program->birth_date_cutoff) {
            return false;
        }



        $age = Carbon::parse($player->birthDate)->age;
        if ($program->age_restriction_from && $program->age_restriction_to) {
            if ($age < $program->age_restriction_from || $age > $program->age_restriction_to) {
                return false;
            }
        }


        $playerGender = strtolower($player->gender);
        $programGender = strtolower($program->gender);
        if ($programGender !== 'coed') {
            if ($playerGender === 'boy' && $programGender !== 'boys') {
                return false;
            }
            if ($playerGender === 'girl' && $programGender !== 'girls') {
                return false;
            }
        }

        // Check grade restrictions
        // if ($program->grade) {
        //     $grades = explode(',', $program->grade);
        //     if (!in_array($player->grade, $grades)) {
        //         return false;
        //     }
        // }

        return true;
    }
}
