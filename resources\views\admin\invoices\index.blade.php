@extends('layouts.app')

@section('title', 'Invoices Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Invoices Management</h4>
                    <div>
                        <button class="btn btn-primary" onclick="syncInvoices()">
                            <i class="bi bi-arrow-clockwise"></i> Sync from Stripe
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="status-filter">
                                <option value="">All Statuses</option>
                                <option value="draft">Draft</option>
                                <option value="open">Open</option>
                                <option value="paid">Paid</option>
                                <option value="void">Void</option>
                                <option value="uncollectible">Uncollectible</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="payment-type-filter">
                                <option value="">All Payment Types</option>
                                <option value="full">Full Payment</option>
                                <option value="split">Split Payment</option>
                                <option value="recurring">Recurring</option>
                                <option value="outstanding_to_recurring">Outstanding to Recurring</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-from" placeholder="From Date">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-to" placeholder="To Date">
                        </div>
                    </div>

                    <!-- Invoices Table -->
                    <div id="invoices-container">
                        @include('admin.invoices.partialInvoicesList', ['invoices' => $invoices])
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Refund</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="refundForm">
                    <div class="mb-3">
                        <label class="form-label">Invoice Amount</label>
                        <input type="text" class="form-control" id="invoice-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Already Refunded</label>
                        <input type="text" class="form-control" id="refunded-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Refundable Amount</label>
                        <input type="text" class="form-control" id="refundable-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Refund Amount *</label>
                        <input type="number" class="form-control" id="refund-amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Reason *</label>
                        <select class="form-select" id="refund-reason" required>
                            <option value="">Select Reason</option>
                            <option value="duplicate">Duplicate</option>
                            <option value="fraudulent">Fraudulent</option>
                            <option value="requested_by_customer">Requested by Customer</option>
                            <option value="expired_uncaptured_charge">Expired Uncaptured Charge</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="refund-notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="processRefund()">Process Refund</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
let currentInvoiceId = null;

function syncInvoices() {
    fetch('{{ route("admin.invoices.sync") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while syncing invoices.');
    });
}

function showRefundModal(invoiceId, amount, refundedAmount) {
    currentInvoiceId = invoiceId;
    const refundableAmount = amount - refundedAmount;

    document.getElementById('invoice-amount').value = '$' + parseFloat(amount).toFixed(2);
    document.getElementById('refunded-amount').value = '$' + parseFloat(refundedAmount).toFixed(2);
    document.getElementById('refundable-amount').value = '$' + parseFloat(refundableAmount).toFixed(2);
    document.getElementById('refund-amount').max = refundableAmount;
    document.getElementById('refund-amount').value = refundableAmount;

    new bootstrap.Modal(document.getElementById('refundModal')).show();
}

function processRefund() {
    const amount = document.getElementById('refund-amount').value;
    const reason = document.getElementById('refund-reason').value;
    const notes = document.getElementById('refund-notes').value;

    if (!amount || !reason) {
        alert('Please fill in all required fields.');
        return;
    }

    fetch(`/admin/invoices/${currentInvoiceId}/refund`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: parseFloat(amount),
            reason: reason,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('refundModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing the refund.');
    });
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filters = ['status-filter', 'payment-type-filter', 'date-from', 'date-to'];

    filters.forEach(filterId => {
        document.getElementById(filterId).addEventListener('change', loadInvoices);
    });
});

function loadInvoices() {
    const params = new URLSearchParams();

    const status = document.getElementById('status-filter').value;
    const paymentType = document.getElementById('payment-type-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;

    if (status) params.append('status', status);
    if (paymentType) params.append('payment_type', paymentType);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    fetch(`{{ route('admin.invoices.index') }}?${params.toString()}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('invoices-container').innerHTML = data.invoices;
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endpush
