<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - {{ $paymentData['program']->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .success-container {
            max-width: 600px;
            margin: 2rem auto;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-container">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    
                    <h2 class="text-success mb-4">Payment Successful!</h2>
                    
                    <div class="alert alert-success">
                        <h5>Thank you for your payment</h5>
                        <p class="mb-0">Your payment has been processed successfully.</p>
                    </div>

                    <!-- Payment Details -->
                    <div class="row text-start mt-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-primary"></i> Payment Details</h6>
                            <p class="mb-1"><strong>Program:</strong> {{ $paymentData['program']->name }}</p>
                            <p class="mb-1"><strong>Players:</strong> 
                                @foreach($paymentData['player_names'] as $name)
                                    {{ $name }}@if(!$loop->last), @endif
                                @endforeach
                            </p>
                            <p class="mb-1"><strong>Requested by:</strong> {{ $paymentData['requesting_user']->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-dollar-sign text-success"></i> Payment Summary</h6>
                            <p class="mb-1"><strong>Amount Paid:</strong> ${{ number_format($paymentData['amount_paid'], 2) }}</p>
                            <p class="mb-1"><strong>Payment Date:</strong> {{ $paymentData['completed_at']->format('M j, Y \a\t g:i A') }}</p>
                            <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> What's Next?</h6>
                        <p class="mb-0">
                            The program organizer ({{ $paymentData['requesting_user']->name }}) has been notified of your payment. 
                            You should receive a confirmation email shortly with program details and next steps.
                        </p>
                    </div>

                    <!-- Contact Information -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-question-circle"></i> 
                            Questions? Contact {{ $paymentData['requesting_user']->name }} at 
                            <a href="mailto:{{ $paymentData['requesting_user']->email }}">{{ $paymentData['requesting_user']->email }}</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
