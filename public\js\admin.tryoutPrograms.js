let currentProgramId = null;
let availablePlayers = [];
let playerSelect = null;

// Initialize Select2 with custom styling
function initializePlayerSelect() {
    try {
        playerSelect = $('#playerSelect').select2({
            data: availablePlayers.map(player => ({
                id: player.id,
                text: `${player.name} (${player.email || 'No email'})`
            })),
            placeholder: 'Invite players...',
            allowClear: true,
            multiple: true,
            width: '100%',
            templateResult: formatPlayerOption,
            templateSelection: formatPlayerSelection
        });

        updateSendButton();

        // Handle selection changes
        playerSelect.on('change', updateSendButton);
    } catch (e) {
        console.error('Error initializing Select2:', e);
        playerSelect = null;
        const helpText = document.getElementById('playerSelectHelp');
        helpText.textContent = 'Error initializing player selection. Please try again.';
        helpText.classList.add('text-danger');
    }
}

// Custom format for dropdown options
function formatPlayerOption(player) {
    if (!player.id) return player.text;

    return $(`<div class="d-flex align-items-center py-1">
        <div class="player-icon rounded-circle bg-light d-flex align-items-center justify-content-center me-2"
             style="width: 32px; height: 32px">
            <i class="bi bi-person"></i>
        </div>
        <div>
            <div class="fw-bold">${player.text.split('(')[0]}</div>
            <small class="text-muted">${player.text.split('(')[1]?.replace(')', '')}</small>
        </div>
    </div>`);
}

// Custom format for selected items
function formatPlayerSelection(player) {
    if (!player.id) return player.text;
    return player.text.split('(')[0];
}

// Update send button state based on selection
function updateSendButton() {
    const sendButton = document.getElementById('sendInvitationBtn');
    const selectedPlayers = playerSelect ? playerSelect.val() : [];

    if (selectedPlayers && selectedPlayers.length > 0) {
        sendButton.disabled = false;
        sendButton.style.opacity = '1';
        sendButton.style.cursor = 'pointer';
    } else {
        sendButton.disabled = true;
        sendButton.style.opacity = '0.6';
        sendButton.style.cursor = 'not-allowed';
    }
}

// Open the invite modal
function openRegularInviteModal(programId) {
    currentProgramId = programId;
    const modal = new bootstrap.Modal(document.getElementById('regularInviteModal'));
    modal.show();
    loadAvailablePlayers();
}

// Load available players from the server
function loadAvailablePlayers() {
    const modalLoading = document.getElementById('modalLoading');
    const modalContent = document.getElementById('modalContent');

    modalLoading.style.display = 'block';
    modalContent.style.display = 'none';

    fetch(`/admin/api/available-players-for-tryout/${currentProgramId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availablePlayers = data.players;
                initializePlayerSelect();
            } else {
                console.error('Failed to load players:', data.message);
                availablePlayers = [];
                initializePlayerSelect();
            }
        })
        .catch(error => {
            console.error('Error loading players:', error);
            const helpText = document.getElementById('playerSelectHelp');
            helpText.textContent = 'Failed to load players. Please try again.';
            helpText.classList.add('text-danger');
        })
        .finally(() => {
            modalLoading.style.display = 'none';
            modalContent.style.display = 'block';
        });
}

// Send invitations to selected players
function sendInvitation() {
    const selectedPlayers = playerSelect.val();
    if (!selectedPlayers || selectedPlayers.length === 0) return;

    const sendBtnText = document.getElementById('sendBtnText');
    const sendBtnLoading = document.getElementById('sendBtnLoading');
    const sendInvitationBtn = document.getElementById('sendInvitationBtn');

    sendBtnText.style.display = 'none';
    sendBtnLoading.style.display = 'inline';
    sendInvitationBtn.disabled = true;

    fetch('/admin/api/send-tryout-program-invitation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            program_id: currentProgramId,
            player_ids: selectedPlayers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Success', 'Invitations sent successfully!', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('regularInviteModal'));
            modal.hide();
            window.location.reload();
        } else {
            throw new Error(data.message || 'Failed to send invitations');
        }
    })
    .catch(error => {
        console.error('Error sending invitations:', error);
        showNotification('Error', error.message || 'An error occurred while sending the invitations', 'error');
    })
    .finally(() => {
        sendBtnText.style.display = 'inline';
        sendBtnLoading.style.display = 'none';
        sendInvitationBtn.disabled = false;
    });
}

// Show notification toast
function showNotification(title, message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center border-0 show`;
    toast.style.position = 'fixed';
    toast.style.top = '1rem';
    toast.style.right = '1rem';
    toast.style.zIndex = '1050';
    toast.style.minWidth = '300px';

    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
    toast.classList.add(bgClass, 'text-white');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// Clean up when modal is closed
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('regularInviteModal').addEventListener('hidden.bs.modal', function () {
        currentProgramId = null;
        availablePlayers = [];

        if (playerSelect && typeof playerSelect.destroy === 'function') {
            try {
                playerSelect.destroy();
            } catch (e) {
                console.log('Error destroying Select2:', e);
            }
            playerSelect = null;
        }

        document.getElementById('playerSelect').innerHTML = '';
        updateSendButton();
    });
});
