<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProgramCoupon extends Model
{
    protected $casts = [
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $fillable = [
        'program_id',
        'code',
        'discount_type',
        'discount_value',
        'usage_limit',
        'valid_from',
        'valid_until',
        'is_active',
        'description',
    ];
}
