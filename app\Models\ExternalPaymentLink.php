<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ExternalPaymentLink extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'payment_transaction_id',
        'requesting_user_id',
        'program_id',
        'external_email',
        'amount_to_pay',
        'player_ids',
        'player_names',
        'status',
        'sent_at',
        'opened_at',
        'payment_completed_at',
        'expires_at',
        'stripe_payment_intent_id',
        'stripe_charge_id',
        'payment_metadata',
        'open_count',
        'access_logs',
    ];

    protected $casts = [
        'player_ids' => 'array',
        'player_names' => 'array',
        'amount_to_pay' => 'decimal:2',
        'sent_at' => 'datetime',
        'opened_at' => 'datetime',
        'payment_completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'payment_metadata' => 'array',
        'access_logs' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($link) {
            if (empty($link->token)) {
                $link->token = 'EPL_' . strtoupper(Str::random(32));
            }

            if (empty($link->expires_at)) {
                $link->expires_at = Carbon::now()->addDays(7); // Default 7 days expiry
            }
        });
    }

    // Relationships
    public function paymentTransaction(): BelongsTo
    {
        return $this->belongsTo(PaymentTransaction::class);
    }

    public function requestingUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requesting_user_id');
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    // Helper Methods
    public function isExpired(): bool
    {
        return Carbon::now()->isAfter($this->expires_at);
    }

    public function isActive(): bool
    {
        return !$this->isExpired() && in_array($this->status, ['created', 'sent', 'opened']);
    }

    public function getPaymentUrl(): string
    {
        return route('external.payment.show', ['token' => $this->token]);
    }

    public function markAsOpened(string $ipAddress = null): void
    {
        $this->increment('open_count');

        if ($this->opened_at === null) {
            $this->opened_at = Carbon::now();
            $this->status = 'opened';
        }

        // Log access
        $accessLogs = $this->access_logs ?? [];
        $accessLogs[] = [
            'timestamp' => Carbon::now()->toISOString(),
            'ip_address' => $ipAddress,
            'action' => 'opened'
        ];

        $this->access_logs = $accessLogs;
        $this->save();
    }

    public function markAsPaymentCompleted(string $stripeChargeId = null): void
    {
        $this->status = 'payment_completed';
        $this->payment_completed_at = Carbon::now();

        if ($stripeChargeId) {
            $this->stripe_charge_id = $stripeChargeId;
        }

        $this->save();
    }

    public function canProcessPayment(): bool
    {
        return $this->isActive() && !in_array($this->status, ['payment_completed', 'cancelled']);
    }

    /**
     * Set custom expiration time
     */
    public function setExpirationTime($hours = 24): self
    {
        $this->expires_at = Carbon::now()->addHours($hours);
        return $this;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', Carbon::now())
            ->whereIn('status', ['created', 'sent', 'opened']);
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', Carbon::now());
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'payment_completed');
    }

    public function scopeForProgram($query, $programId)
    {
        return $query->where('program_id', $programId);
    }
}
