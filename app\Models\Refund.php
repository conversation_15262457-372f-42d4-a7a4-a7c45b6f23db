<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Refund extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'refundable_type', // Invoice or Charge
        'refundable_id',
        'stripe_refund_id',
        'amount',
        'currency',
        'reason',
        'status',
        'metadata',
        'refunded_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'refunded_at' => 'datetime',
        'amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function refundable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getFormattedAmountAttribute()
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getFormattedRefundedAtAttribute()
    {
        return $this->refunded_at ? $this->refunded_at->format('M d, Y H:i:s') : 'N/A';
    }

    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'succeeded' => '<span class="badge bg-success">Completed</span>',
            'pending' => '<span class="badge bg-warning">Pending</span>',
            'failed' => '<span class="badge bg-danger">Failed</span>',
            default => '<span class="badge bg-secondary">Unknown</span>',
        };
    }
}
