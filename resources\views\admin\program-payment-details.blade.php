@extends('layouts.admin')

@section('title', 'Program Payment Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>{{ $program->name }} - Payment Details</h3>
                    <small class="text-muted">{{ $program->location }} • {{ $program->sport }}</small>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>${{ number_format($stats['total_revenue'], 2) }}</h5>
                                    <small>Total Revenue</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>{{ $stats['total_registrations'] }}</h5>
                                    <small>Total Registrations</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5>${{ number_format($stats['credit_used'], 2) }}</h5>
                                    <small>Credit Used</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>{{ $stats['recurring_payments'] }}</h5>
                                    <small>Recurring Payments</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Guardian</th>
                                    <th>Player</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Original</th>
                                    <th>Discount</th>
                                    <th>Credit</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transactions as $transaction)
                                <tr>
                                    <td>{{ $transaction->created_at->format('M j, Y') }}</td>
                                    <td>{{ $transaction->user->name }}</td>
                                    <td>{{ $transaction->player->name ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-{{ $transaction->transaction_type === 'recurring' ? 'warning' : 'primary' }}">
                                            {{ ucfirst($transaction->transaction_type) }}
                                        </span>
                                        @if($transaction->installment_number)
                                            <small class="d-block">{{ $transaction->installment_number }}/{{ $transaction->total_installments }}</small>
                                        @endif
                                    </td>
                                    <td>${{ number_format($transaction->amount, 2) }}</td>
                                    <td>${{ number_format($transaction->original_amount, 2) }}</td>
                                    <td>
                                        @if($transaction->discount_amount > 0)
                                            <span class="text-success">-${{ number_format($transaction->discount_amount, 2) }}</span>
                                            @if($transaction->coupon_code)
                                                <small class="d-block">({{ $transaction->coupon_code }})</small>
                                            @endif
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        @if($transaction->credit_used > 0)
                                            <span class="text-info">${{ number_format($transaction->credit_used, 2) }}</span>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $transaction->payment_method === 'stripe' ? 'primary' : 'info' }}">
                                            {{ ucfirst($transaction->payment_method) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $transaction->status === 'completed' ? 'success' : 'warning' }}">
                                            {{ ucfirst($transaction->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showDetails({{ $transaction->id }})">
                                            View
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Details</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<script>
function showDetails(transactionId) {
    fetch(`/admin/payment-transaction/${transactionId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('paymentDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>Transaction ID:</strong></div>
                    <div class="col-6">${data.id}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Date:</strong></div>
                    <div class="col-6">${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Guardian:</strong></div>
                    <div class="col-6">${data.user.name}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Player:</strong></div>
                    <div class="col-6">${data.player ? data.player.name : 'N/A'}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Original Amount:</strong></div>
                    <div class="col-6">$${parseFloat(data.original_amount).toFixed(2)}</div>
                </div>
                ${data.discount_amount > 0 ? `
                <div class="row">
                    <div class="col-6"><strong>Coupon Discount:</strong></div>
                    <div class="col-6 text-success">-$${parseFloat(data.discount_amount).toFixed(2)} ${data.coupon_code ? '(' + data.coupon_code + ')' : ''}</div>
                </div>
                ` : ''}
                ${data.credit_used > 0 ? `
                <div class="row">
                    <div class="col-6"><strong>Credit Used:</strong></div>
                    <div class="col-6 text-info">$${parseFloat(data.credit_used).toFixed(2)}</div>
                </div>
                ` : ''}
                <div class="row">
                    <div class="col-6"><strong>Final Amount:</strong></div>
                    <div class="col-6"><strong>$${parseFloat(data.amount).toFixed(2)}</strong></div>
                </div>
                ${data.next_payment_date ? `
                <div class="row">
                    <div class="col-6"><strong>Next Payment:</strong></div>
                    <div class="col-6">${new Date(data.next_payment_date).toLocaleDateString()}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Monthly Amount:</strong></div>
                    <div class="col-6">$${parseFloat(data.monthly_amount).toFixed(2)}</div>
                </div>
                ` : ''}
            `;
            $('#paymentDetailsModal').modal('show');
        });
}
</script>
@endsection