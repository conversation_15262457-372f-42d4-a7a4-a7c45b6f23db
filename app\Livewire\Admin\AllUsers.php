<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Mail\BulkUserEmail;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;

class AllUsers extends Component
{
    use WithPagination;

    public $search = '';
    public $filter = '';
    public $perPage = 10;
    public $mergeMode = false;
    public $selectedPlayers = [];
    public $mergeGuardiansMode = false;
    public $selectedGuardians = [];

    // Configuration for searchable fields and filters
    protected $searchableFields = ['firstName', 'lastName', 'email', 'town', 'grade'];
    protected $filterableFields = ['Town' => 'town', 'Age' => 'age', 'Grade' => 'grade', 'Type' => 'type'];
    protected $defaultOrderBy = 'firstName';

    // Define the pagination theme for Livewire
    protected $paginationTheme = 'bootstrap';

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilter()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function render()
    {
        $users = $this->buildQuery()->paginate($this->perPage);
        return view('livewire.admin.all-users', compact('users'));
    }

    /**
     * Build the main query with all filters applied
     */
    protected function buildQuery()
    {
        return User::query()
            ->whereDoesntHave('roles', function ($query) {
                $query->where('name', 'admin');
            })
            ->when($this->hasSearchTerm(), fn($query) => $this->applySearch($query))
            ->when($this->hasValidFilter(), fn($query) => $this->applyOrdering($query))
            ->unless($this->hasValidFilter(), fn($query) => $query->orderBy($this->defaultOrderBy))
            ->with('roles');
    }


    /**
     * Apply search filters to the query
     */
    protected function applySearch($query)
    {
        $searchTerm = $this->search;

        if ($this->hasValidFilter()) {
            return $this->applySpecificFieldSearch($query, $searchTerm);
        }

        return $this->applyGlobalSearch($query, $searchTerm);
    }

    /**
     * Apply search to a specific field based on filter
     */
    protected function applySpecificFieldSearch($query, $searchTerm)
    {
        $field = $this->getFilterField();

        if ($field === 'type') {
            $roleSearch = strtolower($searchTerm);
            return $query->whereHas('roles', function ($q) use ($roleSearch) {
                $q->where('name', 'like', "%{$roleSearch}%");
            });
        } elseif ($field) {
            $query->where($field, 'like', "%{$searchTerm}%");
        } else {
            $this->applyDefaultSearch($query, $searchTerm);
        }

        return $query;
    }

    /**
     * Apply global search across all searchable fields
     */
    protected function applyGlobalSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            foreach ($this->searchableFields as $field) {
                $q->orWhere($field, 'like', "%{$searchTerm}%");
            }
        });
    }

    /**
     * Apply default search (name and email) when filter doesn't match
     */
    protected function applyDefaultSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('firstName', 'like', "%{$searchTerm}%")
                ->orWhere('lastName', 'like', "%{$searchTerm}%")
                ->orWhere('email', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Apply ordering based on filter
     */
    protected function applyOrdering($query)
    {
        $field = $this->getFilterField();

        if ($field === 'type') {
            return $query->orderBy(DB::raw("(SELECT roles.name
                FROM roles
                JOIN role_user ON roles.id = role_user.role_id
                WHERE role_user.user_id = users.id
                LIMIT 1)"));
        }

        return $query->orderBy($field ?: $this->defaultOrderBy);
    }

    /**
     * Get the database field name for the current filter
     */
    protected function getFilterField()
    {
        return $this->filterableFields[$this->filter] ?? null;
    }

    /**
     * Check if there's a search term
     */
    protected function hasSearchTerm()
    {
        return !empty($this->search);
    }

    /**
     * Check if the current filter is valid
     */
    protected function hasValidFilter()
    {
        return !empty($this->filter) && array_key_exists($this->filter, $this->filterableFields);
    }

    /**
     * Get available filters for the view
     */
    public function getAvailableFilters()
    {
        return array_keys($this->filterableFields);
    }

    /**
     * Get searchable fields for the view
     */
    public function getSearchableFields()
    {
        return $this->searchableFields;
    }

    /**
     * Add a new searchable field
     */
    public function addSearchableField($field)
    {
        if (!in_array($field, $this->searchableFields)) {
            $this->searchableFields[] = $field;
        }
        return $this;
    }

    /**
     * Add a new filter
     */
    public function addFilter($label, $field)
    {
        $this->filterableFields[$label] = $field;
        return $this;
    }

    /**
     * Clear all filters and search
     */
    public function clearFilters()
    {
        $this->search = '';
        $this->filter = '';
        $this->resetPage();
    }

    /**
     * Edit user - redirect to appropriate edit route based on role
     */
    public function editUser($userId, $role)
    {
        // You can customize these routes based on your application structure
        switch ($role) {
            case 'admin':
                return redirect()->route('admin.admins.edit', $userId);
            case 'coach':
                return redirect()->route('admin.coaches.edit', $userId);
            case 'guardian':
                return redirect()->route('admin.guardians.edit', $userId);
            case 'player':
                return redirect()->route('admin.players.edit', $userId);
            default:
                session()->flash('error', 'Invalid user role.');
        }
    }

    /**
     * Confirm and delete user
     */
    public function confirmDelete($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // Add any business logic checks here
            if ($user->email === '<EMAIL>') {
                session()->flash('error', 'Cannot delete super admin user.');
                return;
            }

            $user->delete();

            session()->flash('success', 'User deleted successfully.');

            // Reset to first page if current page becomes empty after deletion
            $currentPageUsers = $this->buildQuery()->paginate($this->perPage);
            if ($currentPageUsers->count() === 0 && $this->getPage() > 1) {
                $this->previousPage();
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete user. Please try again.');
        }
    }

    /**
     * Send email to users based on current filters
     */
    public function sendBulkEmail($subject, $message)
    {
        try {
            // Get users based on current filters (without pagination)
            $users = $this->buildQuery()->get();

            // Filter out users without email addresses
            $usersWithEmail = $users->filter(function ($user) {
                return !empty($user->email) && $user->email !== '<EMAIL>';
            });

            if ($usersWithEmail->isEmpty()) {
                session()->flash('error', 'No users with valid email addresses found.');
                return;
            }

            $emailCount = 0;
            foreach ($usersWithEmail as $user) {
                $recipientName = trim($user->firstName . ' ' . $user->lastName);
                Mail::to($user->email)->send(new BulkUserEmail($subject, $message, $recipientName));
                $emailCount++;
            }

            $filterText = $this->getFilterDescription();
            session()->flash('success', "Email sent successfully to {$emailCount} users{$filterText}.");
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to send emails. Please try again.');
        }
    }

    /**
     * Get description of current filters for success message
     */
    private function getFilterDescription()
    {
        $descriptions = [];

        if (!empty($this->filter)) {
            $descriptions[] = "filtered by {$this->filter}";
        }

        if (!empty($this->search)) {
            $descriptions[] = "matching search '{$this->search}'";
        }

        if (empty($descriptions)) {
            return '';
        }

        return ' (' . implode(' and ', $descriptions) . ')';
    }

    /**
     * Get count of users that would receive the email
     */
    public function getEmailRecipientCount()
    {
        $users = $this->buildQuery()->get();
        return $users->filter(function ($user) {
            return !empty($user->email) && $user->email !== '<EMAIL>';
        })->count();
    }

    /**
     * Toggle merge mode
     */
    public function toggleMergeMode()
    {
        $this->mergeMode = !$this->mergeMode;
        $this->selectedPlayers = [];
    }

    /**
     * Toggle player selection for merging
     */
    public function togglePlayerSelection($playerId)
    {
        if (in_array($playerId, $this->selectedPlayers)) {
            $this->selectedPlayers = array_diff($this->selectedPlayers, [$playerId]);
        } else {
            $this->selectedPlayers[] = $playerId;
        }
    }

    /**
     * Select all players on current page
     */
    public function selectAllPlayers()
    {
        $players = $this->buildQuery()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'player');
            })
            ->paginate($this->perPage);

        $playerIds = $players->pluck('id')->toArray();

        // If all current page players are selected, deselect them
        if (empty(array_diff($playerIds, $this->selectedPlayers))) {
            $this->selectedPlayers = array_diff($this->selectedPlayers, $playerIds);
        } else {
            // Otherwise, select all current page players
            $this->selectedPlayers = array_unique(array_merge($this->selectedPlayers, $playerIds));
        }
    }

    /**
     * Get selected players count
     */
    public function getSelectedPlayersCount()
    {
        return count($this->selectedPlayers);
    }

    /**
     * Check if player is selected
     */
    public function isPlayerSelected($playerId)
    {
        return in_array($playerId, $this->selectedPlayers);
    }

    /**
     * Merge selected players
     */
    public function mergeSelectedPlayers()
    {
        if (count($this->selectedPlayers) < 2) {
            session()->flash('error', 'Please select at least 2 players to merge.');
            return;
        }

        try {
            DB::beginTransaction();

            // Validate all selected players first
            $validPlayers = $this->validateSelectedPlayers();

            // Use the first selected player as target, merge others into it
            $targetPlayer = $validPlayers[0];
            $sourcePlayers = array_slice($validPlayers, 1);

            $mergedCount = 0;
            foreach ($sourcePlayers as $sourcePlayer) {
                // Transfer relationships from source to target
                $this->transferPlayerRelationships($sourcePlayer, $targetPlayer);

                // Delete source player
                $sourcePlayer->delete();
                $mergedCount++;
            }

            DB::commit();

            session()->flash('success', "Successfully merged {$mergedCount} players into the target player.");

            // Reset merge mode and selections
            $this->mergeMode = false;
            $this->selectedPlayers = [];

            // Refresh the page to show updated data
            $this->resetPage();
        } catch (\Exception $e) {
            DB::rollback();
            session()->flash('error', 'An error occurred while merging players: ' . $e->getMessage());
        }
    }

    /**
     * Transfer all relationships from source player to target player
     */
    private function transferPlayerRelationships($sourcePlayer, $targetPlayer)
    {
        // 1. Update program registrations
        \App\Models\ProgramRegistration::where('player_id', $sourcePlayer->id)
            ->whereNotExists(function ($query) use ($targetPlayer) {
                $query->select(DB::raw(1))
                    ->from('program_registrations as pr2')
                    ->whereColumn('pr2.program_id', 'program_registrations.program_id')
                    ->where('pr2.player_id', $targetPlayer->id);
            })
            ->update(['player_id' => $targetPlayer->id]);

        // 2. Update player programs
        \App\Models\PlayerProgram::where('player_id', $sourcePlayer->id)
            ->whereNotExists(function ($query) use ($targetPlayer) {
                $query->select(DB::raw(1))
                    ->from('player_program as pp2')
                    ->whereColumn('pp2.program_id', 'player_program.program_id')
                    ->where('pp2.player_id', $targetPlayer->id);
            })
            ->update(['player_id' => $targetPlayer->id]);

        // 3. Update team memberships
        \App\Models\TeamPlayer::where('player_id', $sourcePlayer->id)
            ->whereNotExists(function ($query) use ($targetPlayer) {
                $query->select(DB::raw(1))
                    ->from('team_players as tp2')
                    ->whereColumn('tp2.team_id', 'team_players.team_id')
                    ->where('tp2.player_id', $targetPlayer->id);
            })
            ->update(['player_id' => $targetPlayer->id]);

        // 4. Update admin invitations
        \App\Models\AdminInvitesPlayerForProgram::where('user_id', $sourcePlayer->id)
            ->update(['user_id' => $targetPlayer->id]);

        // 5. Transfer guardian relationships if target doesn't have them
        if (!$targetPlayer->primary_parent_id && $sourcePlayer->primary_parent_id) {
            $targetPlayer->primary_parent_id = $sourcePlayer->primary_parent_id;
        }

        if (!$targetPlayer->parent_id && $sourcePlayer->parent_id) {
            $targetPlayer->parent_id = $sourcePlayer->parent_id;
        }

        // 6. Transfer additional guardians
        $sourceGuardians = \App\Models\UserGuardian::where('user_id', $sourcePlayer->id)->get();
        foreach ($sourceGuardians as $guardianRelation) {
            // Only add if not already exists
            $exists = \App\Models\UserGuardian::where('user_id', $targetPlayer->id)
                ->where('guardian_id', $guardianRelation->guardian_id)
                ->exists();

            if (!$exists) {
                \App\Models\UserGuardian::create([
                    'user_id' => $targetPlayer->id,
                    'guardian_id' => $guardianRelation->guardian_id
                ]);
            }
        }

        // 7. Update target player with any missing information from source
        $updateData = [];
        if (!$targetPlayer->email && $sourcePlayer->email) {
            $updateData['email'] = $sourcePlayer->email;
        }
        if (!$targetPlayer->mobile_number && $sourcePlayer->mobile_number) {
            $updateData['mobile_number'] = $sourcePlayer->mobile_number;
        }
        if (!$targetPlayer->profilePhoto && $sourcePlayer->profilePhoto) {
            $updateData['profilePhoto'] = $sourcePlayer->profilePhoto;
        }

        if (!empty($updateData)) {
            $targetPlayer->update($updateData);
        } else {
            $targetPlayer->save(); // Save guardian relationship updates
        }

        // 8. Delete source player related records
        \App\Models\UserGuardian::where('user_id', $sourcePlayer->id)->delete();
        \App\Models\ProgramRegistration::where('player_id', $sourcePlayer->id)->delete();
        \App\Models\PlayerProgram::where('player_id', $sourcePlayer->id)->delete();
        \App\Models\TeamPlayer::where('player_id', $sourcePlayer->id)->delete();
        \App\Models\AdminInvitesPlayerForProgram::where('user_id', $sourcePlayer->id)->delete();
    }

    /**
     * Cancel merge mode
     */
    public function cancelMergeMode()
    {
        $this->mergeMode = false;
        $this->selectedPlayers = [];
    }

    /**
     * Toggle merge mode for guardians
     */
    public function toggleMergeGuardiansMode()
    {
        $this->mergeGuardiansMode = !$this->mergeGuardiansMode;
        $this->selectedGuardians = [];
    }

    /**
     * Toggle guardian selection for merging
     */
    public function toggleGuardianSelection($guardianId)
    {
        if (in_array($guardianId, $this->selectedGuardians)) {
            $this->selectedGuardians = array_diff($this->selectedGuardians, [$guardianId]);
        } else {
            $this->selectedGuardians[] = $guardianId;
        }
    }

    /**
     * Select all guardians on current page
     */
    public function selectAllGuardians()
    {
        $guardians = $this->buildQuery()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'guardian');
            })
            ->paginate($this->perPage);

        $guardianIds = $guardians->pluck('id')->toArray();

        // If all current page guardians are selected, deselect them
        if (empty(array_diff($guardianIds, $this->selectedGuardians))) {
            $this->selectedGuardians = array_diff($this->selectedGuardians, $guardianIds);
        } else {
            // Otherwise, select all current page guardians
            $this->selectedGuardians = array_unique(array_merge($this->selectedGuardians, $guardianIds));
        }
    }

    /**
     * Get selected guardians count
     */
    public function getSelectedGuardiansCount()
    {
        return count($this->selectedGuardians);
    }

    /**
     * Check if guardian is selected
     */
    public function isGuardianSelected($guardianId)
    {
        return in_array($guardianId, $this->selectedGuardians);
    }

    /**
     * Merge selected guardians
     */
    public function mergeSelectedGuardians()
    {
        if (count($this->selectedGuardians) < 2) {
            session()->flash('error', 'Please select at least 2 guardians to merge.');
            return;
        }
        $targetGuardianId = $this->selectedGuardians[0];
        $sourceGuardianIds = array_slice($this->selectedGuardians, 1);
        try {
            // Update primary_parent_id for all source guardians
            foreach ($sourceGuardianIds as $sourceId) {
                $guardian = \App\Models\User::find($sourceId);
                if ($guardian) {
                    $guardian->primary_parent_id = $targetGuardianId;
                    $guardian->save();
                }
            }
            // Optionally, call backend for audit/logging
            // \Illuminate\Support\Facades\Http::post(route('admin.api.mergeGuardians'), [
            //     'target_guardian_id' => $targetGuardianId,
            //     'source_guardian_ids' => $sourceGuardianIds,
            // ]);
            session()->flash('success', 'Guardians merged successfully.');
            $this->mergeGuardiansMode = false;
            $this->selectedGuardians = [];
            $this->resetPage();
        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while merging guardians: ' . $e->getMessage());
        }
    }

    /**
     * Cancel merge mode for guardians
     */
    public function cancelMergeGuardiansMode()
    {
        $this->mergeGuardiansMode = false;
        $this->selectedGuardians = [];
    }

    /**
     * Validate selected players before merging
     */
    private function validateSelectedPlayers()
    {
        $errors = [];
        $validPlayers = [];

        foreach ($this->selectedPlayers as $playerId) {
            try {
                $player = User::find($playerId);

                if (!$player) {
                    $errors[] = "Player with ID {$playerId} not found";
                    continue;
                }

                if (!$player->hasRole('player')) {
                    $errors[] = "User {$player->firstName} {$player->lastName} (ID: {$playerId}) is not a player";
                    continue;
                }

                $validPlayers[] = $player;
            } catch (\Exception $e) {
                $errors[] = "Error validating player ID {$playerId}: " . $e->getMessage();
            }
        }

        if (!empty($errors)) {
            throw new \Exception('Player validation failed: ' . implode(', ', $errors));
        }

        return $validPlayers;
    }
}
