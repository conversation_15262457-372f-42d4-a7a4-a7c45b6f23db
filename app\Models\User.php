<?php

namespace App\Models;


use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'firstName',
        'lastName',
        'email',
        'password',
        'birthDate',
        'age',
        'profilePhoto',
        'grade',
        'gender',
        'address',
        'street',
        'town',
        'state',
        'program',
        'teamName',
        'parent_id',
        'is_joined',
        'primary_parent_id',
        'mobile_number',
        'is_guardian',
        'is_coach',
        'current_role',
        'stripe_customer_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($user) {

            $fullName = trim($user->firstName . ' ' . $user->lastName);
            $slug = Str::slug($fullName);


            $count = User::where('slug', $slug)->count();
            if ($count > 0) {
                $slug = $slug . '-' . ($count + 1);
            }

            $user->slug = $slug;
        });
    }





    public function playerProgram()
    {
        return $this->hasMany(PlayerProgram::class);
    }

    public function additional_guardians()
    {
        return $this->hasMany(User::class, 'parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'guardian');
        });
    }

    public function players()
    {
        //guardian players
        return $this->hasMany(User::class, 'parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'player');
        });
    }

    public function all_players()
    {
        //guardian + additional_guardians players
        return $this->hasMany(User::class, 'primary_parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'player');
        });
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_user');
    }

    public function hasRole($role)
    {
        return in_array($role, $this->roles()->pluck('name')->toArray());
    }

    public function scopeWithRole($query, $roleName)
    {
        return $query->whereHas('roles', function ($q) use ($roleName) {
            $q->where('name', $roleName);
        });
    }



    public function program()
    {
        return $this->hasMany(UserProgram::class);
    }


    public function calculateAmountForProgram($programId, $registerDate = null)
    {
        try {
            $program = Program::findOrFail($programId);


            if (!$registerDate) {
                $registerDate = Carbon::now();
            }
            if ($program->enable_early_bird_specials == 0) {
                return $program->cost;
            }

            $earlyBirdPricings = EarlyBirdPricing::where('program_id', $program->id)->get();
            $totalRegistration = ProgramRegistration::where('program_id', $program->id)->count() + 1;

            $price = null;

            if ($program->early_bird_specials_date) {
                foreach ($earlyBirdPricings as $earlyBirdPricing) {
                    if ($totalRegistration >= $earlyBirdPricing->from && $totalRegistration <= $earlyBirdPricing->to) {
                        if ($registerDate->lessThan($program->early_bird_specials_date)) {
                            $price = $earlyBirdPricing->price_before;
                        } else {
                            $price = $earlyBirdPricing->price_on_or_after;
                        }
                        break;
                    }
                }
            } else {
                foreach ($earlyBirdPricings as $earlyBirdPricing) {
                    if ($totalRegistration >= $earlyBirdPricing->from && $totalRegistration <= $earlyBirdPricing->to) {
                        $price = $earlyBirdPricing->price_before;
                        break;
                    } else {
                        $price = $earlyBirdPricing->price_on_or_after;
                        break;
                    }
                }
            }
            return $price ?? $program->cost;
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['error' => 'An error occurred. Please try again later.']);
        }
    }









    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_players', 'player_id', 'team_id');
    }


    public function programs()
    {
        return $this->hasMany(PlayerProgram::class, 'player_id');
    }

    public function registrations()
    {
        return $this->hasMany(ProgramRegistration::class, 'player_id');
    }

    public function programRegistrations()
    {
        return $this->belongsToMany(Program::class, 'program_registrations', 'player_id', 'program_id');
    }

    /**
     * Get team invitations for this user (as a player)
     */
    public function teamInvitations()
    {
        return $this->hasMany(PlayerInvitation::class, 'user_id');
    }

    /**
     * Get invitations sent by this user (as admin/coach)
     */
    public function sentInvitations()
    {
        return $this->hasMany(PlayerInvitation::class, 'invited_by');
    }

    /**
     * Get guardian credits for this user
     */
    public function guardianCredits()
    {
        return $this->hasMany(GuardianCredit::class);
    }

    /**
     * Get total available credit amount for this guardian
     */
    public function getTotalAvailableCredit()
    {
        return $this->guardianCredits()
            ->where('remaining_amount', '>', 0)
            ->sum('remaining_amount');
    }

    /**
     * Use credit for payment with detailed logging
     */
    public function useCredit($amount, $paymentTransactionId = null, $programId = null, $playerIds = [])
    {
        $availableCredits = $this->guardianCredits()
            ->where('remaining_amount', '>', 0)
            ->orderBy('created_at', 'asc')
            ->get();

        $remainingAmount = $amount;
        $creditUsageLogs = [];

        foreach ($availableCredits as $credit) {
            if ($remainingAmount <= 0) break;

            $balanceBefore = $credit->remaining_amount;
            $amountToUse = min($remainingAmount, $credit->remaining_amount);
            $credit->remaining_amount -= $amountToUse;
            $credit->save();

            // Create detailed usage log if transaction details provided
            if ($paymentTransactionId && $programId) {
                $creditUsageLogs[] = [
                    'payment_transaction_id' => $paymentTransactionId,
                    'guardian_credit_id' => $credit->id,
                    'user_id' => $this->id,
                    'program_id' => $programId,
                    'credit_amount_used' => $amountToUse,
                    'credit_balance_before' => $balanceBefore,
                    'credit_balance_after' => $credit->remaining_amount,
                    'player_ids' => $playerIds,
                    'usage_context' => 'program_payment',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            $remainingAmount -= $amountToUse;
        }

        // Bulk insert credit usage logs
        if (!empty($creditUsageLogs)) {
            \App\Models\CreditUsageLog::insert($creditUsageLogs);
        }

        return $amount - $remainingAmount; // Return amount actually used
    }

    /**
     * Get payment transactions for this user
     */
    public function paymentTransactions()
    {
        return $this->hasMany(\App\Models\PaymentTransaction::class);
    }

    /**
     * Get credit usage logs for this user
     */
    public function creditUsageLogs()
    {
        return $this->hasMany(\App\Models\CreditUsageLog::class);
    }

    /**
     * Get external payment links requested by this user
     */
    public function requestedExternalPaymentLinks()
    {
        return $this->hasMany(\App\Models\ExternalPaymentLink::class, 'requesting_user_id');
    }

    /**
     * Get subscriptions for this user
     */
    public function subscriptions()
    {
        return $this->hasMany(\App\Models\Subscription::class);
    }

    /**
     * Get active subscriptions for this user
     */
    public function activeSubscriptions()
    {
        return $this->subscriptions()->where('status', 'active');
    }
}
