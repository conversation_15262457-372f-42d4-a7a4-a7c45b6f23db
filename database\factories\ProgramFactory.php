<?php

namespace Database\Factories;

use App\Models\Program;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProgramFactory extends Factory
{
    protected $model = Program::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->paragraph(),
            'cost' => $this->faker->randomFloat(2, 50, 500),
            'payment' => $this->faker->randomElement(['full', 'split', 'recurring']),
            'minimum_recurring_amount' => $this->faker->randomFloat(2, 25, 100),
            'start_date' => $this->faker->dateTimeBetween('now', '+1 month'),
            'end_date' => $this->faker->dateTimeBetween('+1 month', '+3 months'),
            'registration_start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'registration_end_date' => $this->faker->dateTimeBetween('now', '+1 month'),
            'age_restriction_from' => $this->faker->numberBetween(8, 12),
            'age_restriction_to' => $this->faker->numberBetween(13, 18),
            'gender_restriction' => $this->faker->randomElement(['male', 'female', 'both']),
            'max_participants' => $this->faker->numberBetween(10, 50),
            'location' => $this->faker->address(),
            'is_active' => true,
            'program_type' => $this->faker->randomElement(['Individual', 'AAU']),
        ];
    }

    public function fullPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment' => 'full',
        ]);
    }

    public function splitPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment' => 'split',
        ]);
    }

    public function recurringPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment' => 'recurring',
            'minimum_recurring_amount' => $this->faker->randomFloat(2, 25, 100),
        ]);
    }

    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'program_type' => 'Individual',
        ]);
    }

    public function aau(): static
    {
        return $this->state(fn (array $attributes) => [
            'program_type' => 'AAU',
        ]);
    }
}
