<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Subscription;
use Carbon\Carbon;

class SubscriptionTest extends TestCase
{

    public function test_subscription_can_be_created()
    {
        // Create a test user
        $user = User::factory()->create([
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        // Create a subscription
        $subscription = Subscription::create([
            'user_id' => $user->id,
            'stripe_subscription_id' => 'sub_test123',
            'stripe_customer_id' => 'cus_test123',
            'status' => 'active',
            'payment_type' => 'recurring',
            'amount_per_payment' => 100.00,
            'total_amount_due' => 500.00,
            'currency' => 'usd',
            'interval' => 'month',
            'number_of_payments' => 5,
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonths(5),
            'customer_email' => $user->email,
            'customer_name' => "{$user->firstName} {$user->lastName}",
            'description' => 'Test subscription',
        ]);

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'stripe_subscription_id' => 'sub_test123',
            'status' => 'active',
            'amount_per_payment' => 100.00,
        ]);

        // Test relationships
        $this->assertEquals($user->id, $subscription->user->id);
        $this->assertTrue($user->subscriptions->contains($subscription));
        $this->assertTrue($user->activeSubscriptions->contains($subscription));
    }

    public function test_subscription_helper_methods()
    {
        $user = User::factory()->create();

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'stripe_subscription_id' => 'sub_test123',
            'stripe_customer_id' => 'cus_test123',
            'status' => 'active',
            'payment_type' => 'recurring',
            'amount_per_payment' => 100.00,
            'total_amount_due' => 500.00,
            'amount_paid' => 200.00,
            'currency' => 'usd',
            'interval' => 'month',
            'number_of_payments' => 5,
            'payments_completed' => 2,
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonths(5),
            'customer_email' => $user->email,
            'customer_name' => "{$user->firstName} {$user->lastName}",
        ]);

        // Test helper methods
        $this->assertTrue($subscription->isActive());
        $this->assertFalse($subscription->isCanceled());
        $this->assertEquals(3, $subscription->getRemainingPayments());
        $this->assertEquals(300.00, $subscription->getRemainingAmount());
        $this->assertEquals(40.0, $subscription->getProgressPercentage());
        $this->assertEquals('$100.00', $subscription->getFormattedAmountPerPayment());
        $this->assertEquals('$500.00', $subscription->getFormattedTotalAmountDue());
        $this->assertEquals('$200.00', $subscription->getFormattedAmountPaid());
        $this->assertEquals('$300.00', $subscription->getFormattedRemainingAmount());
        $this->assertTrue($subscription->canBeCanceled());
    }

    public function test_subscription_scopes()
    {
        $user = User::factory()->create();

        // Create active subscription
        $activeSubscription = Subscription::create([
            'user_id' => $user->id,
            'stripe_subscription_id' => 'sub_active',
            'stripe_customer_id' => 'cus_test123',
            'status' => 'active',
            'payment_type' => 'recurring',
            'amount_per_payment' => 100.00,
            'total_amount_due' => 500.00,
            'currency' => 'usd',
            'interval' => 'month',
            'number_of_payments' => 5,
            'customer_email' => $user->email,
        ]);

        // Create canceled subscription
        $canceledSubscription = Subscription::create([
            'user_id' => $user->id,
            'stripe_subscription_id' => 'sub_canceled',
            'stripe_customer_id' => 'cus_test123',
            'status' => 'canceled',
            'payment_type' => 'recurring',
            'amount_per_payment' => 100.00,
            'total_amount_due' => 500.00,
            'currency' => 'usd',
            'interval' => 'month',
            'number_of_payments' => 5,
            'customer_email' => $user->email,
        ]);

        // Test scopes
        $this->assertEquals(1, Subscription::active()->count());
        $this->assertTrue(Subscription::active()->first()->is($activeSubscription));

        $this->assertEquals(2, Subscription::recurring()->count());
    }
}
