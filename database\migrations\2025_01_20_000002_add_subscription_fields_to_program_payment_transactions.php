<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('program_payment_transactions', function (Blueprint $table) {
            $table->string('stripe_subscription_id')->nullable()->after('stripe_payment_intent_id');
            $table->string('stripe_customer_id')->nullable()->after('stripe_subscription_id');
        });
    }

    public function down(): void
    {
        Schema::table('program_payment_transactions', function (Blueprint $table) {
            $table->dropColumn(['stripe_subscription_id', 'stripe_customer_id']);
        });
    }
};