@extends('layouts.app')

@section('title', 'payment')

@section('css')
<style>
.payment-summary {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
}

.summary-header {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    font-size: 14px;
}

.summary-item.total {
    border-top: 1px solid #f0f0f0;
    margin-top: 12px;
    padding-top: 16px;
    font-weight: 600;
    font-size: 16px;
    color: #0b4499;
}

.summary-label {
    color: #666;
}

.summary-value {
    font-weight: 500;
    color: #333;
}

.summary-value.discount {
    color: #28a745;
}

.summary-value.credit {
    color: #17a2b8;
}

.summary-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 12px 0;
}
</style>
@endsection

@section('content')
    <div id="errorMessage">
        <span id="errorText"></span>
    </div>
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payment Details</h1>
    </section>
    <section class="sec form mb-5 program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex flex-column-reverse">
                    <h3 class="text-uppercase mb-4"></h3>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-8 col-xxl-7">
                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="firstName">First Name</label>
                            <input class="form-control" id="firstName" type="text" required />
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="lastName">Last Name</label>
                            <input class="form-control" id="lastName" type="text" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="address">Address</label>
                            <input class="form-control" id="address" type="text" required />
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="form-label" for="state">State</label>
                            <select class="form-control" id="state" required>
                                <option value="">Select</option>
                                <option value="AL">Alabama</option>
                                <option value="AK">Alaska</option>
                                <option value="AZ">Arizona</option>
                                <option value="AR">Arkansas</option>
                                <option value="CA">California</option>
                                <option value="CO">Colorado</option>
                                <option value="CT">Connecticut</option>
                                <option value="DE">Delaware</option>
                                <option value="FL">Florida</option>
                                <option value="GA">Georgia</option>
                                <option value="HI">Hawaii</option>
                                <option value="ID">Idaho</option>
                                <option value="IL">Illinois</option>
                                <option value="IN">Indiana</option>
                                <option value="IA">Iowa</option>
                                <option value="KS">Kansas</option>
                                <option value="KY">Kentucky</option>
                                <option value="LA">Louisiana</option>
                                <option value="ME">Maine</option>
                                <option value="MD">Maryland</option>
                                <option value="MA">Massachusetts</option>
                                <option value="MI">Michigan</option>
                                <option value="MN">Minnesota</option>
                                <option value="MS">Mississippi</option>
                                <option value="MO">Missouri</option>
                                <option value="MT">Montana</option>
                                <option value="NE">Nebraska</option>
                                <option value="NV">Nevada</option>
                                <option value="NH">New Hampshire</option>
                                <option value="NJ">New Jersey</option>
                                <option value="NM">New Mexico</option>
                                <option value="NY">New York</option>
                                <option value="NC">North Carolina</option>
                                <option value="ND">North Dakota</option>
                                <option value="OH">Ohio</option>
                                <option value="OK">Oklahoma</option>
                                <option value="OR">Oregon</option>
                                <option value="PA">Pennsylvania</option>
                                <option value="RI">Rhode Island</option>
                                <option value="SC">South Carolina</option>
                                <option value="SD">South Dakota</option>
                                <option value="TN">Tennessee</option>
                                <option value="TX">Texas</option>
                                <option value="UT">Utah</option>
                                <option value="VT">Vermont</option>
                                <option value="VA">Virginia</option>
                                <option value="WA">Washington</option>
                                <option value="WV">West Virginia</option>
                                <option value="WI">Wisconsin</option>
                                <option value="WY">Wyoming</option>
                            </select>
                        </div>

                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">ZIP Code</label>
                            <div id="card-zip" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-2">
                            <label class="text-uppercase form-label" for="cardType">Card Type</label>
                            <input class="form-control" id="cardType" type="text" disabled />
                        </div>
                        <div class="mb-4 col-md-4">
                            <label class="text-uppercase form-label">Card Number</label>
                            <div id="card-number" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">Exp. Date</label>
                            <div id="card-expiry" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">CVC</label>
                            <div id="card-cvc" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 d-flex justify-content-center mt-5">
                            <button class="cta py-0" id="paymentButton"> <span id="submitButtonText">Submit
                                </span>
                                <span id="loaderForPayment">
                                    <img id="loaderForPaymentImage" src="{{ asset('images/loader.svg') }}"
                                        alt="Loading..."
                                        style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                        </div>
                    </div>

                    <div class="loaderDiv">
                        <img class="loaderImg" src="{{ asset('images/loading.svg') }}"
                            style="height: 120px;width: auto;" />
                    </div>
                    </form>
                </div>
                
                <!-- Payment Summary Card -->
                <div class="col-xl-4 col-xxl-4">
                    <div class="payment-summary">
                        <div class="summary-header">Payment Summary</div>
                        
                        <div class="summary-item">
                            <span class="summary-label">Program Cost</span>
                            <span class="summary-value" id="originalAmount">$0.00</span>
                        </div>
                        
                        <div class="summary-item" id="couponRow" style="display: none;">
                            <span class="summary-label">Coupon Discount</span>
                            <span class="summary-value discount" id="couponDiscount">-$0.00</span>
                        </div>
                        
                        <div class="summary-item" id="creditRow" style="display: none;">
                            <span class="summary-label">Credit Applied</span>
                            <span class="summary-value credit" id="creditApplied">-$0.00</span>
                        </div>
                        
                        <div class="summary-divider"></div>
                        
                        <div class="summary-item total">
                            <span class="summary-label">Total to Pay</span>
                            <span class="summary-value" id="totalAmount">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://cdn.jsdelivr.net/npm/card-validator@latest/dist/card-validator.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {

            const paymentData = @json(session('payment_data'));


            console.log(paymentData);
            
            // Populate payment summary
            populatePaymentSummary(paymentData);
            
            function populatePaymentSummary(data) {
                // Calculate original amount (before any discounts/credits)
                const originalAmount = parseFloat(data.originalAmount || data.totalAmountToBePaid || 0);
                const discountAmount = parseFloat(data.discount_amount || 0);
                const usedCredit = parseFloat(data.used_credit || 0);
                const finalAmount = parseFloat(data.paidAmount || data.totalAmountToBePaid || 0);
                
                // Update original amount
                document.getElementById('originalAmount').textContent = `$${originalAmount.toFixed(2)}`;
                
                // Show coupon discount if applied
                if (discountAmount > 0) {
                    document.getElementById('couponRow').style.display = 'flex';
                    document.getElementById('couponDiscount').textContent = `-$${discountAmount.toFixed(2)}`;
                }
                
                // Show credit applied if used
                if (usedCredit > 0) {
                    document.getElementById('creditRow').style.display = 'flex';
                    document.getElementById('creditApplied').textContent = `-$${usedCredit.toFixed(2)}`;
                }
                
                // Update total amount
                document.getElementById('totalAmount').textContent = `$${finalAmount.toFixed(2)}`;
            }


            var stripe = Stripe('{{ env('STRIPE_KEY') }}');


            const elements = stripe.elements();

            const cardNumber = elements.create('cardNumber', {
                placeholder: 'Card Number',
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#32325d'
                    }
                },
            });

            const cardExpiry = elements.create('cardExpiry');
            const cardCvc = elements.create('cardCvc');
            const cardZip = elements.create('postalCode');

            cardNumber.mount('#card-number');
            cardExpiry.mount('#card-expiry');
            cardCvc.mount('#card-cvc');
            cardZip.mount('#card-zip');

            const cardTypeInput = document.getElementById('cardType');
            cardNumber.on('change', function(event) {
                cardTypeInput.value = event.brand ? capitalize(event.brand) : '';
                if (event.error) showError(event.error.message);
            });

            function capitalize(str) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }

            function showError(message) {

                const errorText = document.getElementById('errorText');
                const errorMessageDiv = document.getElementById('errorMessage');
                errorText.textContent = message;
                errorMessageDiv.style.display = 'block';
                setTimeout(() => {
                    errorMessageDiv.style.display = 'none';
                }, 2000);
            }

            function showLoader() {
                const loader = document.querySelector('.loaderDiv');
                loader.style.display = "block";
            }

            function hideLoader() {
                const loader = document.querySelector('.loaderDiv');
                loader.style.display = "none";
            }


            let selectedState = document.getElementById('state');




            document.getElementById('paymentButton').addEventListener('click', async function(event) {
                try {
                    event.preventDefault();

                    const firstName = document.getElementById('firstName').value;
                    const lastName = document.getElementById('lastName').value;
                    const address = document.getElementById('address').value;
                    const state = document.getElementById('state').value;


                    if (!firstName || !lastName || !address || !state) {
                        showError("Please fill out all required fields.");
                        return;
                    }
                    showLoader();
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content');

                    const paymentData = @json(session('payment_data'));
                    const cardHolderName = `${firstName} ${lastName}`.trim();
                    const billingDetails = {
                        name: cardHolderName,
                        address: {
                            postal_code: document.getElementById('card-zip').value,
                        },
                    };


                    const paymentMethodResponse = await stripe.createPaymentMethod({
                        type: 'card',
                        card: cardNumber,
                        billing_details: billingDetails,
                    });

                    if (paymentMethodResponse.error) {
                        hideLoader();
                        showError(paymentMethodResponse.error.message);
                        return;
                    }

                    const paymentMethodId = paymentMethodResponse.paymentMethod.id;

                    if (paymentData.payment_type == 'recurringPayments') {

                        const requestBody = {
                            cardHolderName: cardHolderName,
                            address: address,
                            state: state,
                            user_id: paymentData.user_id,
                            program_id: paymentData.program_id,
                            payment_type: paymentData.payment_type,
                            paidAmount: paymentData.paidAmount,
                            totalAmountToBePaid: paymentData.totalAmountToBePaid,
                            player_ids: paymentData.player_ids,
                            email: paymentData.payment_type === 'otherGuardian' ? guardianEmail :
                                null,
                            paymentMethodId: paymentMethodId,
                        };
                        if (paymentData.team_id) {
                            requestBody.team_id = paymentData.team_id;
                        }


                        const response = await fetch(route('setup-recurring-payment'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify(requestBody),
                        });

                        const result = await response.json();

                        if (result.error) {
                            hideLoader();
                            showError(result.error);
                        } else {

                            const requestData = {

                                subscriptionId: result.subscriptionId,
                                status: 'active',
                                user_id: paymentData.user_id,
                                programId: paymentData
                                    .program_id,
                                payment_type: paymentData.payment_type,
                                paidAmount: paymentData.paidAmount,
                                amount: paymentData
                                    .totalAmountToBePaid,
                                numberOfPayments: paymentData
                                    .numberOfPayments,
                                paymentMethodId: paymentMethodId,
                                playerIds: paymentData
                                    .player_ids,
                                email: paymentData.email === 'otherGuardian' ?
                                    guardianEmail : null,
                                createdAt: new Date().toISOString(),


                            };
                            if (paymentData.team_id) {
                                requestData.team_id = paymentData.team_id;
                            }

                            const updateResponse = await fetch(route('recurringPayments.status'), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': csrfToken,
                                },



                                body: JSON.stringify(requestData),
                            });

                            const StatusResult = await updateResponse.json();
                            if (!updateResponse.ok) {
                                hideLoader();
                                showError(result.error);
                            } else {
                                window.location.href = route('payment.success');
                            }
                        }

                    } else if (paymentData.payment_type == "specificAmount") {
                        // const url = {{ route('specificAmountPayment') }};
                        const url = "{{ route('specificAmountPayment') }}";

                        const requestBody = {

                            cardHolderName: cardHolderName,
                            address: address,
                            state: state,
                            user_id: paymentData.user_id,
                            program_id: paymentData.program_id,
                            payment_type: paymentData.payment_type,
                            amount: paymentData
                                .totalAmountToBePaid,
                            paidAmount: paymentData.paidAmount,
                            player_ids: paymentData.player_ids,
                            email: paymentData.email === 'otherGuardian' ?
                                guardianEmail : null,
                            paymentMethodId: paymentMethodId,
                            createdAt: new Date().toISOString(),

                        }

                        if (paymentData.team_id) {
                            requestBody.team_id = paymentData.team_id;
                        }
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify(requestBody),
                        });
                        const result = await response.json();
                        console.log(result);

                        if (response.ok) {

                            if (result.requires_action || result.status === 'requires_confirmation') {
                                const {
                                    error
                                } = await stripe.confirmCardPayment(result
                                    .payment_intent_client_secret);
                                if (error) {
                                    hideLoader();
                                    showError(error.message);
                                } else {

                                    window.location.href = route('payment.success');
                                }
                            } else {

                                window.location.href = route('payment.success');
                            }
                        } else {

                            hideLoader();
                            showError(result.error || 'Payment processing failed.');
                        }


                    } else {

                        const requestBody = {

                            cardHolderName: cardHolderName,
                            address: address,
                            state: state,
                            user_id: paymentData.user_id,
                            program_id: paymentData.program_id,
                            payment_type: paymentData.payment_type,
                            amount: paymentData
                                .totalAmountToBePaid,
                            paidAmount: paymentData.paidAmount,
                            player_ids: paymentData.player_ids,
                            email: paymentData.email === 'otherGuardian' ?
                                guardianEmail : null,
                            paymentMethodId: paymentMethodId,
                            createdAt: new Date().toISOString(),

                        }

                        if (paymentData.team_id) {
                            requestBody.team_id = paymentData.team_id;
                        }
                        const response = await fetch(route('payment.process'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify(requestBody),
                        });
                        const result = await response.json();
                        if (response.ok) {

                            if (result.requires_action || result.status === 'requires_confirmation') {
                                const {
                                    error
                                } = await stripe.confirmCardPayment(result
                                    .payment_intent_client_secret);
                                if (error) {
                                    hideLoader();
                                    showError(error.message);
                                } else {

                                    window.location.href = route('payment.success');
                                }
                            } else {

                                window.location.href = route('payment.success');
                            }
                        } else {

                            hideLoader();
                            showError(result.error || 'Payment processing failed.');
                        }
                    }
                } catch (err) {
                    hideLoader();
                    showError(err);
                } finally {
                    hideLoader();
                }
            });

        });
    </script>

@endsection
