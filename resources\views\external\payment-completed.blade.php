<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Already Completed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .completed-container {
            max-width: 600px;
            margin: 2rem auto;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .completed-icon {
            font-size: 4rem;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="completed-container">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="completed-icon mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    
                    <h2 class="text-success mb-4">Payment Already Completed</h2>
                    
                    <div class="alert alert-success">
                        <h5>This payment has already been processed</h5>
                        <p class="mb-0">The payment for this link has already been completed successfully.</p>
                    </div>

                    <!-- Payment Details -->
                    <div class="row text-start mt-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-primary"></i> Payment Details</h6>
                            <p class="mb-1"><strong>Program:</strong> {{ $link->program->name }}</p>
                            <p class="mb-1"><strong>Amount:</strong> ${{ number_format($link->amount_to_pay, 2) }}</p>
                            <p class="mb-1"><strong>Requested by:</strong> {{ $link->requestingUser->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check text-success"></i> Payment Status</h6>
                            <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>
                            @if($link->payment_completed_at)
                                <p class="mb-1"><strong>Completed:</strong> {{ $link->payment_completed_at->format('M j, Y \a\t g:i A') }}</p>
                            @endif
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> Already Paid</h6>
                        <p class="mb-0">
                            This payment link has already been used and the payment has been processed successfully. 
                            If you have any questions about this payment, please contact the program organizer.
                        </p>
                    </div>

                    <!-- Contact Information -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-question-circle"></i> 
                            Questions? Contact {{ $link->requestingUser->name }} at 
                            <a href="mailto:{{ $link->requestingUser->email }}">{{ $link->requestingUser->email }}</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
