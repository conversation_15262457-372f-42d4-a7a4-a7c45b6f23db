<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecurringPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'card_holder',
        'address',
        'state',
        'program_id',
        'payment_type',
        'player_ids',
        'team_id',
        'email',
        'number_of_payments',
        'initial_paid_amount',
        'total_amount_due',
        'paid_amount',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'player_ids' => 'array',
        'initial_paid_amount' => 'decimal:2',
        'total_amount_due' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }
}