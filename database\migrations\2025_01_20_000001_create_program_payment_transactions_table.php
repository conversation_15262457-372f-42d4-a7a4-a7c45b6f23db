<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('program_payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('program_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('player_id')->nullable();
            $table->enum('transaction_type', ['full', 'partial', 'recurring', 'down_payment']);
            $table->enum('payment_method', ['stripe', 'credit', 'external']);
            $table->decimal('amount', 10, 2);
            $table->decimal('original_amount', 10, 2);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('credit_used', 10, 2)->default(0);
            $table->string('coupon_code')->nullable();
            $table->string('stripe_payment_intent_id')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->integer('installment_number')->nullable();
            $table->integer('total_installments')->nullable();
            $table->decimal('monthly_amount', 10, 2)->nullable();
            $table->date('next_payment_date')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('program_payment_transactions');
    }
};