<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Link Expired</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .expired-container {
            max-width: 600px;
            margin: 2rem auto;
        }
        .card {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .expired-icon {
            font-size: 4rem;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="expired-container">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="expired-icon mb-4">
                        <i class="fas fa-clock"></i>
                    </div>
                    
                    <h2 class="text-warning mb-4">Payment Link Expired</h2>
                    
                    <div class="alert alert-warning">
                        <h5>This payment link has expired</h5>
                        <p class="mb-0">The payment link you're trying to access is no longer valid.</p>
                    </div>

                    <!-- Link Details -->
                    <div class="row text-start mt-4">
                        <div class="col-12">
                            <h6><i class="fas fa-info-circle text-primary"></i> Payment Link Details</h6>
                            <p class="mb-1"><strong>Program:</strong> {{ $link->program->name }}</p>
                            <p class="mb-1"><strong>Amount:</strong> ${{ number_format($link->amount_to_pay, 2) }}</p>
                            <p class="mb-1"><strong>Expired on:</strong> {{ $link->expires_at->format('M j, Y \a\t g:i A') }}</p>
                            <p class="mb-1"><strong>Requested by:</strong> {{ $link->requestingUser->name }}</p>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> What to do next?</h6>
                        <p class="mb-0">
                            Please contact {{ $link->requestingUser->name }} at 
                            <a href="mailto:{{ $link->requestingUser->email }}">{{ $link->requestingUser->email }}</a>
                            to request a new payment link.
                        </p>
                    </div>

                    <!-- Contact Information -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-envelope"></i> 
                            Contact: <a href="mailto:{{ $link->requestingUser->email }}">{{ $link->requestingUser->email }}</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
