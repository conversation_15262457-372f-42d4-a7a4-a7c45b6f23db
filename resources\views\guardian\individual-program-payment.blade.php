@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Payment Options - {{ $paymentData['program']->name }}</h4>
                </div>
                <div class="card-body">
                    <!-- Program and Player Details -->
                    <div class="mb-4">
                        <h5>Registration Details</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Player Name</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($paymentData['player_details'] as $player)
                                    <tr>
                                        <td>{{ $player['name'] }}</td>
                                        <td>${{ number_format($player['amount'], 2) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th>Total Amount</th>
                                        <th>${{ number_format($paymentData['total_amount'], 2) }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <!-- Available Credit Display -->
                    @if($paymentData['available_credit'] > 0)
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-credit-card"></i>
                        <strong>Available Credit: ${{ number_format($paymentData['available_credit'], 2) }}</strong>
                        <br>
                        <small>You can use your available credit to pay for this registration.</small>
                    </div>
                    @endif

                    <!-- Payment Options Form -->
                    <form id="paymentOptionsForm">
                        @csrf
                        <input type="hidden" name="program_id" value="{{ $paymentData['program']->id }}">
                        <input type="hidden" name="player_ids" value="{{ json_encode(array_column($paymentData['player_details'], 'id')) }}">

                        <!-- Payment Type Selection -->
                        <div class="mb-4">
                            <h5>Select Payment Type</h5>
                            @foreach($paymentData['available_payment_methods'] as $method)
                                @if($method === 'full')
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_type" id="payment_type_full" value="full" checked>
                                    <label class="form-check-label" for="payment_type_full">
                                        <strong>Full Payment</strong> - Pay the complete amount now
                                    </label>
                                </div>
                                @elseif($method === 'split')
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_type" id="payment_type_split" value="split">
                                    <label class="form-check-label" for="payment_type_split">
                                        <strong>Split Payment</strong> - Pay a portion now, rest later
                                    </label>
                                </div>
                                @elseif($method === 'recurring')
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_type" id="payment_type_recurring" value="recurring">
                                    <label class="form-check-label" for="payment_type_recurring">
                                        <strong>Recurring Payment</strong> - Set up monthly payments
                                        @if($paymentData['minimum_recurring_amount'])
                                        <br><small class="text-muted">Minimum monthly amount: ${{ number_format($paymentData['minimum_recurring_amount'], 2) }}</small>
                                        @endif
                                    </label>
                                </div>
                                @endif
                            @endforeach
                        </div>

                        <!-- Recurring Payment Amount (shown when recurring is selected) -->
                        <div id="recurringAmountSection" class="mb-4" style="display: none;">
                            <label for="recurring_amount" class="form-label">Monthly Payment Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="recurring_amount" name="recurring_amount" 
                                       min="{{ $paymentData['minimum_recurring_amount'] ?? 0 }}" 
                                       max="{{ $paymentData['total_amount'] }}" 
                                       step="0.01" placeholder="Enter monthly amount">
                            </div>
                            <small class="form-text text-muted">
                                Enter the amount you want to pay monthly. 
                                @if($paymentData['minimum_recurring_amount'])
                                Minimum: ${{ number_format($paymentData['minimum_recurring_amount'], 2) }}
                                @endif
                            </small>
                        </div>

                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <h5>How would you like to pay?</h5>
                            
                            <!-- Credit Payment Option -->
                            @if($paymentData['available_credit'] > 0)
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_method_credit" value="credit">
                                <label class="form-check-label" for="payment_method_credit">
                                    <strong>Use Available Credit</strong> (${{ number_format($paymentData['available_credit'], 2) }} available)
                                </label>
                            </div>
                            @endif

                            <!-- Card Payment Option -->
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_method_card" value="card" 
                                       @if($paymentData['available_credit'] <= 0) checked @endif>
                                <label class="form-check-label" for="payment_method_card">
                                    <strong>Pay with Card</strong>
                                </label>
                            </div>

                            <!-- Mixed Payment Option (Credit + Card) -->
                            @if($paymentData['available_credit'] > 0 && $paymentData['available_credit'] < $paymentData['total_amount'])
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_method_mixed" value="mixed">
                                <label class="form-check-label" for="payment_method_mixed">
                                    <strong>Use Credit + Card</strong> - Use available credit and pay remaining with card
                                </label>
                            </div>
                            @endif

                            <!-- External Payment Option -->
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_method_external" value="external_link">
                                <label class="form-check-label" for="payment_method_external">
                                    <strong>Send Payment Link to Someone Else</strong>
                                </label>
                            </div>
                        </div>

                        <!-- Credit Amount Selection (for mixed payment) -->
                        <div id="creditAmountSection" class="mb-4" style="display: none;">
                            <label for="credit_amount" class="form-label">Amount to Pay with Credit</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="credit_amount" name="credit_amount" 
                                       min="0" max="{{ $paymentData['available_credit'] }}" step="0.01" 
                                       value="{{ $paymentData['available_credit'] }}">
                            </div>
                            <small class="form-text text-muted">
                                Maximum available: ${{ number_format($paymentData['available_credit'], 2) }}
                            </small>
                        </div>

                        <!-- External Email Section -->
                        <div id="externalEmailSection" class="mb-4" style="display: none;">
                            <label for="external_email" class="form-label">Email Address for Payment Link</label>
                            <input type="email" class="form-control" id="external_email" name="external_email" 
                                   placeholder="Enter email address to send payment link">
                            <small class="form-text text-muted">
                                A secure payment link will be sent to this email address. The link will expire in 7 days.
                            </small>
                        </div>

                        <!-- Payment Summary -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6>Payment Summary</h6>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Total Amount:</strong> $<span id="summary_total">{{ number_format($paymentData['total_amount'], 2) }}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Credit Used:</strong> $<span id="summary_credit">0.00</span>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <strong>Amount to Pay:</strong> $<span id="summary_remaining">{{ number_format($paymentData['total_amount'], 2) }}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Payment Method:</strong> <span id="summary_method">Card</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="fas fa-credit-card"></i> Continue to Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing your payment selection...</p>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    const totalAmount = {{ $paymentData['total_amount'] }};
    const availableCredit = {{ $paymentData['available_credit'] }};
    
    // Show/hide sections based on payment type selection
    $('input[name="payment_type"]').change(function() {
        if ($(this).val() === 'recurring') {
            $('#recurringAmountSection').show();
        } else {
            $('#recurringAmountSection').hide();
        }
        updatePaymentSummary();
    });
    
    // Show/hide sections based on payment method selection
    $('input[name="payment_method"]').change(function() {
        const method = $(this).val();
        
        $('#creditAmountSection').toggle(method === 'mixed');
        $('#externalEmailSection').toggle(method === 'external_link');
        
        updatePaymentSummary();
    });
    
    // Update credit amount when changed
    $('#credit_amount').on('input', function() {
        updatePaymentSummary();
    });
    
    function updatePaymentSummary() {
        const paymentMethod = $('input[name="payment_method"]:checked').val();
        let creditUsed = 0;
        let methodText = 'Card';
        
        if (paymentMethod === 'credit') {
            creditUsed = Math.min(availableCredit, totalAmount);
            methodText = 'Credit';
        } else if (paymentMethod === 'mixed') {
            creditUsed = parseFloat($('#credit_amount').val()) || 0;
            methodText = 'Credit + Card';
        } else if (paymentMethod === 'external_link') {
            methodText = 'External Payment Link';
        }
        
        const remainingAmount = Math.max(0, totalAmount - creditUsed);
        
        $('#summary_total').text(totalAmount.toFixed(2));
        $('#summary_credit').text(creditUsed.toFixed(2));
        $('#summary_remaining').text(remainingAmount.toFixed(2));
        $('#summary_method').text(methodText);
        
        // Update button text
        if (paymentMethod === 'external_link') {
            $('#submitBtn').html('<i class="fas fa-envelope"></i> Send Payment Link');
        } else if (creditUsed >= totalAmount) {
            $('#submitBtn').html('<i class="fas fa-check"></i> Complete Payment with Credit');
        } else {
            $('#submitBtn').html('<i class="fas fa-credit-card"></i> Continue to Payment');
        }
    }
    
    // Form submission
    $('#paymentOptionsForm').submit(function(e) {
        e.preventDefault();
        
        // Validation
        const paymentMethod = $('input[name="payment_method"]:checked').val();
        const paymentType = $('input[name="payment_type"]:checked').val();
        
        if (paymentType === 'recurring') {
            const recurringAmount = parseFloat($('#recurring_amount').val());
            const minAmount = {{ $paymentData['minimum_recurring_amount'] ?? 0 }};
            if (!recurringAmount || recurringAmount < minAmount) {
                alert('Please enter a valid recurring payment amount.');
                return;
            }
        }
        
        if (paymentMethod === 'external_link') {
            const email = $('#external_email').val();
            if (!email || !email.includes('@')) {
                alert('Please enter a valid email address for the payment link.');
                return;
            }
        }
        
        if (paymentMethod === 'mixed') {
            const creditAmount = parseFloat($('#credit_amount').val());
            if (!creditAmount || creditAmount <= 0 || creditAmount > availableCredit) {
                alert('Please enter a valid credit amount.');
                return;
            }
        }
        
        // Show loading modal
        $('#loadingModal').modal('show');
        
        // Submit form via AJAX
        $.ajax({
            url: '{{ route("individual.program.payment.process.selection") }}',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#loadingModal').modal('hide');
                
                if (response.success) {
                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        alert(response.message || 'Payment processed successfully!');
                    }
                } else {
                    alert(response.message || 'An error occurred. Please try again.');
                }
            },
            error: function(xhr) {
                $('#loadingModal').modal('hide');
                
                let message = 'An error occurred. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join('\n');
                }
                
                alert(message);
            }
        });
    });
    
    // Initialize summary
    updatePaymentSummary();
});
</script>
@endsection
