<?php

namespace App\Policies;

use App\Models\Charge;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ChargePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Charge $charge): bool
    {
        return $user->hasRole('admin') || $user->id === $charge->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Charge $charge): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Charge $charge): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can refund the charge.
     */
    public function refund(User $user, Charge $charge): bool
    {
        return $user->hasRole('admin') && $charge->canBeRefunded();
    }

    /**
     * Determine whether the user can download the receipt.
     */
    public function downloadReceipt(User $user, Charge $charge): bool
    {
        return $user->hasRole('admin') || $user->id === $charge->user_id;
    }
}
