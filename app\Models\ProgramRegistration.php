<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProgramRegistration extends Model
{
    use HasFactory;
    protected $fillable = ['user_id', 'program_id', 'amount', 'player_id', 'team_id', 'is_paid', 'pending_amount'];
    protected $table = 'program_registrations';


    public function program()
    {
        return $this->belongsTo(Program::class, 'program_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id'); // Guardian/user who registered
    }

    // public function player()
    // {
    //     return $this->belongsTo(User::class, 'player_id'); // Player being registered
    // }
}
