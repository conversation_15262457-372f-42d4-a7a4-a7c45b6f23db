<style>
    table {
        border-collapse: collapse;
        width: 100%;
        font-family: Arial, sans-serif;
    }

    th, td {
        border: 1px solid #ccc;
        padding: 8px 10px;
        text-align: left;
        font-weight: normal; 
        background-color: transparent; /* no bg color */
    }

    thead tr:first-child th {
        font-size: 1.4rem;
    }

    thead tr:nth-child(2) th {
        font-size: 1.1rem;
    }
</style>

<table>
    <thead>
        <tr>
            <th colspan="6" style="text-align:center;">User Info</th>
        </tr>
        <tr>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
            <th>Town</th>
            <th>Gender</th>
            <th>Roles</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($users as $user)

            <tr>
                <td>{{ $user->firstName }}</td>
                <td>{{ $user->lastName }}</td>
                <td>{{ $user->email }}</td>
                <td>{{ $user->town ?? '' }}</td>
                <td>{{ $user->gender ?? '' }}</td>
                <td>{{ $user->roles->pluck('name')->implode(', ') }}</td>
            </tr>

            @if ($user->teams && $user->teams->count() > 0)
                <tr>
                    <td colspan="6">
                        Teams: {{ $user->teams->pluck('name')->implode(', ') }}
                    </td>
                </tr>
            @endif

            @php
                $roles = $user->roles->pluck('name')->toArray();
                $excludeRoles = ['guardian', 'coach'];
                $hasExcludeRole = count(array_intersect($roles, $excludeRoles)) > 0;
            @endphp

            @if (!$hasExcludeRole && optional($user->programRegistrations)->count() > 0)
                <tr>
                    <td colspan="6">Programs for {{ $user->firstName }} {{ $user->lastName }}</td>
                </tr>
                <tr>
                    <th colspan="2">Program Name</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th colspan="2">Type</th>
                </tr>
                @foreach ($user->programRegistrations as $program)
                    <tr>
                        <td colspan="2">{{ $program->name }}</td>
                        <td>{{ \Carbon\Carbon::parse($program->start_date)->format('M d, Y') }}</td>
                        <td>{{ \Carbon\Carbon::parse($program->end_date)->format('M d, Y') }}</td>
                        <td colspan="2">{{ $program->type ?? 'N/A' }}</td>
                    </tr>
                @endforeach
            @elseif (!$hasExcludeRole)
                <tr>
                    <td colspan="6" style="text-align:center; color:red; font-style: italic;">
                        No programs found for {{ $user->firstName }}
                    </td>
                </tr>
            @endif

            <tr><td colspan="6" style="height: 20px;"></td></tr>

        @endforeach
    </tbody>
</table>
