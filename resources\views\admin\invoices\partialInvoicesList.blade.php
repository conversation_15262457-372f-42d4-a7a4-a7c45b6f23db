<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Payment Type</th>
                <th>Date</th>
                <th>Refund Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($invoices as $invoice)
                <tr>
                    <td>
                        <a href="{{ route('admin.invoices.show', $invoice) }}" class="text-decoration-none">
                            {{ $invoice->stripe_invoice_id ?: 'INV-' . $invoice->id }}
                        </a>
                    </td>
                    <td>
                        {{ $invoice->user->firstName }} {{ $invoice->user->lastName }}
                        <br><small class="text-muted">{{ $invoice->user->email }}</small>
                    </td>
                    <td>${{ number_format($invoice->amount, 2) }}</td>
                    <td>
                        <span class="badge bg-{{ $invoice->status === 'paid' ? 'success' : ($invoice->status === 'open' ? 'warning' : 'secondary') }}">
                            {{ ucfirst($invoice->status) }}
                        </span>
                    </td>
                    <td>
                        <span class="badge bg-info">
                            {{ ucfirst(str_replace('_', ' ', $invoice->payment_type ?? 'unknown')) }}
                        </span>
                    </td>
                    <td>{{ $invoice->created_at->format('M d, Y') }}</td>
                    <td>
                        @if($invoice->refund_status === 'full')
                            <span class="badge bg-danger">Fully Refunded</span>
                        @elseif($invoice->refund_status === 'partial')
                            <span class="badge bg-warning">Partially Refunded</span>
                        @else
                            <span class="badge bg-success">No Refunds</span>
                        @endif
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{{ route('admin.invoices.show', $invoice) }}"
                               class="btn btn-sm btn-outline-primary"
                               title="View Details">
                                <i class="bi bi-eye"></i>
                            </a>

                            @if($invoice->stripe_invoice_id)
                                <a href="{{ route('admin.invoices.download', $invoice) }}"
                                   class="btn btn-sm btn-outline-secondary"
                                   title="Download PDF">
                                    <i class="bi bi-download"></i>
                                </a>
                            @endif

                            @if($invoice->canBeRefunded())
                                <button type="button"
                                        class="btn btn-sm btn-outline-warning"
                                        title="Process Refund"
                                        onclick="showRefundModal({{ $invoice->id }}, {{ $invoice->amount }}, {{ $invoice->refunded_amount }})">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-inbox fs-1"></i>
                            <p class="mt-2">No invoices found</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@if($invoices->hasPages())
    <div class="d-flex justify-content-center">
        {{ $invoices->links('pagination::bootstrap-5') }}
    </div>
@endif
