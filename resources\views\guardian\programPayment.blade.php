@extends('layouts.app')

@section('title', 'payment')
@section('css')

<style>
.coupon-container {
    background: #f8f9fa;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.coupon-container:hover {
    border-color: #0b4499;
    background: #f0f4ff;
}

.coupon-container.applied {
    background: #e8f5e8;
    border-color: #28a745;
    border-style: solid;
}

.coupon-input-wrapper {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.coupon-input-wrapper:focus-within {
    border-color: #0b4499;
    box-shadow: 0 0 0 3px rgba(11, 68, 153, 0.1);
}

.coupon-input {
    flex: 1;
    border: none;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    background: transparent;
}

.coupon-input::placeholder {
    color: #999;
}

.coupon-btn {
    background: #0b4499;
    color: white;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.coupon-btn:hover {
    background: #083366;
}

.coupon-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.coupon-btn.remove {
    background: #dc3545;
}

.coupon-btn.remove:hover {
    background: #c82333;
}

.coupon-icon {
    position: absolute;
    top: -10px;
    left: 20px;
    background: #0b4499;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.coupon-message {
    margin-top: 8px;
    font-size: 13px;
    font-weight: 500;
    min-height: 18px;
    display: flex;
    align-items: center;
}

.coupon-message.success {
    color: #28a745;
}

.coupon-message.error {
    color: #dc3545;
}

.coupon-message.info {
    color: #17a2b8;
}

.coupon-message:not(:empty)::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    margin-right: 8px;
    display: inline-block;
}
</style>
@endsection
@section('content')

    <div id="errorMessage">
        <span id="errorText"></span>
    </div>

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payment</h1>
        <div class="mt-3">
            <h2 class="text-uppercase" style="color: inherit;">
                <span style="color: #0b4499; font-size: 1.2em;">$<span id="paymentAmount"></span></span>
            </h2>
        </div>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex flex-column-reverse">
                    <h3 class="text-uppercase mb-4">Payment Options:</h3>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 payment-form">
                    <form class="form" id="paymentForm">
                        <!-- Full Amount Payment Option -->
                        <div class="row mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="fullAmount" type="radio" name="payment"
                                    value="fullAmount" />
                                <label class="form-check-label text-uppercase" for="fullAmount">
                                    I will be paying this amount in full
                                </label>
                            </div>
                        </div>

                        <!-- Specific Amount Payment Option -->
                        @if($program->payment=="split")
                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="specificAmount" type="radio" name="payment"
                                    value="specificAmount" />
                                <label class="form-check-label text-uppercase" for="specificAmount">
                                    I will be paying
                                </label>
                            </div>
                            <div class="col-md-auto">
                                <input class="form-control" id="specificMoney" type="number" step="0.01"
                                    placeholder="Enter amount" />
                            </div>

                        </div>
                        @endif
                        @if($program->payment == 'recurring')
                        <!-- Recurring Payments Option -->
                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="recurringPayments" type="radio" name="payment"
                                    value="recurringPayments" />
                                <label class="form-check-label text-uppercase" for="recurringPayments">
                                    I will be making recurring payments
                                </label>
                            </div>
                        </div>

                        <!-- Recurring Payment Details -->
                        <div id="recurringDetails" class="row mb-4" style="display: none;">
                            <div class="col-12">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">Recurring Payment Details</h6>
                                        <div class="mb-3">
                                            <strong>Down Payment:</strong> $<span id="downPaymentAmount">{{ $program->down_payment ?? 0 }}</span>
                                            <small class="text-muted d-block">This amount will be charged immediately</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="installmentMonths" class="form-label">Select installment period:</label>
                                            <select class="form-select" id="installmentMonths">
                                                @for($i = 1; $i <= ($program->up_to_months ?? 6); $i++)
                                                    <option value="{{ $i }}">{{ $i }} month{{ $i > 1 ? 's' : '' }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <strong>Monthly Payment:</strong> $<span id="monthlyPaymentAmount">0.00</span>
                                            <small class="text-muted d-block">Remaining amount divided by selected months</small>
                                        </div>
                                        <div class="alert alert-info">
                                            <small>
                                                <strong>Payment Structure:</strong><br>
                                                • Down payment: $<span id="downPaymentDisplay">{{ $program->down_payment ?? 0 }}</span> (paid now)<br>
                                                • Monthly payments: $<span id="monthlyDisplay">0.00</span> × <span id="monthsDisplay">1</span> month(s)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="otherGuardian" type="radio" name="payment"
                                    value="otherGuardian" />
                                <label class="form-check-label text-uppercase" for="invoiceEmail">
                                    This payment will be paid by
                                </label>
                            </div>
                            <div class="col-md-auto">
                                <input class="form-control" id="invoiceEmail" type="email"
                                    placeholder="Enter email address" />
                            </div>
                        </div>

                        <!-- Coupon Code Section -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="coupon-container" id="couponContainer">
                                    <div class="coupon-icon">%</div>
                                    <div class="mb-2">
                                        <small class="text-muted text-uppercase fw-bold">Have a coupon code?</small>
                                    </div>
                                    <div class="coupon-input-wrapper">
                                        <input class="coupon-input" id="couponCode" type="text" placeholder="Enter coupon code" />
                                        <button type="button" class="coupon-btn" id="applyCouponBtn">Apply</button>
                                    </div>
                                    <div id="couponMessage" class="coupon-message"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Credit Payment Option -->
                        <div class="row align-items-center mb-4" id="creditPaymentOption">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="creditPayment" type="checkbox"
                                    value="creditPayment" />
                                <label class="form-check-label text-uppercase" for="creditPayment">
                                    Use available credit ($<span id="availableCreditAmount">0.00</span>)
                                </label>
                                <small class="text-muted d-block mt-1">
                                    <span id="creditPaymentDescription">Credit will be applied automatically. If insufficient, you'll pay the remaining amount.</span>
                                </small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-5">
                            <div class="col text-center">
                                <button type="submit" class="cta px-4 py-2">Proceed to Payment</button>
                            </div>
                        </div>


                    </form>
                </div>
            </div>

        </div>
    </section>
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
    <script>
        class PaymentManager {
            constructor(paymentData) {
                this.paymentData = paymentData;
                this.originalAmount = paymentData.totalAmountToBePaid;
                this.availableCredit = 0;
                this.discountAmount = 0;
                this.appliedCoupon = null;
                this.paymentAmountAfterCredit = 0;
                this.usedCredit = 0;

                this.elements = this.initializeElements();
                this.init();
            }

            initializeElements() {
                return {
                    paymentAmount: document.getElementById('paymentAmount'),
                    fullAmountRadio: document.getElementById('fullAmount'),
                    specificAmountRadio: document.getElementById('specificAmount'),
                    recurringPaymentsRadio: document.getElementById('recurringPayments'),
                    otherGuardianRadio: document.getElementById('otherGuardian'),
                    creditPaymentInput: document.getElementById('creditPayment'),
                    invoiceEmail: document.getElementById('invoiceEmail'),

                    specificMoney: document.getElementById('specificMoney'),
                    applyCouponBtn: document.getElementById('applyCouponBtn'),
                    couponCodeInput: document.getElementById('couponCode'),
                    couponMessage: document.getElementById('couponMessage'),
                    paymentForm: document.getElementById('paymentForm'),
                    errorMessageDiv: document.getElementById('errorMessage'),
                    errorText: document.getElementById('errorText')
                };
            }

            init() {
                this.updatePaymentDisplay();
                this.checkAvailableCredit();
                this.setupEventListeners();
                this.configurePaymentOptions();
            }

            setupEventListeners() {
                this.elements.applyCouponBtn.addEventListener('click', () => this.handleCouponAction());
                document.querySelectorAll('input[name="payment"]').forEach(radio => {
                    radio.addEventListener('change', () => {
                        this.updatePaymentAmount();
                        this.toggleRecurringDetails();
                    });
                });
                
                const installmentSelect = document.getElementById('installmentMonths');
                if (installmentSelect) {
                    installmentSelect.addEventListener('change', () => this.calculateMonthlyPayment());
                }
                
                if (this.elements.creditPaymentInput) {
                    this.elements.creditPaymentInput.addEventListener('change', () => this.updatePaymentAmount());
                }
                
                this.elements.paymentForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
            }

            async handleCouponAction() {
                if (this.appliedCoupon) {
                    this.removeCoupon();
                } else {
                    await this.applyCoupon();
                }
            }

            async applyCoupon() {
                const couponCode = this.elements.couponCodeInput.value.trim();
                if (!couponCode) {
                    this.showCouponMessage('Please enter a coupon code', 'error');
                    return;
                }

                this.toggleCouponButton(true, 'Applying...');

                try {
                    const response = await fetch('/guardian/apply-coupon', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            coupon_code: couponCode,
                            program_id: this.paymentData.program_id,
                            original_amount: this.originalAmount
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.appliedCoupon = data.coupon;
                        this.discountAmount = parseFloat(data.discount_amount) || 0;
                        this.paymentData.totalAmountToBePaid = this.originalAmount - this.discountAmount;

                        this.updatePaymentDisplay();
                        this.showCouponMessage(`Coupon applied! You saved $${this.discountAmount.toFixed(2)}`, 'success');

                        this.elements.couponCodeInput.disabled = true;
                        this.elements.applyCouponBtn.textContent = 'Remove';
                        this.elements.applyCouponBtn.classList.add('remove');
                    } else {
                        this.showCouponMessage(data.message || 'Invalid coupon code', 'error');
                    }
                } catch (error) {
                    console.error('Error applying coupon:', error);
                    this.showCouponMessage('Error applying coupon. Please try again.', 'error');
                } finally {
                    this.toggleCouponButton(false);
                }
            }

            removeCoupon() {
                this.appliedCoupon = null;
                this.discountAmount = 0;
                this.paymentData.totalAmountToBePaid = parseFloat(this.originalAmount);

                this.updatePaymentDisplay();
                this.showCouponMessage('Coupon removed', 'info');

                this.elements.couponCodeInput.disabled = false;
                this.elements.couponCodeInput.value = '';
                this.elements.applyCouponBtn.textContent = 'Apply';
                this.elements.applyCouponBtn.classList.remove('remove');
                document.getElementById('couponContainer').classList.remove('applied');
            }

            toggleCouponButton(disabled, text = null) {
                this.elements.applyCouponBtn.disabled = disabled;
                if (text) this.elements.applyCouponBtn.textContent = text;
                else if (!disabled && this.elements.applyCouponBtn.textContent === 'Applying...') {
                    this.elements.applyCouponBtn.textContent = 'Apply';
                }
            }

            showCouponMessage(message, type) {
                this.elements.couponMessage.textContent = message;
                this.elements.couponMessage.className = `coupon-message ${type}`;
                
                const container = document.getElementById('couponContainer');
                if (type === 'success') {
                    container.classList.add('applied');
                } else {
                    container.classList.remove('applied');
                }

                if (type !== 'success') {
                    setTimeout(() => {
                        this.elements.couponMessage.textContent = '';
                        this.elements.couponMessage.className = 'coupon-message';
                    }, 5000);
                }
            }

            async checkAvailableCredit() {
                try {
                    const response = await fetch('/guardian/check-credit', {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    const data = await response.json();

                    if (data.success && data.available_credit > 0) {
                        this.availableCredit = parseFloat(data.available_credit);
                        this.updateCreditDisplay();
                        document.getElementById('creditPaymentOption').style.display = 'block';
                    } else {
                        document.getElementById('creditPaymentOption').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error checking credit:', error);
                    document.getElementById('creditPaymentOption').style.display = 'none';
                }
            }

            updateCreditDisplay() {
                const totalAmount = parseFloat(this.paymentData.totalAmountToBePaid) || 0;
                document.getElementById('availableCreditAmount').textContent = this.availableCredit.toFixed(2);

                const creditInput = this.elements.creditPaymentInput;
                const descriptionElement = document.getElementById('creditPaymentDescription');
                
                if (this.availableCredit >= totalAmount) {
                    creditInput.type = 'radio';
                    creditInput.name = 'payment';
                    descriptionElement.textContent = 'Your credit covers the full amount. Registration will be completed automatically.';
                } else {
                    creditInput.type = 'checkbox';
                    creditInput.removeAttribute('name');
                    const remaining = totalAmount - this.availableCredit;
                    descriptionElement.textContent = `Credit will cover $${this.availableCredit.toFixed(2)}. You'll pay the remaining $${remaining.toFixed(2)} with card.`;
                }
                this.validateRecurringPayment();
            }

            configurePaymentOptions() {
                const paymentType = this.paymentData.programPayment;
                const elements = this.elements;

                if (paymentType === 'split') {
                    this.disableElements([elements.recurringPaymentsRadio]);
                    this.hideElements([elements.recurringPaymentsRadio]);
                    this.enableElements([elements.fullAmountRadio, elements.specificAmountRadio, elements.otherGuardianRadio]);
                } else if (paymentType === 'full') {
                    this.disableElements([elements.specificMoney, elements.recurringPaymentsRadio, elements.specificAmountRadio]);
                    this.enableElements([elements.fullAmountRadio, elements.otherGuardianRadio]);
                } else {
                    this.disableElements([elements.specificMoney, elements.specificAmountRadio]);
                    this.enableElements([elements.recurringPaymentsRadio, elements.fullAmountRadio, elements.otherGuardianRadio]);
                }
            }

            disableElements(elements) {
                elements.forEach(el => el && (el.disabled = true));
            }

            enableElements(elements) {
                elements.forEach(el => el && (el.disabled = false));
            }

            hideElements(elements) {
                elements.forEach(el => el && (el.style.display = 'none'));
            }

            getCurrentEffectiveAmount() {
                let effectiveAmount = this.originalAmount - this.discountAmount;
                if (this.elements.creditPaymentInput?.checked) {
                    effectiveAmount = Math.max(0, effectiveAmount - this.availableCredit);
                }
                return effectiveAmount;
            }

            updatePaymentAmount() {
                let displayAmount = 0;
                
                // Set credit usage
                if (this.elements.creditPaymentInput?.checked) {
                    const totalAfterDiscount = this.originalAmount - this.discountAmount;
                    this.usedCredit = Math.min(this.availableCredit, totalAfterDiscount);
                } else {
                    this.usedCredit = 0;
                }
                
                // Calculate total remaining after discounts and credits
                const totalRemaining = this.originalAmount - this.discountAmount - this.usedCredit;
                
                console.log('Debug payment calculation:', {
                    originalAmount: this.originalAmount,
                    discountAmount: this.discountAmount,
                    usedCredit: this.usedCredit,
                    totalRemaining: totalRemaining,
                    recurringSelected: this.elements.recurringPaymentsRadio?.checked,
                    downPayment: this.paymentData.downPayment
                });
                
                if (this.elements.recurringPaymentsRadio?.checked) {
                    // For recurring payments, user pays the down payment amount
                    const downPayment = parseFloat(this.paymentData.downPayment) || 0;
                    displayAmount = downPayment; // Always show down payment for recurring
                } else {
                    // For other payment types, show the remaining amount after all discounts
                    displayAmount = Math.max(0, totalRemaining);
                }
                
                console.log('Final display amount:', displayAmount);
                
                this.paymentAmountAfterCredit = displayAmount;
                this.elements.paymentAmount.textContent = displayAmount.toFixed(2);
                this.validateRecurringPayment();
            }

            validateRecurringPayment() {
                const downPayment = parseFloat(this.paymentData.downPayment) || 0;
                const effectiveAmount = this.getCurrentEffectiveAmount();
                const recurringRadio = this.elements.recurringPaymentsRadio;
                
                if (downPayment > effectiveAmount) {
                    if (recurringRadio) {
                        recurringRadio.disabled = true;
                        recurringRadio.checked = false;
                    }
                    document.getElementById('recurringDetails').style.display = 'none';
                } else {
                    if (recurringRadio) {
                        recurringRadio.disabled = false;
                    }
                }
            }

            toggleRecurringDetails() {
                const recurringDetails = document.getElementById('recurringDetails');
                if (recurringDetails) {
                    if (this.elements.recurringPaymentsRadio?.checked) {
                        recurringDetails.style.display = 'block';
                        this.calculateMonthlyPayment();
                    } else {
                        recurringDetails.style.display = 'none';
                    }
                }
            }

            calculateMonthlyPayment() {
                const totalAfterDiscount = this.originalAmount - this.discountAmount;
                const downPayment = parseFloat(this.paymentData.downPayment) || 0;
                const months = parseInt(document.getElementById('installmentMonths')?.value) || 1;
                
                // Calculate what remains after down payment and credits
                let remainingAfterDownPayment = totalAfterDiscount - downPayment;
                if (this.elements.creditPaymentInput?.checked) {
                    remainingAfterDownPayment -= this.usedCredit;
                }
                
                const monthlyPayment = Math.max(0, remainingAfterDownPayment / months);

                document.getElementById('monthlyPaymentAmount').textContent = monthlyPayment.toFixed(2);
                document.getElementById('monthlyDisplay').textContent = monthlyPayment.toFixed(2);
                document.getElementById('monthsDisplay').textContent = months;
                document.getElementById('downPaymentDisplay').textContent = downPayment.toFixed(2);
            }

            updatePaymentDisplay() {
                this.paymentData.totalAmountToBePaid = this.originalAmount - this.discountAmount;
                this.updatePaymentAmount();
                this.updateCreditDisplay();
            }

            showError(message) {
                this.elements.errorText.textContent = message;
                this.elements.errorMessageDiv.style.display = 'block';
                setTimeout(() => {
                    this.elements.errorMessageDiv.style.display = 'none';
                }, 2000);
            }

            validateForm() {
                const elements = this.elements;

                // Check if recurring payment is selected but not valid
                if (elements.recurringPaymentsRadio?.checked) {
                    const downPayment = parseFloat(this.paymentData.downPayment) || 0;
                    const effectiveAmount = this.getCurrentEffectiveAmount();
                    
                    if (downPayment > effectiveAmount) {
                        this.showError('Down payment exceeds remaining amount after discounts and credits. Please select a different payment option.');
                        return false;
                    }
                }

                const paymentOptions = [elements.fullAmountRadio, elements.recurringPaymentsRadio,
                                     elements.specificAmountRadio, elements.otherGuardianRadio];
                
                // Add credit payment if it's a radio (full coverage)
                if (elements.creditPaymentInput?.type === 'radio') {
                    paymentOptions.push(elements.creditPaymentInput);
                }

                if (!paymentOptions.some(radio => radio?.checked)) {
                    this.showError('Please select a valid payment option');
                    return false;
                }

                return true;
            }

            getPaymentData() {
                const elements = this.elements;
                let paymentType, paymentAmount = 0, email = '', installmentMonths = null;

                if (elements.fullAmountRadio?.checked) {
                    paymentType = 'fullAmount';
                    paymentAmount = this.paymentData.totalAmountToBePaid;
                } else if (elements.specificAmountRadio?.checked && elements.specificMoney) {
                    paymentType = 'specificAmount';
                    paymentAmount = parseFloat(elements.specificMoney.value);
                } else if (elements.recurringPaymentsRadio?.checked) {
                    paymentType = 'recurringPayments';
                    paymentAmount = parseFloat(this.paymentData.downPayment) || 0;
                    installmentMonths = parseInt(document.getElementById('installmentMonths')?.value) || 1;
                } else if (elements.otherGuardianRadio?.checked) {
                    paymentType = 'otherGuardian';
                    email = elements.invoiceEmail.value;
                } else if (elements.creditPaymentInput?.type === 'radio' && elements.creditPaymentInput?.checked) {
                    paymentType = 'creditPayment';
                    paymentAmount = this.paymentAmountAfterCredit;
                }

                if (['specificAmount'].includes(paymentType) && isNaN(paymentAmount)) {
                    this.showError('Please enter valid details');
                    return null;
                }

                if (paymentType === 'otherGuardian' && !email) {
                    this.showError('Please enter a valid email address.');
                    return null;
                }

                return { paymentType, paymentAmount, email, installmentMonths };
            }

            buildFormData(paymentType, paymentAmount, email, installmentMonths = null) {
                const baseData = {
                    user_id: this.paymentData.user_id,
                    program_id: this.paymentData.program_id,
                    payment_type: paymentType,
                    paidAmount: paymentType === 'creditPayment' ? parseFloat(paymentAmount) : paymentAmount,
                    totalAmountToBePaid: this.paymentData.totalAmountToBePaid,
                    email,
                    player_ids: this.paymentData.player_ids,
                    applied_coupon: this.appliedCoupon,
                    discount_amount: this.discountAmount
                };

                if (paymentType === 'creditPayment' || this.elements.creditPaymentInput?.checked) {
                    baseData.used_credit = this.usedCredit;
                }

                if (paymentType === 'recurringPayments' && installmentMonths) {
                    const totalAfterDiscount = this.originalAmount - this.discountAmount;
                    const downPayment = parseFloat(this.paymentData.downPayment) || 0;
                    
                    // Calculate remaining amount after down payment and credits
                    let remainingAfterDownPayment = totalAfterDiscount - downPayment;
                    if (this.elements.creditPaymentInput?.checked) {
                        remainingAfterDownPayment -= this.usedCredit;
                    }
                    
                    baseData.installment_months = installmentMonths;
                    baseData.down_payment = downPayment;
                    baseData.monthly_payment = Math.max(0, remainingAfterDownPayment / installmentMonths);
                }

                return baseData;
            }

            async handleFormSubmit(e) {
                e.preventDefault();

                if (!this.validateForm()) return;

                const paymentData = this.getPaymentData();
                if (!paymentData) return;

                const { paymentType, paymentAmount, email, installmentMonths } = paymentData;
                const formData = this.buildFormData(paymentType, paymentAmount, email, installmentMonths);
                
                // Add checkout data
                formData.original_amount = this.originalAmount;

                // Handle $0 payments for recurring payments
                if (paymentType === 'recurringPayments' && paymentAmount === 0) {
                    this.showError('Cannot create recurring payments when down payment is $0. Please select full payment or adjust your credits/coupons.');
                    return;
                }
                
                try {
                    const response = await fetch('{{ route("guardian.prepare-checkout") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(formData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        window.location.href = data.checkout_url;
                    } else {
                        this.showError('Error: ' + (data.message || 'An error occurred'));
                    }
                } catch (error) {
                    console.error('Error preparing checkout:', error);
                    this.showError('An error occurred. Please try again.');
                }
            }

            handlePaymentSuccess(data, paymentType) {
                if (paymentType === 'creditPayment') {
                    if (data.fully_paid) {
                        alert('Registration completed successfully');
                        window.location.href = route('guardian.dashboard');
                    } else {
                        window.location.href = data.redirect_url;
                    }
                } else if (paymentType === 'otherGuardian') {
                    if (data.external_payment_sent) {
                        alert(`Payment link has been sent to ${data.external_email}. They will receive an email with payment instructions.`);
                        window.location.href = data.redirect_url;
                    } else {
                        this.showError('Failed to send payment link: ' + (data.error || 'An error occurred'));
                    }
                } else {
                    window.location.href = data.redirect_url;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const paymentData = @json(session('payment_data'));
            paymentData.downPayment = {{ $program->down_payment ?? 0 }};
            paymentData.upToMonths = {{ $program->up_to_months ?? 6 }};
            new PaymentManager(paymentData);
        });
    </script>
@endsection
