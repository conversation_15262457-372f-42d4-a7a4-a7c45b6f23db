<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique(); // Unique identifier for this transaction
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // Guardian who initiated payment
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->json('player_ids'); // Array of player IDs being registered
            
            // Payment Details
            $table->decimal('total_amount', 10, 2); // Total program cost
            $table->decimal('paid_amount', 10, 2)->default(0); // Amount actually paid via card/external
            $table->decimal('credit_used', 10, 2)->default(0); // Amount paid using credits
            $table->decimal('pending_amount', 10, 2)->default(0); // Remaining amount to be paid
            
            // Payment Method & Type
            $table->enum('payment_method', ['card', 'credit', 'external_link', 'mixed']); // How payment was made
            $table->enum('payment_type', ['full', 'split', 'recurring']); // Payment structure
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
            
            // External Payment Link Details
            $table->string('external_email')->nullable(); // Email for external payment
            $table->string('external_payment_token')->nullable(); // Secure token for external payment
            $table->timestamp('external_link_sent_at')->nullable();
            $table->timestamp('external_link_expires_at')->nullable();
            $table->enum('external_payment_status', ['not_applicable', 'link_sent', 'link_opened', 'payment_completed', 'link_expired'])->default('not_applicable');
            
            // Stripe Integration
            $table->string('stripe_payment_intent_id')->nullable();
            $table->string('stripe_charge_id')->nullable();
            $table->string('stripe_subscription_id')->nullable(); // For recurring payments
            
            // Metadata
            $table->json('payment_metadata')->nullable(); // Additional payment details
            $table->json('credit_usage_details')->nullable(); // Details of which credits were used
            
            // Timestamps
            $table->timestamp('payment_completed_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'program_id']);
            $table->index(['status', 'payment_method']);
            $table->index('external_payment_token');
            $table->index('stripe_payment_intent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
