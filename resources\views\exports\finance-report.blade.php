@extends('layouts.app')

@section('content')
<style>
    table {
        border-collapse: collapse;
        width: 100%;
        font-family: Arial, sans-serif;
    }

    th, td {
        border: 1px solid #ccc;
        padding: 8px 10px;
        text-align: left;
    }

    thead tr:first-child th {
        font-size: 1.4rem;
        font-weight: 900;
    }

    th {
        background-color: #f8f9fa;
    }

    .player-row {
        background-color: #e2f0d9; /* light green */
    }

    .finance-row {
        background-color: #fff3cd; /* light yellow */
    }

    .no-data {
        text-align: center;
        font-style: italic;
        color: red;
    }

    .btn-export {
        margin: 20px 0;
    }
</style>

<div class="container mt-4">
    <h2>Finance Report</h2>
    <a href="{{ route('admin.export.finance.report') }}" class="btn btn-success btn-export">Download Excel Report</a>

    <table>
        <thead>
            <tr>
                <th>Player Name</th>
                <th>Player Email</th>
                <th>Guardian Email</th>
                <th>Program</th>
                <th>Total Fee</th>
                <th>Pending</th>
                <th>Payment Type</th>
            </tr>
        </thead>
        <tbody>
            @forelse($registrations as $registration)
                <tr class="player-row">
                    <td>{{ $registration['player_name'] }}</td>
                    <td>{{ $registration['player_email'] }}</td>
                    <td>{{ $registration['guardian_email'] ?? 'N/A' }}</td>
                    <td>{{ $registration['program_name'] }}</td>
                    <td>${{ $registration['total_amount'] }}</td>
                    <td>${{ $registration['paid_amount'] }}</td>
                    <td>${{ $registration['pending_amount'] }}</td>
                    <td>{{ ucfirst($registration['payment_type']) }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="no-data">No finance records found.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
@endsection
