<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Program;
use App\Models\ExternalPaymentLink;
use App\Models\PaymentTransaction;
use App\Services\PaymentTransactionService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;

class ExternalPaymentTest extends TestCase
{
    use WithFaker;

    public function test_external_payment_link_creation()
    {
        // Create test data
        $guardian = User::factory()->create([
            'email' => '<EMAIL>',
            'firstName' => 'John',
            'lastName' => 'Doe',
        ]);

        $program = Program::factory()->create([
            'name' => 'Test Program',
            'cost' => 100.00,
            'payment' => 'full',
        ]);

        $player = User::factory()->create([
            'parent_id' => $guardian->id,
            'firstName' => 'Jane',
            'lastName' => 'Doe',
        ]);

        $externalEmail = '<EMAIL>';

        // Mock the payment service
        $paymentService = $this->app->make(PaymentTransactionService::class);

        // Simulate the external payment request
        $response = $this->actingAs($guardian)
            ->postJson(route('guardian.paymentMethod'), [
                'user_id' => $guardian->id,
                'program_id' => $program->id,
                'payment_type' => 'otherGuardian',
                'paidAmount' => 100.00,
                'totalAmountToBePaid' => 100.00,
                'player_ids' => [$player->id],
                'email' => $externalEmail,
            ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'external_payment_sent' => true,
                'external_email' => $externalEmail,
            ]);

        // Assert database records were created
        $this->assertDatabaseHas('payment_transactions', [
            'user_id' => $guardian->id,
            'program_id' => $program->id,
            'external_email' => $externalEmail,
            'payment_method' => 'external_link',
        ]);

        $this->assertDatabaseHas('external_payment_links', [
            'requesting_user_id' => $guardian->id,
            'program_id' => $program->id,
            'external_email' => $externalEmail,
            'amount_to_pay' => 100.00,
        ]);
    }

    public function test_external_payment_validation()
    {
        $guardian = User::factory()->create();
        $program = Program::factory()->create();

        // Test missing email for otherGuardian payment type
        $response = $this->actingAs($guardian)
            ->postJson(route('guardian.paymentMethod'), [
                'user_id' => $guardian->id,
                'program_id' => $program->id,
                'payment_type' => 'otherGuardian',
                'paidAmount' => 100.00,
                'totalAmountToBePaid' => 100.00,
                'player_ids' => [1],
                // Missing email
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);

        // Test invalid email format
        $response = $this->actingAs($guardian)
            ->postJson(route('guardian.paymentMethod'), [
                'user_id' => $guardian->id,
                'program_id' => $program->id,
                'payment_type' => 'otherGuardian',
                'paidAmount' => 100.00,
                'totalAmountToBePaid' => 100.00,
                'player_ids' => [1],
                'email' => 'invalid-email',
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_external_payment_link_access()
    {
        // Create external payment link
        $link = ExternalPaymentLink::factory()->create([
            'external_email' => '<EMAIL>',
            'amount_to_pay' => 100.00,
            'status' => 'sent',
        ]);

        // Test accessing the external payment page
        $response = $this->get(route('external.payment.show', ['token' => $link->token]));

        $response->assertStatus(200)
            ->assertViewIs('external.payment-form')
            ->assertViewHas('paymentData');

        // Assert link was marked as opened
        $link->refresh();
        $this->assertEquals('opened', $link->status);
        $this->assertNotNull($link->opened_at);
        $this->assertEquals(1, $link->open_count);
    }

    public function test_external_payment_link_expiration()
    {
        // Create expired external payment link
        $link = ExternalPaymentLink::factory()->create([
            'expires_at' => now()->subDay(),
            'status' => 'sent',
        ]);

        // Test accessing expired link
        $response = $this->get(route('external.payment.show', ['token' => $link->token]));

        $response->assertStatus(200)
            ->assertViewIs('external.payment-expired');
    }

    public function test_external_payment_link_not_found()
    {
        // Test accessing non-existent link
        $response = $this->get(route('external.payment.show', ['token' => 'invalid-token']));

        $response->assertStatus(200)
            ->assertViewIs('external.payment-not-found');
    }
}
