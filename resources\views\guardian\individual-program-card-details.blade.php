@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">

                <div class="card-header">
                    <h4>Payment Details - {{ $paymentData['program_name'] }}</h4>
                </div>
                <div class="card-body">
                    <!-- Payment Summary -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6>Payment Summary</h6>
                            <div class="row">
                                <div class="col-6">
                                    <strong>Program:</strong><br>
                                    {{ $paymentData['program_name'] }}
                                </div>
                                <div class="col-6">
                                    <strong>Players:</strong><br>
                                    @foreach($paymentData['player_names'] as $name)
                                        {{ $name }}@if(!$loop->last), @endif
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>Total Amount:</strong><br>
                                    ${{ number_format($paymentData['transaction']->total_amount, 2) }}
                                </div>
                                <div class="col-6">
                                    <strong>Credit Used:</strong><br>
                                    ${{ number_format($paymentData['credit_used'], 2) }}
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <h5 class="text-primary">
                                        <strong>Amount to Pay: ${{ number_format($paymentData['remaining_amount'], 2) }}</strong>
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <form id="cardPaymentForm">
                        @csrf

                        <!-- Card Holder Information -->
                        <div class="mb-3">
                            <label for="card_holder_name" class="form-label">Card Holder Name *</label>
                            <input type="text" class="form-control" id="card_holder_name" name="card_holder_name"
                                   value="{{ Auth::user()->name }}" required>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-3">
                            <label for="address" class="form-label">Billing Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="3"
                                      placeholder="Enter your billing address" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="state" class="form-label">State *</label>
                            <input type="text" class="form-control" id="state" name="state"
                                   placeholder="Enter your state" required>
                        </div>

                        <!-- Stripe Card Element -->
                        <div class="mb-3">
                            <label class="form-label">Card Information *</label>
                            <div id="card-element" class="form-control" style="height: 40px; padding: 10px;">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms_accepted" required>
                            <label class="form-check-label" for="terms_accepted">
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and
                                <a href="#" target="_blank">Privacy Policy</a> *
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="fas fa-lock"></i> Pay ${{ number_format($paymentData['remaining_amount'], 2) }}
                            </button>
                        </div>

                        <!-- Security Notice -->
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> Your payment information is secure and encrypted
                            </small>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Processing...</span>
                </div>
                <p class="mt-2 mb-0">Processing your payment...</p>
                <small class="text-muted">Please do not close this window</small>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
$(document).ready(function() {
    // Initialize Stripe
    const stripe = Stripe('{{ env("STRIPE_KEY") }}');
    const elements = stripe.elements();

    // Create card element
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                    color: '#aab7c4',
                },
            },
            invalid: {
                color: '#9e2146',
            },
        },
    });

    cardElement.mount('#card-element');

    // Handle real-time validation errors from the card Element
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission
    $('#cardPaymentForm').submit(function(e) {
        e.preventDefault();

        // Validate form
        if (!$('#card_holder_name').val().trim()) {
            alert('Please enter the card holder name.');
            return;
        }

        if (!$('#address').val().trim()) {
            alert('Please enter your billing address.');
            return;
        }

        if (!$('#state').val().trim()) {
            alert('Please enter your state.');
            return;
        }

        if (!$('#terms_accepted').is(':checked')) {
            alert('Please accept the terms and conditions.');
            return;
        }

        // Disable submit button and show processing modal
        $('#submitBtn').prop('disabled', true);
        $('#processingModal').modal('show');

        // Create payment method
        stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: $('#card_holder_name').val(),
                address: {
                    line1: $('#address').val(),
                    state: $('#state').val(),
                },
            },
        }).then(function(result) {
            if (result.error) {
                // Show error to customer
                $('#processingModal').modal('hide');
                $('#submitBtn').prop('disabled', false);

                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = result.error.message;
            } else {
                // Submit payment method to server
                processPayment(result.paymentMethod.id);
            }
        });
    });

    function processPayment(paymentMethodId) {
        $.ajax({
            url: '{{ route("individual.program.payment.process.card") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                payment_method_id: paymentMethodId,
                card_holder_name: $('#card_holder_name').val(),
                address: $('#address').val(),
                state: $('#state').val(),
            },
            success: function(response) {
                $('#processingModal').modal('hide');

                if (response.success) {
                    // Show success message and redirect
                    alert(response.message || 'Payment completed successfully!');

                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        window.location.href = '{{ route("guardian.dashboard") }}';
                    }
                } else {
                    $('#submitBtn').prop('disabled', false);
                    alert(response.message || 'Payment failed. Please try again.');
                }
            },
            error: function(xhr) {
                $('#processingModal').modal('hide');
                $('#submitBtn').prop('disabled', false);

                let message = 'Payment processing failed. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join('\n');
                }

                alert(message);
            }
        });
    }

    // Handle browser back button
    window.addEventListener('beforeunload', function(e) {
        if ($('#processingModal').hasClass('show')) {
            e.preventDefault();
            e.returnValue = 'Payment is being processed. Are you sure you want to leave?';
        }
    });
});
</script>
@endsection
