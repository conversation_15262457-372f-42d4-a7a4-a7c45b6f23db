@extends('layouts.app')
@section('title', 'Tryout Programs')

<style>
    /* Custom Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single,
    .select2-container .select2-selection--multiple {
        min-height: 43px !important;
        border-radius: 12px !important;
        border: 1px solid #d9d9d9 !important;
        background-color: #f8f9fa !important;
        display: flex !important;
        align-items: center !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        right: 15px !important;
        width: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        border-color: #0b4499 transparent transparent transparent !important;
        border-style: solid !important;
        border-width: 6px 6px 0 6px !important;
        height: 0 !important;
        left: 50% !important;
        margin-left: -6px !important;
        margin-top: -3px !important;
        position: absolute !important;
        top: 50% !important;
        width: 0 !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #666 !important;
        line-height: 43px !important;
        padding-right: 30px !important;
        padding-left: 15px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #666 !important;
        opacity: 1 !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #0b4499 !important;
    color: white;
    border: none;
    border-radius: 5rem;
    padding: 0.25rem 0.75rem;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {

    background-color: #0b4499 !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0b4499;
}

/* Fix for Select2 inside Bootstrap modal */
.modal-open .select2-container--open .select2-dropdown {
    z-index: 1056;
    /* Higher than modal backdrop */
}

/* Modal Button Styling */
#create-team-button {
    margin: 0 auto;
    min-width: 200px;
}

.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 43px !important;
    user-select: none;
    -webkit-user-select: none
}

/* Modal Form Styling */
#team-create-modal .modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

#team-create-modal .modal-body {
    padding: 2rem;
}

#team-create-modal .form-label {
    margin-bottom: 0.875rem;
    color: #c1b05c;
    font-weight: 900;
    letter-spacing: 2px;
}

#team-create-modal .heading h2 {
    color: #062e69;
    font-weight: 900;
    letter-spacing: 2px;
}

#team-create-modal .invalid-feedback-admin {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive fixes */
@media (max-width: 767px) {
    #team-create-modal .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    #team-create-modal .modal-body {
        padding: 1.5rem;
    }

    #create-team-button {
        width: 100%;
    }
}

/* Spinning animation for loading states */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}


.modal .modal-content {
border-radius: 20px;
border: none;
box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal .modal-header {
border-bottom: 2px solid #d9d9d9;
padding: 1.5rem 2rem;
}

.modal .modal-body {
padding: 2rem;
}

.modal .modal-title {
color: #062e69;
font: 900 18px/1 'Montserrat', sans-serif;
letter-spacing: 0.5px;
}

.modal .form-label {
color: #194795;
font: 700 14px/1 'Montserrat', sans-serif;
letter-spacing: 1px;
text-transform: uppercase;
margin-bottom: 0.875rem;
}

.modal .form-control {
background-color: #f8f9fa;
border-radius: 12px;
border: 1px solid #d9d9d9;
padding: 12px 15px;
font: 500 16px/1 'Montserrat', sans-serif;
}

.modal .text-muted {
font: 400 12px/1 'Montserrat', sans-serif;
}

.btn-close {
opacity: 1;
font-size: 1.2rem;
}

.modal-footer {
border-top: 2px solid #d9d9d9;
padding: 1.5rem 2rem;
}

.cta {
background: #0b4499;
border: 1px solid #0b4499;
border-radius: 5rem;
color: #fff;
font: 900 14px/1 'Poppins', sans-serif;
letter-spacing: 0.34px;
padding: 0.75rem 1.5rem;
text-transform: uppercase;
transition: all 0.3s ease;
}

.cta:hover {
background-color: #fff;
color: #0b4499;
}

.cta:disabled {
background-color: #6c757d;
border-color: #6c757d;
opacity: 0.6;
cursor: not-allowed;
}

.spinner-border {
width: 1.5rem;
height: 1.5rem;
border-width: 0.2em;
}

.alert {
border-radius: 12px;
font: 500 14px/1.5 'Montserrat', sans-serif;
padding: 1rem;
margin-bottom: 1.5rem;
}

.modal-table-program {
max-height: 400px;
overflow-y: auto;
border-radius: 12px;
box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Spinning animation for loading states */
@keyframes spin {
0% { transform: rotate(0deg); }
100% { transform: rotate(360deg); }
}

.spin {
animation: spin 1s linear infinite;
display: inline-block;
}

/* Multiple Selection Styling */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    box-sizing: border-box !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 8px 15px !important;
    width: 100% !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 5px !important;
    min-height: 43px !important;
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: flex !important;
    overflow: visible !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    text-overflow: unset !important;
    white-space: normal !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #0b4499 !important;
    color: white !important;
    border: none !important;
    border-radius: 20px !important;
    padding: 4px 12px !important;
    margin: 2px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white !important;
    margin-right: 5px !important;
    border: none !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    background-color: transparent !important;
    color: #ffd !important;
}

.select2-container--default .select2-selection--multiple {
    position: relative !important;
}

.select2-container--default .select2-selection--multiple:after {
    content: '' !important;
    position: absolute !important;
    right: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    border-top: 5px solid #0b4499 !important;
    border-right: 5px solid transparent !important;
    border-left: 5px solid transparent !important;
    pointer-events: none !important;
}

.select2-container--default .select2-selection--multiple .select2-search__field {
    color: #666 !important;
    font-size: 14px !important;
    margin-top: 0 !important;
}

.select2-container--default .select2-selection--multiple .select2-search__field::placeholder {
    color: #666 !important;
    opacity: 0.8 !important;
}
</style>

@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Programs</h1>
    </section>

    @if (session('success'))
        <div id="successMessageForSession">
            <span id="successText">{{ session('success') }}</span>
        </div>
    @endif

    @if (session('error'))
        <div id="errorMessageForSession">
            <span id="errorText">{{ session('error') }}</span>
        </div>
    @endif



    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="{{ route('admin.program.allPrograms') }}">Back</a>
        {{-- <button id="add-the-team" class="cta-button-popup" style="position:absolute; right:2rem;">Create Team</button> --}}

        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>

    <section class="sec program-table">

        @livewire('admin.tryout-programs')

    </section>





{{-- Regular Program Invitation Modal --}}
<div class="modal fade" id="regularInviteModal" tabindex="-1" aria-labelledby="regularInviteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 800px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title navy-text" id="regularInviteModalLabel">
                    <i class="bi bi-person-plus me-2"></i>Invite Players to Program
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{-- Loading State --}}
                <div id="modalLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading available players...</p>
                </div>

                {{-- Content --}}
                <div id="modalContent">
                    <div class="mb-4">
                        <label for="playerSelect" class="form-label text-uppercase">Select Players</label>
                        <select class="form-control" id="playerSelect" multiple="multiple">
                        </select>
                        <small id="playerSelectHelp" class="text-muted d-block mt-2">
                            <i class="bi bi-info-circle me-1"></i>
                            Select one or more players to invite to this program
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d;" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Cancel
                </button>
                <button type="button" class="cta" id="sendInvitationBtn" onclick="sendInvitation()" disabled style="opacity: 0.6; cursor: not-allowed;">
                    <span id="sendBtnText">
                        <i class="bi bi-send me-1"></i>Send Invitations
                    </span>
                    <span id="sendBtnLoading" style="display: none;">
                        <i class="bi bi-arrow-clockwise spin me-1"></i>Sending...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

{{-- Team Invitation Modal --}}
<div class="modal fade" id="teamInviteModal" tabindex="-1" aria-labelledby="teamInviteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title navy-text" id="teamInviteModalLabel">
                    <i class="bi bi-people me-2"></i>Invite Player to Team
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{-- Loading State --}}
                <div id="teamModalLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading available teams...</p>
                </div>

                {{-- Content --}}
                <div id="teamModalContent">
                    <div class="mb-3">
                        <label for="teamSelect" class="form-label text-uppercase">Select Team</label>
                        <select class="form-control" id="teamSelect">
                            <option value="">Select a team...</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="assignedBalance" class="form-label text-uppercase">Assigned Balance</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="assignedBalance" value="0" min="0" step="0.01">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d;" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Cancel
                </button>
                <button type="button" class="cta" id="sendTeamInvitation" onclick="sendTeamInvitation()">
                    <i class="bi bi-send me-1"></i>Send Invitation
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Clean up Select2 when modal is closed
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('regularInviteModal').addEventListener('hidden.bs.modal', function () {
            // The logic for closing the modal and resetting variables is now handled by the JS file
        });
    });

    // Team invitation modal variables
    let currentTeamInvitePlayerId = null;
    let currentTeamInviteProgramId = null;
    let availableTeams = [];

    // Function to open team invite modal
    function openTeamInviteModal(playerId, programId) {
        console.log('Opening team invite modal for player:', playerId, 'program:', programId);
        currentTeamInvitePlayerId = playerId;
        currentTeamInviteProgramId = programId;

        const modal = new bootstrap.Modal(document.getElementById('teamInviteModal'));
        modal.show();

        loadAvailableTeams();
    }

    // Function to load available teams
    function loadAvailableTeams() {
        document.getElementById('teamModalLoading').style.display = 'block';
        document.getElementById('teamModalContent').style.display = 'none';

        fetch(`/admin/api/available-teams-for-program/${currentTeamInviteProgramId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    availableTeams = data.teams;
                    populateTeamSelect();
                } else {
                    console.error('Failed to load teams:', data.message);
                    availableTeams = [];
                    populateTeamSelect();
                }

                document.getElementById('teamModalLoading').style.display = 'none';
                document.getElementById('teamModalContent').style.display = 'block';
            })
            .catch(error => {
                console.error('Error loading teams:', error);
                availableTeams = [];
                populateTeamSelect();

                document.getElementById('teamModalLoading').style.display = 'none';
                document.getElementById('teamModalContent').style.display = 'block';
            });
    }

    // Function to populate team select dropdown
    function populateTeamSelect() {
        const select = document.getElementById('teamSelect');
        select.innerHTML = '<option value="">Choose a team...</option>';

        availableTeams.forEach(team => {
            const option = document.createElement('option');
            option.value = team.id;
            option.textContent = team.name;
            select.appendChild(option);
        });

        updateTeamSendButton();
    }

    // Function to update team send button state
    function updateTeamSendButton() {
        const teamSelect = document.getElementById('teamSelect');
        const balanceInput = document.getElementById('assignedBalance');
        const sendBtn = document.getElementById('sendTeamInvitation');

        if (teamSelect.value && balanceInput.value && parseFloat(balanceInput.value) >= 0) {
            sendBtn.disabled = false;
            sendBtn.style.opacity = '1';
            sendBtn.style.cursor = 'pointer';
        } else {
            sendBtn.disabled = true;
            sendBtn.style.opacity = '0.6';
            sendBtn.style.cursor = 'not-allowed';
        }
    }

    // Function to send team invitation
    function sendTeamInvitation() {
        const teamSelect = document.getElementById('teamSelect');
        const balanceInput = document.getElementById('assignedBalance');
        const selectedTeamId = teamSelect.value;
        const assignedBalance = balanceInput.value;

        if (!selectedTeamId || !currentTeamInvitePlayerId || !currentTeamInviteProgramId) {
            alert('Please select a team and enter balance amount');
            return;
        }

        if (!assignedBalance || parseFloat(assignedBalance) < 0) {
            alert('Please enter a valid balance amount');
            return;
        }

        // Show loading state
        document.getElementById('sendTeamBtnText').style.display = 'none';
        document.getElementById('sendTeamBtnLoading').style.display = 'inline';
        document.getElementById('sendTeamInvitationBtn').disabled = true;

        // Prepare form data
        const formData = new FormData();
        formData.append('player_id', currentTeamInvitePlayerId);
        formData.append('team_id', selectedTeamId);
        formData.append('program_id', currentTeamInviteProgramId);
        formData.append('assigned_balance', assignedBalance);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Send request
        fetch('/admin/api/invite-player-to-team', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showGlobalSuccess(data.message);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('teamInviteModal'));
                modal.hide();

                // Reload page to show updated data
                location.reload();
            } else {

                showGlobalError('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error sending team invitation:', error);
            showGlobalError('An error occurred while sending the invitation. Please try again.');
        })
        .finally(() => {
            // Reset loading state
            document.getElementById('sendTeamBtnText').style.display = 'inline';
            document.getElementById('sendTeamBtnLoading').style.display = 'none';
            document.getElementById('sendTeamInvitationBtn').disabled = false;
        });
    }

    // Add event listeners for team modal
    document.addEventListener('DOMContentLoaded', function() {
        // Team select change event
        document.getElementById('teamSelect').addEventListener('change', updateTeamSendButton);

        // Balance input change event
        document.getElementById('assignedBalance').addEventListener('input', updateTeamSendButton);

        // Team modal close event
        document.getElementById('teamInviteModal').addEventListener('hidden.bs.modal', function () {
            currentTeamInvitePlayerId = null;
            currentTeamInviteProgramId = null;
            availableTeams = [];

            // Reset form
            document.getElementById('teamSelect').innerHTML = '<option value="">Choose a team...</option>';
            document.getElementById('assignedBalance').value = '';
            updateTeamSendButton();
        });
    });
</script>

@endsection

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/admin.tryoutPrograms.js') }}"></script>
@endsection

@section('additional_styles')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endsection
