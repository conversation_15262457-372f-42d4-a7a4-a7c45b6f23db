 <div class="tables_heading table-program table-responsive">
     <table class="table custom-table user-management-table">
         <thead>
             <tr>
                 <th style="width:40px" class="py-4">&nbsp;</th>
                 <th style="width:350px" class="py-4">Program</th>
                 <th style="width:200px" class="py-4">Payments</th>
                 <th style="width:200px" class="py-4">Registrations</th>
                 {{-- <th style="width:200px" class="py-4">Invoices</th> --}}
                 <th style="width:200px" class="py-4"></th>
             </tr>
         </thead>
     </table>
 </div>

 @foreach ($programData as $program)
     <div class="tables_collapse_heading table-heading-grey">
         <table class="table table-hover user-management-table">
             <tr>
                 <td style="width:40px">
                     <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                             data-toggle="{{ $program['program_slug'] }}"></i>
                     </div>
                 </td>
                 <td style="width:350px" class="text-custom">
                     {{ $program['name'] }}
                 </td>
                 <td style="width:200px" class="text-custom">
                     ${{ number_format($program['payments'], 2) }}
                 </td>
                 <td style="width:200px" class="text-custom">
                     {{ $program['registrations'] }}
                 </td>
                 {{-- <td style="width:200px" class="text-custom">
                     @if(isset($program['payment_details']) && count($program['payment_details']) > 0)
                         <div class="payment-links">
                             @foreach($program['payment_details'] as $payment)
                                 @if($payment['payment_type'] === 'recurring' || $payment['payment_type'] === 'outstanding_to_recurring')

                                     @if(isset($payment['subscription_details']) && $payment['subscription_details'])
                                         <div class="subscription-details mb-2 p-2 border rounded bg-light">
                                             <div class="d-flex align-items-center mb-2">
                                                 <i class="bi bi-arrow-repeat text-primary me-2"></i>
                                                 <strong class="me-2">Subscription</strong>
                                                 <span class="badge bg-{{ $payment['subscription_details']['status'] === 'active' ? 'success' : ($payment['subscription_details']['status'] === 'canceled' ? 'danger' : 'warning') }}">
                                                     {{ ucfirst($payment['subscription_details']['status']) }}
                                                 </span>
                                                 @if($payment['subscription_details']['cancel_at_period_end'])
                                                     <span class="badge bg-warning ms-1">Cancelling</span>
                                                 @endif
                                                 @if($payment['subscription_details']['pause_collection'])
                                                     <span class="badge bg-secondary ms-1">Auto-Pay Disabled</span>
                                                 @endif
                                             </div>

                                             <div class="subscription-info small">
                                                 <div class="row g-1">
                                                     <div class="col-6">
                                                         <strong>Next Payment:</strong><br>
                                                         <span class="text-{{ $payment['subscription_details']['cancel_at_period_end'] ? 'muted' : 'success' }}">
                                                             {{ $payment['subscription_details']['next_payment_date'] ?? 'N/A' }}
                                                         </span>
                                                     </div>
                                                     <div class="col-6">
                                                         <strong>Created:</strong><br>
                                                         <span>{{ $payment['subscription_details']['created'] ?? 'N/A' }}</span>
                                                     </div>
                                                     <div class="col-12">
                                                         <strong>Period:</strong><br>
                                                         <span class="small">
                                                             {{ $payment['subscription_details']['current_period_start'] ?? 'N/A' }} -
                                                             {{ $payment['subscription_details']['current_period_end'] ?? 'N/A' }}
                                                         </span>
                                                     </div>
                                                     @if($payment['subscription_details']['canceled_at'])
                                                     <div class="col-12">
                                                         <strong>Cancelled:</strong><br>
                                                         <span class="text-danger small">{{ $payment['subscription_details']['canceled_at'] }}</span>
                                                     </div>
                                                     @endif
                                                     @if($payment['subscription_details']['trial_end'])
                                                     <div class="col-12">
                                                         <strong>Trial Ends:</strong><br>
                                                         <span class="small">{{ $payment['subscription_details']['trial_end'] }}</span>
                                                     </div>
                                                     @endif
                                                 </div>
                                             </div>

                                             <div class="mt-2">
                                                 @if($payment['invoice_id'])
                                                     @if($payment['subscription_details']['status'] === 'active')
                                                         @if(!$payment['subscription_details']['cancel_at_period_end'])
                                                             <button onclick="cancelSubscription({{ $payment['invoice_id'] }})"
                                                                     class="btn btn-xs btn-outline-warning me-1"
                                                                     title="Cancel at period end">
                                                                 <i class="bi bi-x-circle"></i> Cancel
                                                             </button>
                                                         @endif
                                                         @if(!$payment['subscription_details']['pause_collection'])
                                                             <button onclick="disableAutoPay({{ $payment['invoice_id'] }})"
                                                                     class="btn btn-xs btn-outline-secondary me-1"
                                                                     title="Disable auto-pay">
                                                                 <i class="bi bi-pause-circle"></i> Pause
                                                             </button>
                                                         @else
                                                             <button onclick="enableAutoPay({{ $payment['invoice_id'] }})"
                                                                     class="btn btn-xs btn-outline-success me-1"
                                                                     title="Enable auto-pay">
                                                                 <i class="bi bi-play-circle"></i> Resume
                                                             </button>
                                                         @endif
                                                     @endif
                                                 @endif
                                                 @if($payment['charge_id'])
                                                     <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                        class="btn btn-xs btn-outline-success"
                                                        target="_blank"
                                                        title="Refund Payment - ${{ number_format($payment['paid_amount'], 2) }}">
                                                         <i class="bi bi-credit-card"></i> Refund
                                                     </a>
                                                 @endif
                                             </div>
                                         </div>
                                     @else

                                         @if($payment['invoice_id'])
                                             <div class="alert alert-warning small p-2">
                                                 <i class="bi bi-exclamation-triangle"></i>
                                                 Subscription details unavailable. Invoice ID: {{ $payment['invoice_id'] }}
                                             </div>
                                         @endif
                                         @if($payment['charge_id'])
                                             <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                class="btn btn-sm btn-outline-success mb-1"
                                                target="_blank"
                                                title="Refund Payment - ${{ number_format($payment['paid_amount'], 2) }}">
                                                 <i class="bi bi-credit-card"></i> Refund #{{ $payment['charge_id'] }}
                                             </a>
                                         @endif
                                     @endif
                                 @else

                                     @if($payment['charge_id'])
                                         <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                            class="btn btn-sm btn-outline-success mb-1"
                                            target="_blank"
                                            title="Refund Payment - ${{ number_format($payment['paid_amount'], 2) }}">
                                             <i class="bi bi-credit-card"></i> Refund #{{ $payment['charge_id'] }}
                                         </a>
                                     @endif
                                 @endif
                                 @if(!$payment['invoice_id'] && !$payment['charge_id'])
                                     <span class="text-muted small">No receipt</span>
                                 @endif
                             @endforeach
                         </div>
                     @else
                         <span class="text-muted">No payments</span>
                     @endif
                 </td> --}}

                 <td style="width:200px">
                     <table class="user-management-actions-table">
                         <tr>
                             <td class="text-center pe-3" data-program-slug="{{ $program['program_slug'] }}">
                                 <i class="bi bi-filetype-xls export-xls-icon"
                                     style="color: #0B4499; font-size: 1.5rem;"></i>
                             </td>
                             <td class="text-center pe-3">
                                 <i class="bi bi-printer-fill print-icon"
                                     data-program-slug="{{ $program['program_slug'] }}"
                                     style="color: #0B4499; font-size: 1.5rem;"></i>
                             </td>
                             <td class="text-center pe-3">
                                 <a href="{{ route('admin.program.edit', $program['program_slug']) }}"
                                     class="action edit" id="edit-admin">
                                     <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                         height="20" />
                                 </a>
                             </td>
                             <td class="text-center">
                                 <form id="deleteProgram-{{ $program['program_slug'] }}"
                                     onsubmit="showConfirmation(event, 'deleteProgram-{{ $program['program_slug'] }}')"
                                     action="{{ route('admin.program.destroy', $program['program_slug']) }}"
                                     method="POST" style="display: inline;">
                                     @csrf
                                     @method('DELETE')
                                     <button type="submit"
                                         style="border: none; background: none; padding: 0; cursor: pointer;">
                                         <span class="action delete">
                                             <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                 width="20" height="20" />
                                         </span>
                                     </button>
                                 </form>
                             </td>
                         </tr>
                     </table>
                 </td>
             </tr>
         </table>
     </div>

     <div class="table_collapse_table table-program table-responsive table-grey">
         @if ($program['type'] == 'Team')
             <table class="table table-hover team-table d-none" width="100%"
                 id="program-table{{ $program['program_slug'] }}">
                 <tr>
                     <th width="40">
                         &nbsp;</th>
                     <th width="200">Team Name</th>
                     <th width="200">Coach</th>
                     <th width="250">Email</th>
                     <th width="250">Assistant Coach Email</th>
                     <th width="200">Player Count</th>
                     <th width="200">Team Balance</th>
                     {{-- <th width="200">Invoices</th> --}}
                     <th width="200">&nbsp;</th>
                 </tr>
                 @foreach ($program['teams'] as $team)
                     <tr>
                         <td>&nbsp;</td>
                         <td>{{ $team['team_name'] }}</td>
                         <td>{{ $team['coach_firstName'] }} {{ $team['coach_lastName'] }}</td>

                         <td>{{ $team['coach_email'] }}</td>
                         <td>{{ $team['assistantCoachEmail'] ?? 'N/A' }}</td>
                         <td class="pl-1">{{ $team['player_count'] }}</td>
                         <td>${{ number_format($team['team_balance'], 2) }}</td>
                         {{-- <td>
                             @if(isset($program['payment_details']) && count($program['payment_details']) > 0)
                                 <div class="payment-links">
                                     @foreach($program['payment_details'] as $payment)
                                         @if($payment['payment_type'] === 'recurring' || $payment['payment_type'] === 'outstanding_to_recurring')

                                             @if(isset($payment['subscription_details']) && $payment['subscription_details'])
                                                 <div class="subscription-details mb-2 p-2 border rounded bg-light">
                                                     <div class="d-flex align-items-center mb-1">
                                                         <i class="bi bi-arrow-repeat text-primary me-2"></i>
                                                         <strong class="me-2">Subscription</strong>
                                                         <span class="badge bg-{{ $payment['subscription_details']['status'] === 'active' ? 'success' : ($payment['subscription_details']['status'] === 'canceled' ? 'danger' : 'warning') }}">
                                                             {{ ucfirst($payment['subscription_details']['status']) }}
                                                         </span>
                                                     </div>
                                                     <div class="small">
                                                         <strong>Next:</strong> {{ $payment['subscription_details']['next_payment_date'] ?? 'N/A' }}<br>
                                                         <strong>Period:</strong> {{ $payment['subscription_details']['current_period_start'] }} - {{ $payment['subscription_details']['current_period_end'] }}
                                                     </div>
                                                     <div class="mt-1">
                                                         @if($payment['invoice_id'])
                                                             @if($payment['subscription_details']['status'] === 'active')
                                                                 @if(!$payment['subscription_details']['cancel_at_period_end'])
                                                                     <button onclick="cancelSubscription({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-warning me-1"
                                                                             title="Cancel at period end">
                                                                         <i class="bi bi-x-circle"></i> Cancel
                                                                     </button>
                                                                 @endif
                                                                 @if(!$payment['subscription_details']['pause_collection'])
                                                                     <button onclick="disableAutoPay({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-secondary me-1"
                                                                             title="Disable auto-pay">
                                                                         <i class="bi bi-pause-circle"></i> Pause
                                                                     </button>
                                                                 @else
                                                                     <button onclick="enableAutoPay({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-success me-1"
                                                                             title="Enable auto-pay">
                                                                         <i class="bi bi-play-circle"></i> Resume
                                                                     </button>
                                                                 @endif
                                                             @endif
                                                         @endif
                                                         @if($payment['charge_id'])
                                                             <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                                class="btn btn-xs btn-outline-success"
                                                                target="_blank" title="Refund Payment">
                                                                 <i class="bi bi-credit-card"></i> Refund
                                                             </a>
                                                         @endif
                                                     </div>
                                                 </div>
                                             @else

                                                 @if($payment['invoice_id'])
                                                     <div class="alert alert-warning small p-2">
                                                         <i class="bi bi-exclamation-triangle"></i>
                                                         Subscription details unavailable. Invoice ID: {{ $payment['invoice_id'] }}
                                                     </div>
                                                 @endif
                                             @endif
                                         @else

                                             @if($payment['charge_id'])
                                                 <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                    class="btn btn-sm btn-outline-success mb-1"
                                                    target="_blank"
                                                    title="Refund Payment - ${{ number_format($payment['paid_amount'], 2) }}">
                                                     <i class="bi bi-credit-card"></i> Refund #{{ $payment['charge_id'] }}
                                                 </a>
                                             @endif
                                         @endif
                                     @endforeach
                                 </div>
                             @else
                                 <span class="text-muted small">No payments</span>
                             @endif
                         </td> --}}
                         <td>
                             <table>
                                 <tr>
                                     <td class="pe-5">
                                         <a href="{{ route('admin.editTeams', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']]) }}"
                                             class="action edit">
                                             <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                 width="20" height="20" />
                                         </a>
                                     </td>
                                     <td>
                                         <form id="removeTeamFromProgram-{{ $team['team_id'] }}"
                                             onsubmit="showConfirmation(event, 'removeTeamFromProgram-{{ $team['team_id'] }}')"
                                             action="{{ route('admin.removeTeamFromProgram', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']]) }}"
                                             method="POST" style="display: inline;">
                                             @csrf
                                             @method('DELETE')
                                             <button type="submit" class="btn btn-link p-0 border-0"
                                                 style="background: none;">
                                                 <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                     width="20" height="20" />
                                             </button>
                                         </form>
                                     </td>

                                 </tr>
                             </table>
                         </td>
                         {{-- <td>
                             @if(isset($team['charges']) && count($team['charges']) > 0)
                                 <div class="charge-links">
                                     @foreach($team['charges'] as $charge)
                                         <a href="{{ route('admin.charges.show', $charge['charge_id']) }}"
                                            class="btn btn-sm btn-outline-success mb-1"
                                            target="_blank"
                                            title="View Charge - ${{ number_format($charge['amount'], 2) }}">
                                             <i class="bi bi-credit-card"></i> ${{ number_format($charge['amount'], 2) }}
                                         </a>
                                         @if($charge['receipt_url'])
                                             <a href="{{ $charge['receipt_url'] }}"
                                                class="btn btn-sm btn-outline-info mb-1"
                                                target="_blank"
                                                title="Download Receipt">
                                                 <i class="bi bi-download"></i> Receipt
                                             </a>
                                         @endif
                                     @endforeach
                                 </div>
                             @else
                                 <span class="text-muted small">No charges</span>
                             @endif
                         </td> --}}
                     </tr>
                 @endforeach
             </table>
             <button class="cta openModalButton d-none" id="add-team-btn{{ $program['program_slug'] }}"
                 data-program-id={{ $program['program_slug'] }}
                 style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                 ADD Team to program
             </button>
         @elseif ($program['type'] == 'Individual' || $program['type'] == 'AAU')
             <table class="table table-hover team-table d-none" width="100%"
                 id="program-table{{ $program['program_slug'] }}">
                 <tr>
                     <th width="40">&nbsp;</th>
                     <th width="200">Player Name</th>
                     <th width="200">Email</th>
                     <th width="200">Guardian Email</th>
                     <th width="200">Program Dates</th>
                     {{-- <th width="200">Invoices</th> --}}
                     <th width="200"></th>
                     <th width="200">&nbsp;</th>
                 </tr>
                 @foreach ($program['players'] as $individual)
                     <tr>
                         <td>&nbsp;</td>
                         <td>{{ $individual['player_name'] }}</td>
                         <td>{{ $individual['player_email'] }}</td>
                         <td>{{ $individual['guardian_email'] }}</td>
                         <td>{{ $individual['program_dates']['start_date'] }} -
                             {{ $individual['program_dates']['end_date'] }}</td>
                         {{-- <td>
                             @if(isset($program['payment_details']) && count($program['payment_details']) > 0)
                                 <div class="payment-links">
                                     @foreach($program['payment_details'] as $payment)
                                         @if($payment['payment_type'] === 'recurring' || $payment['payment_type'] === 'outstanding_to_recurring')

                                             @if(isset($payment['subscription_details']) && $payment['subscription_details'])
                                                 <div class="subscription-details mb-2 p-2 border rounded bg-light">
                                                     <div class="d-flex align-items-center mb-1">
                                                         <i class="bi bi-arrow-repeat text-primary me-2"></i>
                                                         <strong class="me-2">Subscription</strong>
                                                         <span class="badge bg-{{ $payment['subscription_details']['status'] === 'active' ? 'success' : ($payment['subscription_details']['status'] === 'canceled' ? 'danger' : 'warning') }}">
                                                             {{ ucfirst($payment['subscription_details']['status']) }}
                                                         </span>
                                                     </div>
                                                     <div class="small">
                                                         <strong>Next:</strong> {{ $payment['subscription_details']['next_payment_date'] ?? 'N/A' }}<br>
                                                         <strong>Period:</strong> {{ $payment['subscription_details']['current_period_start'] }} - {{ $payment['subscription_details']['current_period_end'] }}
                                                     </div>
                                                     <div class="mt-1">
                                                         @if($payment['invoice_id'])
                                                             @if($payment['subscription_details']['status'] === 'active')
                                                                 @if(!$payment['subscription_details']['cancel_at_period_end'])
                                                                     <button onclick="cancelSubscription({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-warning me-1"
                                                                             title="Cancel at period end">
                                                                         <i class="bi bi-x-circle"></i> Cancel
                                                                     </button>
                                                                 @endif
                                                                 @if(!$payment['subscription_details']['pause_collection'])
                                                                     <button onclick="disableAutoPay({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-secondary me-1"
                                                                             title="Disable auto-pay">
                                                                         <i class="bi bi-pause-circle"></i> Pause
                                                                     </button>
                                                                 @else
                                                                     <button onclick="enableAutoPay({{ $payment['invoice_id'] }})"
                                                                             class="btn btn-xs btn-outline-success me-1"
                                                                             title="Enable auto-pay">
                                                                         <i class="bi bi-play-circle"></i> Resume
                                                                     </button>
                                                                 @endif
                                                             @endif
                                                         @endif
                                                         @if($payment['charge_id'])
                                                             <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                                class="btn btn-xs btn-outline-success"
                                                                target="_blank" title="Refund Payment">
                                                                 <i class="bi bi-credit-card"></i> Refund
                                                             </a>
                                                         @endif
                                                     </div>
                                                 </div>
                                             @else

                                                 @if($payment['invoice_id'])
                                                     <div class="alert alert-warning small p-2">
                                                         <i class="bi bi-exclamation-triangle"></i>
                                                         Subscription details unavailable. Invoice ID: {{ $payment['invoice_id'] }}
                                                     </div>
                                                 @endif
                                             @endif
                                         @else

                                             @if($payment['charge_id'])
                                                 <a href="{{ route('admin.charges.show', $payment['charge_id']) }}"
                                                    class="btn btn-sm btn-outline-success mb-1"
                                                    target="_blank"
                                                    title="Refund Payment - ${{ number_format($payment['paid_amount'], 2) }}">
                                                     <i class="bi bi-credit-card"></i> Refund #{{ $payment['charge_id'] }}
                                                 </a>
                                             @endif
                                         @endif
                                     @endforeach
                                 </div>
                             @else
                                 <span class="text-muted small">No payments</span>
                             @endif
                         </td> --}}
                         {{-- <td>
                             @if(isset($individual['charges']) && count($individual['charges']) > 0)
                                 <div class="charge-links">
                                     @foreach($individual['charges'] as $charge)
                                         <a href="{{ route('admin.charges.show', $charge['charge_id']) }}"
                                            class="btn btn-sm btn-outline-success mb-1"
                                            target="_blank"
                                            title="View Charge - ${{ number_format($charge['amount'], 2) }}">
                                             <i class="bi bi-credit-card"></i> ${{ number_format($charge['amount'], 2) }}
                                         </a>
                                         @if($charge['receipt_url'])
                                             <a href="{{ $charge['receipt_url'] }}"
                                                class="btn btn-sm btn-outline-info mb-1"
                                                target="_blank"
                                                title="Download Receipt">
                                                 <i class="bi bi-download"></i> Receipt
                                             </a>
                                         @endif
                                     @endforeach
                                 </div>
                             @else
                                 <span class="text-muted small">No charges</span>
                             @endif
                         </td> --}}
                         <td></td>
                         <td>
                             <table>
                                 <tr>
                                     <td class="pe-5">
                                         <a href="#" class="action edit">
                                             <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                 width="20" height="20" />
                                         </a>
                                     </td>
                                     <td>
                                         <form id="removePlayerForm-{{ $individual['player_id'] }}"
                                             action="{{ route('admin.removePlayerFromProgram', ['program' => $program['program_slug'], 'player' => $individual['player_id']]) }}"
                                             method="POST"
                                             onsubmit="showConfirmation(event, 'removePlayerForm-{{ $individual['player_id'] }}')"
                                             style="display: inline;">
                                             @csrf
                                             @method('DELETE')
                                             <button type="submit" class="btn btn-link p-0 border-0 action delete"
                                                 style="background: none;">
                                                 <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                     width="20" height="20" />
                                             </button>
                                         </form>
                                     </td>
                                 </tr>
                             </table>
                         </td>
                     </tr>
                 @endforeach
             </table>
             <button class="cta playerModalButton d-none" id="add-player-btn{{ $program['program_slug'] }}"
                 data-program-id={{ $program['program_slug'] }}
                 style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                 ADD player to program
             </button>
         @endif
     </div>
 @endforeach


 <div id="all-payments-pagination" class="mt-4 d-flex justify-content-center pagination">
     {{ $programs->onEachSide(1)->links('pagination::bootstrap-5') }}
 </div>

 <script>
 function cancelSubscription(invoiceId) {
     if (confirm('Are you sure you want to cancel this subscription? It will be cancelled at the end of the current billing period.')) {
         fetch(`/admin/invoices/${invoiceId}/cancel-subscription`, {
             method: 'POST',
             headers: {
                 'Content-Type': 'application/json',
                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
             }
         })
         .then(response => response.json())
         .then(data => {
             if (data.success) {
                 alert('Subscription will be cancelled at the end of the current billing period.');
                 location.reload();
             } else {
                 alert('Error: ' + (data.message || 'Failed to cancel subscription'));
             }
         })
         .catch(error => {
             console.error('Error:', error);
             alert('An error occurred while cancelling the subscription.');
         });
     }
 }

 function disableAutoPay(invoiceId) {
     if (confirm('Are you sure you want to disable auto-pay for this subscription?')) {
         fetch(`/admin/invoices/${invoiceId}/disable-autopay`, {
             method: 'POST',
             headers: {
                 'Content-Type': 'application/json',
                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
             }
         })
         .then(response => response.json())
         .then(data => {
             if (data.success) {
                 alert('Auto-pay has been disabled for this subscription.');
                 location.reload();
             } else {
                 alert('Error: ' + (data.message || 'Failed to disable auto-pay'));
             }
         })
         .catch(error => {
             console.error('Error:', error);
             alert('An error occurred while disabling auto-pay.');
         });
     }
 }

 function enableAutoPay(invoiceId) {
     if (confirm('Are you sure you want to enable auto-pay for this subscription?')) {
         fetch(`/admin/invoices/${invoiceId}/enable-autopay`, {
             method: 'POST',
             headers: {
                 'Content-Type': 'application/json',
                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
             }
         })
         .then(response => response.json())
         .then(data => {
             if (data.success) {
                 alert('Auto-pay has been enabled for this subscription.');
                 location.reload();
             } else {
                 alert('Error: ' + (data.message || 'Failed to enable auto-pay'));
             }
         })
         .catch(error => {
             console.error('Error:', error);
             alert('An error occurred while enabling auto-pay.');
         });
     }
 }
 </script>
