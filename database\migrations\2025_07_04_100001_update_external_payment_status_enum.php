<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to alter the enum to include 'link_created'
        DB::statement("ALTER TABLE payment_transactions MODIFY COLUMN external_payment_status ENUM('not_applicable', 'link_created', 'link_sent', 'link_opened', 'payment_completed', 'link_expired') DEFAULT 'not_applicable'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'link_created' from the enum (only if no records use it)
        DB::statement("ALTER TABLE payment_transactions MODIFY COLUMN external_payment_status ENUM('not_applicable', 'link_sent', 'link_opened', 'payment_completed', 'link_expired') DEFAULT 'not_applicable'");
    }
};
