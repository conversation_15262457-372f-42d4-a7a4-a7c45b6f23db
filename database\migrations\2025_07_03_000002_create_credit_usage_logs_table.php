<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_usage_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_transaction_id')->constrained('payment_transactions')->onDelete('cascade');
            $table->foreignId('guardian_credit_id')->constrained('guardian_credits')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // Guardian who used credit
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            
            // Credit Usage Details
            $table->decimal('credit_amount_used', 10, 2); // Amount of this specific credit used
            $table->decimal('credit_balance_before', 10, 2); // Credit balance before usage
            $table->decimal('credit_balance_after', 10, 2); // Credit balance after usage
            
            // Context
            $table->json('player_ids'); // Which players this credit was used for
            $table->string('usage_context')->default('program_payment'); // Context of usage
            
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'program_id']);
            $table->index('payment_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_usage_logs');
    }
};
