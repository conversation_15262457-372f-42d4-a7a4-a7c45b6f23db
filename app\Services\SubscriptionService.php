<?php

namespace App\Services;

use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    public function updateNextPaymentDate(Subscription $subscription)
    {
        try {
            $currentPeriodEnd = $subscription->current_period_end;

            // If there's a trial period and we're in it
            if ($subscription->trial_end && Carbon::now()->lt($subscription->trial_end)) {
                $subscription->next_payment_date = $subscription->trial_end;
            }
            // If subscription is scheduled to cancel
            else if ($subscription->cancel_at) {
                $subscription->next_payment_date = min(
                    Carbon::parse($subscription->cancel_at),
                    Carbon::parse($subscription->current_period_end)
                );
            }
            // Regular subscription
            else if ($currentPeriodEnd) {
                $subscription->next_payment_date = Carbon::parse($currentPeriodEnd);
            }

            // If all payments are completed, no next payment
            if ($subscription->payments_completed >= $subscription->number_of_payments) {
                $subscription->next_payment_date = null;
            }

            $subscription->save();

            Log::info('Updated next payment date for subscription', [
                'subscription_id' => $subscription->id,
                'next_payment_date' => $subscription->next_payment_date,
                'current_period_end' => $subscription->current_period_end,
                'payments_completed' => $subscription->payments_completed,
                'total_payments' => $subscription->number_of_payments
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update next payment date', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
