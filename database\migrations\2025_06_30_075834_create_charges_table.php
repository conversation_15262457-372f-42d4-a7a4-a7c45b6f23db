<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('charges', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('stripe_charge_id')->unique();
            $table->string('stripe_payment_intent_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('usd');
            $table->string('status');
            $table->string('payment_type')->nullable();
            $table->json('program_ids')->nullable();
            $table->json('player_ids')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('charge_date')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->string('refund_status')->default('none'); // none, partial, full
            $table->text('description')->nullable();
            $table->string('receipt_url')->nullable();
            $table->string('receipt_number')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['stripe_charge_id']);
            $table->index(['stripe_payment_intent_id']);
            $table->index(['status']);
            $table->index(['payment_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('charges');
    }
};
