@extends('layouts.app')
@section('title', 'Add Program')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Add Program</h1>
    </section>
    @if (session('success'))
        <div id="successMessageForSession">
            <span id="successText">{{ session('success') }}</span>
        </div>
    @endif

    @if (session('error'))
        <div id="errorMessageForSession">
            <span id="errorText">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Frontend Validation Error Message -->
    <div id="frontendErrorMessage" style="display: none;">
        <span id="frontendErrorText"></span>
        <button type="button" id="closeErrorBtn">&times;</button>
    </div>

    @if (
        $errors->hasAny([
            'name',
            'type',
            'sub_program',
            'location',
            'sport',
            'gender',
            'age_restriction_from',
            'age_restriction_to',
            'registration_opening_date',
            'registration_closing_date',
            'start_date',
            'end_date',
            'start_time',
            'end_time',
            'frequency',
            'frequency_days',
            'number_of_registers',
            'enable_waitlist',
            'cost',
            'enable_early_bird_specials',
            'payment',
            'program_description',
        ]))
        <div class="alert alert-danger mx-auto text-center" style="max-width: 500px;" role="alert">
            Please Fill All The details Correctly.
        </div>
    @endif

    @if (
        $errors->hasAny([
            'early_bird_specials_date',
            'early_bird_from.*',
            'early_bird_to.*',
            'price_before.*',
            'price_on_or_after.*',
        ]))
        <div id="errorMessageForSession" role="alert">
            If Early Bird Specials have been checked, you must fill in all the details correctly.
        </div>
    @endif

    <div class="backButtonforAddPrograms">
        <a class="cta" href="{{ route('admin.program.allPrograms') }}">Back</a>
    </div>
    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5"><span
                    class="hero-bar d-inline-flex"></span></div>
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-9">
                    <form class="row" action="{{ route('admin.program.store') }}" method="POST">
                        @csrf
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="name">Program Name</label>
                            <input class="form-control @error('name') is-invalid @enderror" id="programName" type="text"
                                name="name" value="{{ old('name') }}" required />
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="type">Program Type</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('type') is-invalid @enderror" id="type"
                                    name="type" required>
                                    <option value="" {{ old('type') === '' ? 'selected' : '' }}>Select Program
                                        Type
                                    </option>
                                    <option value="individual" {{ old('type') == 'individual' ? 'selected' : '' }}>
                                        Individual</option>
                                    <option value="team" {{ old('type') == 'team' ? 'selected' : '' }}>Team</option>
                                    <option value="aau" {{ old('type') == 'aau' ? 'selected' : '' }}>AAU</option>
                                    <option value="tryout" {{ old('type') == 'tryout' ? 'selected' : '' }}>Tryout</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>



                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="sub_program">Sub Program Name</label>
                            <input class="form-control @error('sub_program') is-invalid @enderror" id="subProgramName"
                                name="sub_program" type="text" value="{{ old('sub_program') }}" required />
                            @error('sub_program')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="location">Location</label>
                            <input class="form-control @error('location') is-invalid @enderror" id="location"
                                name="location" type="text" value="{{ old('location') }}" />
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>




                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="sport">Sports</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('sport') is-invalid @enderror" id="sports"
                                    name="sport" required>
                                    <option value="" {{ old('sport') === '' ? 'selected' : '' }}>Select</option>
                                    <option value="basketball" {{ old('sport') === 'basketball' ? 'selected' : '' }}>
                                        Basketball</option>
                                    <option value="volleyball" {{ old('sport') === 'volleyball' ? 'selected' : '' }}>
                                        Volleyball</option>
                                    <option value="pickleball" {{ old('sport') === 'pickleball' ? 'selected' : '' }}>
                                        Pickleball</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('sport')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('gender') is-invalid @enderror" id="gender"
                                    name="gender" required>
                                    <option value="" {{ old('gender') == '' ? 'selected' : '' }}>Select</option>
                                    <option value="boys" {{ old('gender') == 'boys' ? 'selected' : '' }}>Boys
                                    </option>
                                    <option value="girls" {{ old('gender') == 'girls' ? 'selected' : '' }}>Girls
                                    </option>
                                    <option value="coed" {{ old('gender') == 'coed' ? 'selected' : '' }}>Co-ed
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('gender')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>




                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Age Restriction (optional)</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_from">Start
                                        Age</label>
                                    <input class="form-control @error('age_restriction_from') is-invalid @enderror"
                                        id="age_restriction_from" name="age_restriction_from" type="number"
                                        value="{{ old('age_restriction_from') }}" />
                                    @error('age_restriction_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_to">End
                                        Age</label>
                                    <input class="form-control @error('age_restriction_to') is-invalid @enderror"
                                        id="age_restriction_to" name="age_restriction_to" type="number"
                                        value="{{ old('age_restriction_to') }}" />
                                    @error('age_restriction_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>


                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="grade">Grade</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('grade') is-invalid @enderror" id="grade"
                                    name="grade">
                                    <option value="" {{ old('grade') == '' ? 'selected' : '' }}>Select</option>
                                    <option value="6th" {{ old('grade') == '6th' ? 'selected' : '' }}>6th
                                    </option>
                                    <option value="7th" {{ old('grade') == '7th' ? 'selected' : '' }}>7th
                                    </option>
                                    <option value="8th" {{ old('grade') == '8th' ? 'selected' : '' }}>8th
                                    </option>
                                    <option value="9th" {{ old('grade') == '9th' ? 'selected' : '' }}>9th
                                    </option>
                                    <option value="10th" {{ old('grade') == '10th' ? 'selected' : '' }}>10th
                                    </option>
                                    <option value="adult" {{ old('grade') == 'adult' ? 'selected' : '' }}>Adult
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('grade')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birth_date_cutoff">Birth Date Cutoff</label>
                            <input class="form-control @error('birth_date_cutoff') is-invalid @enderror"
                                id="birth_date_cutoff" name="birth_date_cutoff" type="date"
                                value="{{ old('birth_date_cutoff') }}" />
                            @error('birth_date_cutoff')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>


                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Registration</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_opening_date">Registration Start</label>
                                    <input class="form-control @error('registration_opening_date') is-invalid @enderror"
                                        id="registration_opening_date" name="registration_opening_date" type="date"
                                        value="{{ old('registration_opening_date') }}" />
                                    @error('registration_opening_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_closing_date">Registration End</label>
                                    <input class="form-control @error('registration_closing_date') is-invalid @enderror"
                                        id="registration_closing_date" name="registration_closing_date" type="date"
                                        value="{{ old('registration_closing_date') }}" />
                                    @error('registration_closing_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>



                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_date">Start Date</label>
                                    <input class="form-control @error('start_date') is-invalid @enderror" id="start_date"
                                        name="start_date" type="date" value="{{ old('start_date') }}" />
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_date">End Date</label>
                                    <input class="form-control @error('end_date') is-invalid @enderror" id="end_date"
                                        name="end_date" type="date" value="{{ old('end_date') }}" />
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>



                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_time">Start Time</label>
                                    <input class="form-control @error('start_time') is-invalid @enderror" id="start_time"
                                        name="start_time" type="time" value="{{ old('start_time') }}" />
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_time">End Time</label>
                                    <input class="form-control @error('end_time') is-invalid @enderror" id="end_time"
                                        name="end_time" type="time" value="{{ old('end_time') }}" />
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>


                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Frequency</div>

                            <div class="form-sub-label text-uppercase mb-3">Daily</div>
                            <div class="row">
                                @foreach (['mon', 'tue', 'wed', 'thur', 'fri', 'sat', 'sun'] as $day)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency_days') is-invalid @enderror"
                                                id="{{ $day }}" name="frequency_days[]" type="checkbox"
                                                value="{{ $day }}"
                                                {{ is_array(old('frequency_days')) && in_array($day, old('frequency_days')) ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $day }}">{{ strtoupper($day) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="form-sub-label text-uppercase mb-3">Weekly</div>
                            <div class="row">
                                @foreach (['every-week', 'every-other-week', 'once-per-month'] as $week)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency') is-invalid @enderror"
                                                id="{{ $week }}" name="frequency" type="radio"
                                                value="{{ $week }}"
                                                {{ old('frequency') === $week ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $week }}">{{ strtoupper(str_replace('-', ' ', $week)) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            @error('frequency')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                        </div>





                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Enrollment (optional)</div>
                            <div class="form-sub-label text-uppercase mb-3">Registration Limit</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-2">
                                    <label class="text-uppercase visually-hidden form-label"
                                        for="number_of_registers">Number of Registers</label>
                                    <input class="form-control @error('number_of_registers') is-invalid @enderror"
                                        id="number_of_registers" name="number_of_registers" type="number"
                                        value="{{ old('number_of_registers') }}" />
                                    @error('number_of_registers')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check">
                                        <input type="hidden" name="enable_waitlist" value="0">
                                        <input class="form-check-input @error('enable_waitlist') is-invalid @enderror"
                                            id="enable-waitlist" name="enable_waitlist" type="checkbox" value="1"
                                            {{ old('enable_waitlist') ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="enable-waitlist">Enable
                                            Waitlist</label>
                                    </div>
                                    @error('enable_waitlist')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Cost</div>
                            <div class="form-sub-label text-uppercase mb-3">&nbsp;</div>
                            <div class="row align-items-center">
                                <!-- Cost Input Field -->
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase visually-hidden form-label" for="cost">Cost</label>
                                    <input class="form-control @error('cost') is-invalid @enderror" style="width: 100%;"
                                        id="cost" name="cost" type="number" step="0.01"
                                        value="{{ old('cost') }}" />
                                    @error('cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>


                                <div class="mb-4 col-md-8">
                                    <div class="form-check">
                                        <input type="hidden" name="enable_early_bird_specials" value="0">
                                        <input class="form-check-input" id="enable_early_bird_specials" type="checkbox"
                                            value="1" name="enable_early_bird_specials"
                                            {{ old('enable_early_bird_specials') ? 'checked' : '' }} />
                                        <label class="form-check-label text-uppercase" for="enable_early_bird_specials">
                                            Enable Early Bird Specials
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="early-bird-pricing" class="col-md-12 mt-4 mb-4 to_set_mb3" style="display: none;">
                            <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                <div class="form-label text-uppercase">Early Bird Pricing</div>
                                <div class="row align-items-center">
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase form-sub-label" for="early_bird_from_1">From</label>
                                        <input class="form-control  @error('early_bird_from.0') is-invalid @enderror"
                                            id="early_bird_from_1" type="number" step="0.01"
                                            name="early_bird_from[]" value="{{ old('early_bird_from.0') }}" />
                                    </div>
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase form-sub-label" for="early_bird_to_1">to</label>
                                        <input class="form-control   @error('early_bird_to.0') is-invalid @enderror"
                                            id="early_bird_to_1" type="number" step="0.01" name="early_bird_to[]"
                                            value="{{ old('early_bird_to.0') }}" />

                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-sub-label" for="price_before_1">Price
                                            Before</label>
                                        <input class="form-control @error('price_before.0') is-invalid @enderror"
                                            id="price_before_1" type="number" step="0.01" name="price_before[]"
                                            value="{{ old('price_before.0') }}" />

                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-sub-label"
                                            for="early_bird_specials_date">Date</label>
                                        <input
                                            class="form-control @error('early_bird_specials_date') is-invalid @enderror"
                                            id="early_bird_specials_date" type="date" name="early_bird_specials_date"
                                            value="{{ old('early_bird_specials_date') }}" />

                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-sub-label" for="price_on_or_after_1">Price
                                            On or
                                            After</label>
                                        <input class="form-control  @error('price_on_or_after.0') is-invalid @enderror"
                                            id="price_on_or_after_1" type="number" step="0.01"
                                            name="price_on_or_after[]" value="{{ old('price_on_or_after.0') }}" />

                                    </div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_from_2">From</label>
                                        <input class="form-control  @error('early_bird_from.1') is-invalid @enderror"
                                            id="early_bird_from_2" type="number" step="0.01"
                                            name="early_bird_from[]" value="{{ old('early_bird_from.1') }}" />
                                    </div>

                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_to_2">to</label>
                                        <input class="form-control @error('early_bird_to.1') is-invalid @enderror"
                                            id="early_bird_to_2" type="number" step="0.01" name="early_bird_to[]"
                                            value="{{ old('early_bird_to.1') }}" />
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_before_2">Price
                                            Before</label>
                                        <input class="form-control  @error('price_before.1') is-invalid @enderror"
                                            id="price_before_2" type="number" step="0.01" name="price_before[]"
                                            value="{{ old('price_before.1') }}" />
                                    </div>
                                    <div class="col-md-3"></div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_on_or_after_2">
                                            Price On
                                            or After</label>
                                        <input class="form-control @error('price_on_or_after.1') is-invalid @enderror"
                                            id="price_on_or_after_2" type="number" step="0.01"
                                            @error('price_on_or_after.1') is-invalid @enderror name="price_on_or_after[]"
                                            value="{{ old('price_on_or_after.1') }}" />
                                    </div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_from_3">From</label>
                                        <input class="form-control  @error('early_bird_from.2') is-invalid @enderror"
                                            id="early_bird_from_3" type="number" step="0.01"
                                            name="early_bird_from[]" value="{{ old('early_bird_from.2') }}" />
                                    </div>
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_to_3">to</label>
                                        <input class="form-control @error('early_bird_to.2') is-invalid @enderror"
                                            id="early_bird_to_3" type="number" step="0.01" name="early_bird_to[]"
                                            value="{{ old('early_bird_to.2') }}" />
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_before_3">Price
                                            Before</label>
                                        <input class="form-control  @error('price_before.2') is-invalid @enderror"
                                            id="price_before_3" type="number" step="0.01" name="price_before[]"
                                            value="{{ old('price_before.2') }}" />
                                    </div>
                                    <div class="col-md-3"></div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_on_or_after_3">Price On
                                            or After</label>
                                        <input class="form-control @error('price_on_or_after.2') is-invalid @enderror"
                                            id="price_on_or_after_3" type="number" step="0.01"
                                            name="price_on_or_after[]" value="{{ old('price_on_or_after.2') }}" />
                                    </div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_from_4">From</label>
                                        <input class="form-control  @error('early_bird_from.3') is-invalid @enderror"
                                            id="early_bird_from_4" type="number" step="0.01"
                                            name="early_bird_from[]" value="{{ old('early_bird_from.3') }}" />
                                    </div>

                                    <div class="mb-4 col-md">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="early_bird_to_4">to</label>
                                        <input class="form-control @error('early_bird_to.3') is-invalid @enderror"
                                            id="early_bird_to_4" type="number" step="0.01" name="early_bird_to[]"
                                            value="{{ old('early_bird_to.3') }}" />
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_before_4">Price
                                            Before</label>
                                        <input class="form-control  @error('price_before.3') is-invalid @enderror"
                                            id="price_before_4" type="number" step="0.01" name="price_before[]"
                                            value="{{ old('price_before.3') }}" />
                                    </div>
                                    <div class="col-md-3"></div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase visually-hidden form-label"
                                            for="price_on_or_after_4">Price On
                                            or After</label>
                                        <input class="form-control @error('price_on_or_after.3') is-invalid @enderror"
                                            id="price_on_or_after_4" type="number" step="0.01"
                                            name="price_on_or_after[]" value="{{ old('price_on_or_after.3') }}" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Program Coupons -->
                        <div class="col-12 mt-4 mb-4">
                            <div class="form-label text-uppercase">Program Coupons</div>
                            <button type="button" class="btn btn-primary mt-2" id="add-coupon-btn">Add Coupon</button>
                            <div id="coupons-container" class="mt-3" style="display: none;">
                                <!-- Coupon entries will be added here dynamically -->
                            </div>
                        </div>

                        <!-- Program Description -->
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="program_description">Program Description
                                <span class="red-text">*</span></label>
                            <textarea class="form-control @error('program_description') is-invalid @enderror" id="program_description"
                                name="program_description" style="height: 140px">
                                    {{ old('program_description') }}
                                </textarea>

                            @error('program_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>


                        <!-- Payment -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Payment</div>
                            <div class="row">
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="FullPaymentOnly" type="radio"
                                            value="full" name="payment"
                                            {{ old('payment') === 'full' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="FullPaymentOnly">Full
                                            Payment only</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="recurringPayment" type="radio"
                                            value="recurring" name="payment"
                                            {{ old('payment') === 'recurring' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="RecurringPayment">Recurring
                                            Payment</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="SplitPayment" type="radio" value="split"
                                            name="payment" {{ old('payment') === 'split' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="SplitPayment">Split
                                            Payment</label>
                                    </div>
                                </div>
                            </div>
                            <!-- Display validation error -->
                            @error('payment')
                                <div class="text-danger">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4" id="minimumMonthlyPaymentDiv"
                            style="{{ old('payment') === 'recurring' ? 'display:block;' : 'display:none;' }}">
                            <label class="text-uppercase form-label" for="minimumMonthlyPayment">
                               Down Payment
                            </label>
                            <input class="form-control @error('down_payment') is-invalid @enderror"
                                id="down_payment" name="down_payment" type="number" step="0.01"
                                min="1" placeholder="Enter amount in dollars, Minimum $1"
                                value="{{ old('down_payment') }}" />
                            @error('down_payment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <div class="mt-3">
                                <label class="text-uppercase form-label" for="payment_months">
                                    Number of Months
                                </label>
                                <select class="form-control @error('payment_months') is-invalid @enderror" id="payment_months" name="payment_months">
                                    <option value="">Select number of months</option>
                                    @for ($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ old('payment_months') == $i ? 'selected' : '' }}>{{ $i }} {{ $i == 1 ? 'Month' : 'Months' }}</option>
                                    @endfor
                                </select>
                                @error('payment_months')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                        </div>

                        <div class="mb-4" id="statusDiv">
                            <label class="form-label text-uppercase" for="statusSelect">Status</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('status') is-invalid @enderror" id="statusSelect"
                                    name="status" required>
                                    <option value="" {{ old('status') == '' ? 'selected' : '' }}>Select</option>
                                    <option value="public" {{ old('status') == 'public' ? 'selected' : '' }}>Public
                                    </option>
                                    <option value="private" {{ old('status') == 'private' ? 'selected' : '' }}>Private
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <div class="col-md-6 d-flex justify-content-center mx-auto mt-5">
                            <button class="cta py-0" type="submit">Save Program <span
                                    class="d-none ms-3 cta-response"></span></button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
        <style>
            .coupon-entry {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                background-color: #f9f9f9;
            }
            .coupon-entry:hover {
                border-color: #007bff;
                background-color: #f0f8ff;
            }
            #add-coupon-btn {
                background-color: #0b4499 !important;
                border-color: #0b4499 !important;
                color: white !important;
                border-radius: 6px;
                font-weight: 600;
            }
            #add-coupon-btn:hover {
                background-color: #062e69 !important;
                border-color: #062e69 !important;
                color: white !important;
            }
            .remove-coupon {
                font-size: 11px;
                padding: 6px 4px;
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
                color: white !important;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .remove-coupon:hover {
                background-color: #c82333 !important;
                border-color: #bd2130 !important;
            }
            .is-invalid {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
            }
            .validation-error {
                color: #dc3545;
                font-size: 0.875em;
                margin-top: 0.25rem;
            }
            #frontendErrorMessage {
                position: fixed;
                top: -100px;
                right: 20px;
                z-index: 1000;
                padding: 15px 40px 15px 20px;
                color: #ffffff;
                font-size: 16px;
                border-radius: 8px;
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
                opacity: 0;
                transition: opacity 0.3s ease;
                background-color: #e74c3c;
                max-width: 400px;
                text-align: left;
            }
            #frontendErrorMessage.slide-in {
                top: 20px;
                opacity: 1;
                animation: slide-in 0.5s ease-out;
            }
            #closeErrorBtn {
                position: absolute;
                top: 5px;
                right: 10px;
                background: none;
                border: none;
                color: #ffffff;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            #closeErrorBtn:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
            }
            #frontendErrorMessage.slide-out {
                animation: slide-out 0.5s ease-in forwards;
            }
            @keyframes slide-in {
                from {
                    top: -100px;
                    opacity: 0;
                }
                to {
                    top: 20px;
                    opacity: 1;
                }
            }
            @keyframes slide-out {
                from {
                    top: 20px;
                    opacity: 1;
                }
                to {
                    top: -100px;
                    opacity: 0;
                }
            }
        </style>
        <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let buttonElement = document.querySelector('.tox-promotion-link');
                
                // Coupon Management
                let couponCount = 0;
                const addCouponBtn = document.getElementById('add-coupon-btn');
                const couponsContainer = document.getElementById('coupons-container');
                
                function createCouponEntry(index) {
                    return `
                        <div class="coupon-entry mb-4" data-index="${index}">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label text-uppercase">Coupon Code</label>
                                    <input type="text" name="coupons[${index}][code]" class="form-control coupon-code" placeholder="e.g., SUMMER2025" required>
                                    <div class="invalid-feedback">Please enter a unique coupon code.</div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Discount Type</label>
                                    <select name="coupons[${index}][discount_type]" class="form-select" required>
                                        <option value="percentage">Percentage Off</option>
                                        <option value="fixed">Fixed Amount Off</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Value</label>
                                    <input type="number" step="0.01" min="0.01" name="coupons[${index}][discount_value]" class="form-control" placeholder="20" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Usage Limit</label>
                                    <input type="number" min="1" name="coupons[${index}][usage_limit]" class="form-control" placeholder="Optional">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Valid Until</label>
                                    <input type="date" name="coupons[${index}][valid_until]" class="form-control">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label text-uppercase">&nbsp;</label>
                                    <button type="button" class="btn btn-danger remove-coupon form-control">Remove</button>
                                </div>
                                <div class="col-md-11">
                                    <label class="form-label text-uppercase">Description</label>
                                    <input type="text" name="coupons[${index}][description]" class="form-control" placeholder="Optional coupon description">
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                addCouponBtn.addEventListener('click', function() {
                    couponsContainer.style.display = 'block';
                    couponsContainer.insertAdjacentHTML('beforeend', createCouponEntry(couponCount));
                    couponCount++;
                });
                
                // Restore coupons from old input on validation errors
                @if(old('coupons'))
                    @foreach(old('coupons') as $index => $coupon)
                        couponsContainer.style.display = 'block';
                        couponsContainer.insertAdjacentHTML('beforeend', createCouponEntry({{ $index }}));
                        
                        // Fill in the values
                        setTimeout(() => {
                            const entry = document.querySelector(`[data-index="{{ $index }}"]`);
                            if (entry) {
                                entry.querySelector('input[name*="[code]"]').value = '{{ $coupon['code'] ?? '' }}';
                                entry.querySelector('select[name*="[discount_type]"]').value = '{{ $coupon['discount_type'] ?? 'percentage' }}';
                                entry.querySelector('input[name*="[discount_value]"]').value = '{{ $coupon['discount_value'] ?? '' }}';
                                entry.querySelector('input[name*="[usage_limit]"]').value = '{{ $coupon['usage_limit'] ?? '' }}';
                                entry.querySelector('input[name*="[valid_until]"]').value = '{{ $coupon['valid_until'] ?? '' }}';
                                entry.querySelector('input[name*="[description]"]').value = '{{ $coupon['description'] ?? '' }}';
                            }
                        }, 100);
                        
                        couponCount = Math.max(couponCount, {{ $index + 1 }});
                    @endforeach
                @endif

                // Handle remove coupon buttons
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-coupon')) {
                        const couponEntry = e.target.closest('.coupon-entry');
                        couponEntry.remove();
                        if (document.querySelectorAll('.coupon-entry').length === 0) {
                            couponsContainer.style.display = 'none';
                        }
                    }
                });
                
                // Remove invalid classes on input
                document.addEventListener('input', function(e) {
                    if (e.target.classList.contains('form-control')) {
                        e.target.classList.remove('is-invalid');
                    }
                });

                const earlyBirdCheckbox = document.getElementById('enable_early_bird_specials');
                const earlyBirdPricing = document.getElementById('early-bird-pricing');


                function toggleEarlyBirdPricing() {
                    if (earlyBirdCheckbox.checked) {
                        earlyBirdPricing.style.display = 'block';
                    } else {
                        earlyBirdPricing.style.display = 'none';
                    }
                }


                toggleEarlyBirdPricing();


                earlyBirdCheckbox.addEventListener('change', toggleEarlyBirdPricing);


                //recurring payments


                const recurringPaymentRadio = document.getElementById('recurringPayment');
                const oneTimePaymentRadio = document.getElementById('FullPaymentOnly');
                const splitPaymentRadio = document.getElementById('SplitPayment');
                const minimumMonthlyPaymentDiv = document.getElementById('minimumMonthlyPaymentDiv');

                recurringPaymentRadio.addEventListener('change', togglePaymentDiv);
                oneTimePaymentRadio.addEventListener('change', togglePaymentDiv);
                splitPaymentRadio.addEventListener('change', togglePaymentDiv);

                function togglePaymentDiv() {
                    if (recurringPaymentRadio.checked) {
                        minimumMonthlyPaymentDiv.style.display = 'block';
                    } else {
                        minimumMonthlyPaymentDiv.style.display = 'none';
                    }
                }

                // Session message functions
                function showSessionSuccessMessage() {
                    // Implementation for success messages if needed
                }
                
                function showSessionErrorMessage() {
                    // Implementation for error messages if needed
                }
                
                showSessionSuccessMessage();
                showSessionErrorMessage();
            });
        </script>
    </section>
    <script>
        tinymce.init({
            selector: 'textarea#program_description',
            width: 1000,
            height: 300,
            plugins: [
                'advlist', 'autolink', 'link', 'image', 'charmap', 'preview', 'anchor',
                'searchreplace', 'wordcount', 'code', 'fullscreen', 'insertdatetime',
                'table', 'print'
            ],
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen',
            menubar: false,
            content_style: 'body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }'
        });
    </script>


@endsection
