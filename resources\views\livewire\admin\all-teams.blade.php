<div>

    {{-- Filter/Search Form --}}
    <div class="form row table-filter justify-content-center mb-4">
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="filter">Filter By</label>
                    <div class="select-arrow position-relative">
                        <select class="form-control" id="filter" wire:model.live="filter">
                            <option value="">All</option>
                            <option value="Coach">Coach</option>
                            <option value="Assistant Coach">Assistant Coach</option>
                            <option value="Program">Program</option>
                        </select>
                        <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="search">Search</label>
                    <div class="flex items-center">
                        <input class="form-control" id="search" type="text"
                            wire:model.live.debounce.500ms="search" placeholder="Search teams..." />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .table-updating {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>

    <div class="program-table">
        <div class="table-program table-responsive" wire:loading.class="table-updating">
            <table class="table table-hover" width="100%" id="teams-table">
                <thead>
                    <tr>
                        <th class="py-4">Team Name</th>
                        <th class="py-4">Coach</th>
                        <th class="py-4">Assistant Coach</th>
                        <th class="py-4">Current Program</th>
                        <th class="py-4">Players</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($teams as $team)
                        <tr>
                            <td class="py-4" valign="middle">{{ $team->name }}</td>
                            <td class="py-4" valign="middle">
                                @if ($team->primary_coach)
                                    {{ $team->primary_coach->firstName }} {{ $team->primary_coach->lastName }}
                                @else
                                    N/A
                                @endif
                            </td>
                            <td class="py-4" valign="middle">
                                @if ($team->assistant_coach)
                                    {{ $team->assistant_coach->firstName }} {{ $team->assistant_coach->lastName }}
                                @else
                                    N/A
                                @endif
                            </td>
                            <td class="py-4" valign="middle">
                                @if ($team->current_program)
                                    {{ $team->current_program->name }}
                                @else
                                    No Active Program
                                @endif
                            </td>
                            <td class="py-4" valign="middle">
                                @if ($team->current_program)
                                    <button class="cta btn-sm" data-bs-toggle="modal"
                                        data-bs-target="#playersModal-{{ $team->id }}">
                                        View Players
                                    </button>
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center py-4">No teams found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{-- Pagination --}}
            <div class="mt-4 d-flex justify-content-center pagination">
                {{ $teams->onEachSide(1)->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    {{-- Players Modal --}}
    @foreach ($teams as $team)
        @if ($team->current_program)
            <div class="modal fade" id="playersModal-{{ $team->id }}" tabindex="-1"
                aria-labelledby="playersModalLabel-{{ $team->id }}" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="playersModalLabel-{{ $team->id }}">
                                Players for {{ $team->name }} ({{ $team->current_program->name }})
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="program-table">
                                <div class="table-program table-responsive">
                                    <table class="table table-hover" width="100%"
                                        id="players-table-{{ $team->id }}">
                                        <thead>
                                            <tr>
                                                <th class="py-4">Player Name</th>
                                                <th class="py-4">Email</th>
                                                <th class="py-4">Invitation Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($team->current_players as $invitation)
                                                <tr>
                                                    <td class="py-4" valign="middle">
                                                        {{ $invitation->player->firstName ?? '' }}
                                                        {{ $invitation->player->lastName ?? '' }}</td>
                                                    <td class="py-4" valign="middle">
                                                        {{ $invitation->player->email ?? '' }}</td>
                                                    <td class="py-4" valign="middle">
                                                        {{ $invitation->invitation_status ?? $invitation->status }}
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="3" class="text-center py-4">No players invited to
                                                        this team for the current program.</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endforeach
</div>
