<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->renameColumn('minimum_recurring_amount', 'down_payment');
            $table->integer('up_to_months')->nullable()->after('down_payment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            $table->renameColumn('down_payment', 'minimum_recurring_amount');
            $table->dropColumn('up_to_months');
        });
    }
};
