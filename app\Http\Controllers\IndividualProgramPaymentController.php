<?php

namespace App\Http\Controllers;

use App\Models\Program;
use App\Models\User;
use App\Models\PaymentTransaction;
use App\Services\PaymentTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\PaymentIntent;

class IndividualProgramPaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentTransactionService $paymentService)
    {
        $this->paymentService = $paymentService;
        Stripe::setApiKey(env('STRIPE_SECRET'));
    }

    /**
     * Process payment from guardian program payment page
     */
    public function processPayment(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'program_id' => 'required|exists:programs,id',
                'player_ids' => 'required|array',
                'player_ids.*' => 'exists:users,id',
                'payment_type' => 'required|in:full,split,recurring,credit,external',
                'amount' => 'required|numeric|',
                'external_email' => 'nullable|email|required_if:payment_type,external',
                'program_payment_type' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $program = Program::findOrFail($request->program_id);
            $playerIds = $request->player_ids;
            $paymentType = $request->payment_type;
            $amount = $request->amount;

            // Verify program is Individual or AAU
            if (!in_array($program->type, ['Individual', 'AAU'])) {
                return response()->json([
                    'success' => false,
                    'error' => 'This payment method is only for Individual and AAU programs.'
                ], 400);
            }

            // Handle different payment types
            switch ($paymentType) {
                case 'credit':
                    return $this->processCreditPayment($user, $program, $playerIds, $amount);

                case 'external':
                    return $this->processExternalPayment($user, $program, $playerIds, $amount, $request->external_email);

                case 'full':
                case 'split':
                case 'recurring':
                    return $this->initiateCardPayment($user, $program, $playerIds, $amount, $paymentType);

                default:
                    return response()->json([
                        'success' => false,
                        'error' => 'Invalid payment type'
                    ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Error processing payment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'An error occurred while processing the payment'
            ], 500);
        }
    }

    /**
     * Process credit payment
     */
    private function processCreditPayment($user, $program, $playerIds, $amount)
    {
        $availableCredit = $user->getTotalAvailableCredit();

        if ($availableCredit <= 0) {
            return response()->json([
                'success' => false,
                'error' => 'No available credit'
            ], 400);
        }

        $transaction = $this->paymentService->createPaymentTransaction(
            $user,
            $program,
            $playerIds,
            $amount,
            'credit'
        );

        if ($availableCredit >= $amount) {
            // Full credit payment
            $creditUsed = $user->useCredit($amount, $transaction->id, $program->id, $playerIds);

            $transaction->update([
                'credit_used' => $creditUsed,
                'paid_amount' => $creditUsed,
                'status' => 'completed',
                'payment_completed_at' => now()
            ]);

            // Create program registrations
            $this->paymentService->createProgramRegistrations($transaction);

            return response()->json([
                'success' => true,
                'fully_paid' => true,
                'credit_used' => $creditUsed,
                'remaining_credit' => $user->getTotalAvailableCredit()
            ]);
        } else {
            // Partial credit payment - redirect to card payment for remaining amount
            $creditUsed = $user->useCredit($availableCredit, $transaction->id, $program->id, $playerIds);
            $remainingAmount = $amount - $creditUsed;

            $transaction->update([
                'credit_used' => $creditUsed,
                'pending_amount' => $remainingAmount
            ]);

            // Store transaction data in session for card payment
            session([
                'payment_transaction_id' => $transaction->id,
                'remaining_amount' => $remainingAmount
            ]);

            return response()->json([
                'success' => true,
                'fully_paid' => false,
                'credit_used' => $creditUsed,
                'remaining_amount' => $remainingAmount,
                'redirect_url' => route('individual.program.payment.card.details')
            ]);
        }
    }

    /**
     * Process external payment
     */
    private function processExternalPayment($user, $program, $playerIds, $amount, $externalEmail)
    {
        $transaction = $this->paymentService->createPaymentTransaction(
            $user,
            $program,
            $playerIds,
            $amount,
            'external',
            $externalEmail
        );

        $externalLink = $this->paymentService->createExternalPaymentLink($transaction);

        return response()->json([
            'success' => true,
            'external_payment' => true,
            'external_email' => $externalEmail
        ]);
    }

    /**
     * Initiate card payment (full, split, recurring)
     */
    private function initiateCardPayment($user, $program, $playerIds, $amount, $paymentType)
    {
        $transaction = $this->paymentService->createPaymentTransaction(
            $user,
            $program,
            $playerIds,
            $amount,
            $paymentType
        );

        // Store transaction data in session
        session([
            'payment_transaction_id' => $transaction->id,
            'payment_amount' => $amount,
            'payment_type' => $paymentType
        ]);

        return response()->json([
            'success' => true,
            'redirect_url' => route('individual.program.payment.card.details')
        ]);
    }

    /**
     * Show payment options for Individual/AAU program
     */
    public function showPaymentOptions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'program_id' => 'required|exists:programs,id',
            'player_ids' => 'required|array',
            'player_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();
        $program = Program::findOrFail($request->program_id);
        $playerIds = $request->player_ids;

        // Verify program is Individual or AAU
        if (!$program->isIndividualOrAAU()) {
            return redirect()->back()->withErrors(['error' => 'This payment method is only for Individual and AAU programs.']);
        }

        // Calculate total amount
        $totalAmount = 0;
        $playerDetails = [];
        foreach ($playerIds as $playerId) {
            $player = User::findOrFail($playerId);
            $amount = $user->calculateAmountForProgram($program->id);
            $totalAmount += $amount;
            $playerDetails[] = [
                'id' => $player->id,
                'name' => $player->name,
                'amount' => $amount,
            ];
        }

        // Get available payment methods for this program
        $availablePaymentMethods = $program->getAvailablePaymentMethods();

        // Get user's available credit
        $availableCredit = $user->getTotalAvailableCredit();

        $paymentData = [
            'program' => $program,
            'player_details' => $playerDetails,
            'total_amount' => $totalAmount,
            'available_payment_methods' => $availablePaymentMethods,
            'available_credit' => $availableCredit,
            'minimum_recurring_amount' => $program->minimum_recurring_amount,
        ];

        return view('guardian.individual-program-payment', compact('paymentData'));
    }

    /**
     * Process payment selection and create transaction
     */
    public function processPaymentSelection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'program_id' => 'required|exists:programs,id',
            'player_ids' => 'required|array',
            'player_ids.*' => 'exists:users,id',
            'payment_type' => 'required|in:full,split,recurring',
            'payment_method' => 'required|in:card,credit,external_link,mixed',
            'credit_amount' => 'nullable|numeric|min:0',
            'external_email' => 'nullable|email',
            'recurring_amount' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $user = Auth::user();
            $program = Program::findOrFail($request->program_id);

            // Verify program supports the selected payment type
            if (!$program->supportsPaymentType($request->payment_type)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected payment type is not supported for this program.'
                ], 400);
            }

            // Create payment transaction
            $transaction = $this->paymentService->createPaymentTransaction([
                'user_id' => $user->id,
                'program_id' => $request->program_id,
                'player_ids' => $request->player_ids,
                'payment_method' => $request->payment_method,
                'payment_type' => $request->payment_type,
                'external_email' => $request->external_email,
                'metadata' => [
                    'recurring_amount' => $request->recurring_amount,
                    'user_agent' => $request->userAgent(),
                    'ip_address' => $request->ip(),
                ],
            ]);

            // Handle credit payment if requested
            if ($request->payment_method === 'credit' || ($request->payment_method === 'mixed' && $request->credit_amount > 0)) {
                $creditAmount = $request->credit_amount ?? $transaction->total_amount;
                $creditResult = $this->paymentService->processCreditPayment($transaction, $creditAmount);

                if (!$creditResult['success']) {
                    return response()->json([
                        'success' => false,
                        'message' => $creditResult['message']
                    ], 400);
                }

                // If fully paid with credit, return success
                if ($creditResult['fully_paid']) {
                    return response()->json([
                        'success' => true,
                        'fully_paid' => true,
                        'transaction_id' => $transaction->transaction_id,
                        'credit_used' => $creditResult['credit_used'],
                        'message' => 'Payment completed successfully using credit.',
                        'redirect_url' => route('guardian.dashboard'),
                    ]);
                }
            }

            // Handle external payment link
            if ($request->external_email) {
                $externalLink = $this->paymentService->createExternalPaymentLink($transaction, $request->external_email);
                $this->paymentService->sendExternalPaymentLink($externalLink);

                return response()->json([
                    'success' => true,
                    'external_payment_sent' => true,
                    'transaction_id' => $transaction->transaction_id,
                    'external_email' => $request->external_email,
                    'message' => 'Payment link has been sent to ' . $request->external_email,
                    'redirect_url' => route('guardian.dashboard'),
                ]);
            }

            // For card payments, redirect to card details
            session(['payment_transaction_id' => $transaction->id]);

            return response()->json([
                'success' => true,
                'requires_card_payment' => true,
                'transaction_id' => $transaction->transaction_id,
                'remaining_amount' => $transaction->getRemainingAmount(),
                'redirect_url' => route('guardian.individual-program-card-details'),
            ]);
        } catch (\Exception $e) {
            Log::error('Payment selection processing failed', [
                'user_id' => Auth::id(),
                'program_id' => $request->program_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your payment selection. Please try again.'
            ], 500);
        }
    }

    /**
     * Show card details form for remaining payment
     */
    public function showCardDetails()
    {
        $transactionId = session('payment_transaction_id');
        if (!$transactionId) {
            return redirect()->route('guardian.dashboard')->withErrors(['error' => 'No active payment session found.']);
        }

        $transaction = PaymentTransaction::with(['program', 'user'])->findOrFail($transactionId);

        // Verify transaction belongs to current user
        if ($transaction->user_id !== Auth::id()) {
            return redirect()->route('guardian.dashboard')->withErrors(['error' => 'Unauthorized access to payment session.']);
        }

        // Verify transaction is still pending
        if ($transaction->status !== 'pending') {
            return redirect()->route('guardian.dashboard')->with('success', 'Payment has already been completed.');
        }

        $paymentData = [
            'transaction' => $transaction,
            'remaining_amount' => $transaction->getRemainingAmount(),
            'player_names' => $transaction->getPlayerNames(),
            'program_name' => $transaction->program->name,
            'credit_used' => $transaction->credit_used,
        ];

        return view('guardian.individual-program-card-details', compact('paymentData'));
    }

    /**
     * Process card payment
     */
    public function processCardPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method_id' => 'required|string',
            'card_holder_name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'state' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $transactionId = session('payment_transaction_id');
        if (!$transactionId) {
            return response()->json(['success' => false, 'message' => 'No active payment session found.'], 400);
        }

        try {
            $transaction = PaymentTransaction::findOrFail($transactionId);

            // Verify transaction belongs to current user
            if ($transaction->user_id !== Auth::id()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized access.'], 403);
            }

            $remainingAmount = $transaction->getRemainingAmount();

            if ($remainingAmount <= 0) {
                return response()->json(['success' => false, 'message' => 'No payment required.'], 400);
            }

            // Create Stripe PaymentIntent
            $paymentIntent = PaymentIntent::create([
                'amount' => $remainingAmount * 100, // Convert to cents
                'currency' => 'usd',
                'payment_method' => $request->payment_method_id,
                'confirm' => true,
                'description' => 'Individual Program Payment - ' . $transaction->program->name,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never',
                ],
                'metadata' => [
                    'transaction_id' => $transaction->transaction_id,
                    'user_id' => $transaction->user_id,
                    'program_id' => $transaction->program_id,
                    'player_ids' => json_encode($transaction->player_ids),
                    'card_holder_name' => $request->card_holder_name,
                    'address' => $request->address,
                    'state' => $request->state,
                ],
            ]);

            if ($paymentIntent->status === 'succeeded') {
                // Complete the transaction
                $this->paymentService->completeTransaction($transaction, [
                    'stripe_payment_intent_id' => $paymentIntent->id,
                    'stripe_charge_id' => $paymentIntent->charges->data[0]->id ?? null,
                    'paid_amount' => $remainingAmount,
                ]);

                // Clear session
                session()->forget('payment_transaction_id');

                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully!',
                    'transaction_id' => $transaction->transaction_id,
                    'redirect_url' => route('payment.success'),
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment was not successful. Please try again.',
            ], 400);
        } catch (\Exception $e) {
            Log::error('Card payment processing failed', [
                'transaction_id' => $transactionId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed. Please try again.',
            ], 500);
        }
    }
}
