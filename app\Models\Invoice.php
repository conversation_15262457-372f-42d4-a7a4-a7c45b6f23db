<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stripe_invoice_id',
        'stripe_payment_intent_id',
        'stripe_charge_id',
        'stripe_subscription_id',
        'amount',
        'currency',
        'status',
        'payment_type',
        'program_ids',
        'player_ids',
        'metadata',
        'invoice_date',
        'due_date',
        'paid_at',
        'refunded_amount',
        'refund_status',
    ];

    protected $casts = [
        'program_ids' => 'array',
        'player_ids' => 'array',
        'metadata' => 'array',
        'invoice_date' => 'datetime',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function refunds(): MorphMany
    {
        return $this->morphMany(Refund::class, 'refundable');
    }

    public function getProgramsAttribute()
    {
        return Program::whereIn('id', $this->program_ids ?? [])->get();
    }

    public function getPlayersAttribute()
    {
        return User::whereIn('id', $this->player_ids ?? [])->get();
    }

    public function getRemainingAmountAttribute()
    {
        return $this->amount - $this->refunded_amount;
    }

    public function canBeRefunded()
    {
        return $this->status === 'paid' && $this->remaining_amount > 0;
    }

    public function getRefundableAmountAttribute()
    {
        return $this->remaining_amount;
    }
}
