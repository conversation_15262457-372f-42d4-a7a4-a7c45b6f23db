document.addEventListener("DOMContentLoaded", function () {
    // Guardian Search Logic
    const guardianSearchInput = document.getElementById("guardianSearchInput");
    const guardianSearchResults = document.getElementById(
        "guardianSearchResults"
    );

    if (guardianSearchInput && guardianSearchResults) {
        let debounceTimeout = null;
        guardianSearchInput.addEventListener("input", function () {
            const query = guardianSearchInput.value.trim();
            clearTimeout(debounceTimeout);
            if (!query) {
                guardianSearchResults.style.display = "none";
                guardianSearchResults.innerHTML = "";
                return;
            }
            debounceTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(
                        "/guardian/search?query=" + encodeURIComponent(query),
                        {
                            headers: {
                                "X-CSRF-TOKEN": csrfToken,
                                "X-Requested-With": "XMLHttpRequest",
                            },
                        }
                    );
                    const data = await response.json();
                    // Get current guardian id from hidden input
                    const currentGuardianId =
                        document.getElementById("guardianIdInput")?.value;
                    guardianSearchResults.style.display = "block";
                    if (
                        data.success &&
                        data.guardians &&
                        data.guardians.length > 0
                    ) {
                        guardianSearchResults.innerHTML = data.guardians
                            .filter((g) => g.id != currentGuardianId) // Exclude current user
                            .map((g) => {
                                const alreadyAdded =
                                    g.primary_parent_id == currentGuardianId ||
                                    g.parent_id == currentGuardianId;
                                return `
                                    <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center add-guardian-from-search" data-guardian-id="${
                                        g.id
                                    }" data-first-name="${
                                    g.firstName
                                }" data-last-name="${g.lastName}" data-email="${
                                    g.email
                                }" ${alreadyAdded ? "disabled" : ""}>
                                        <span><strong>${g.firstName} ${
                                    g.lastName
                                }</strong><br><small class="text-muted">${
                                    g.email
                                }</small></span>
                                        <span class="badge guardianAddButton" style="min-width: 60px;">${
                                            alreadyAdded ? "Added" : "Add"
                                        }</span>
                                    </button>
                                `;
                            })
                            .join("");
                    } else if (
                        data.success &&
                        (!data.guardians || data.guardians.length === 0)
                    ) {
                        guardianSearchResults.innerHTML =
                            '<div class="list-group-item text-center text-muted">No guardians found.</div>';
                    } else if (!data.success && data.message) {
                        guardianSearchResults.innerHTML = `<div class="list-group-item text-center text-danger">${data.message}</div>`;
                    } else {
                        guardianSearchResults.innerHTML =
                            '<div class="list-group-item text-center text-danger">Could not fetch guardians. Please try again.</div>';
                    }
                } catch (e) {
                    guardianSearchResults.style.display = "block";
                    guardianSearchResults.innerHTML =
                        '<div class="list-group-item text-center text-danger">Could not fetch guardians. Please try again.</div>';
                }
            }, 250);
        });

        // Hide results when clicking outside
        document.addEventListener("click", function (e) {
            if (
                !guardianSearchInput.contains(e.target) &&
                !guardianSearchResults.contains(e.target)
            ) {
                guardianSearchResults.style.display = "none";
            }
        });

        // Delegate click for dynamically added Add buttons
        guardianSearchResults.addEventListener("click", async function (e) {
            const addButton = e.target.classList.contains(
                "add-guardian-from-search"
            )
                ? e.target
                : e.target.closest(".add-guardian-from-search");

            if (addButton && !addButton.classList.contains("added")) {
                e.preventDefault();

                // Get the CSRF token once at the start
                const csrfToken = document
                    .querySelector('meta[name="csrf-token"]')
                    ?.getAttribute("content");
                if (!csrfToken) {
                    showToast(
                        "Security token not found. Please refresh the page.",
                        true
                    );
                    return;
                }

                // Show loader on button
                const badge = addButton.querySelector(".guardianAddButton");
                const originalText = badge.innerHTML;
                badge.innerHTML =
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
                badge.disabled = true;

                try {
                    // Get all needed data attributes
                    const guardianId =
                        addButton.getAttribute("data-guardian-id");
                    const primaryParentId =
                        document.getElementById("guardianIdInput")?.value;

                    // Make the API request to add the guardian
                    const response = await fetch(
                        "guardian/add/additional-guardian",
                        {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": csrfToken,
                                "X-Requested-With": "XMLHttpRequest",
                            },
                            body: JSON.stringify({
                                guardian_id: guardianId,
                                primary_parent_id: primaryParentId,
                            }),
                        }
                    );

                    const data = await response.json();

                    if (data.success) {
                        // Set the badge text to 'Added' and update button state
                        badge.innerHTML = "";
                        badge.textContent = "Added";
                        addButton.classList.add("added");
                        addButton.disabled = true;
                        showToast(
                            data.message || "Guardian added successfully!"
                        );
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        badge.innerHTML = originalText;
                        badge.disabled = false;
                        showToast(
                            data.message || "Failed to add guardian.",
                            true
                        );
                    }
                } catch (error) {
                    badge.innerHTML = originalText;
                    badge.disabled = false;
                    showToast("An error occurred. Please try again.", true);
                }
            }
        });

        // Simple toast function
        function showToast(message, isError) {
            let toast = document.createElement("div");
            toast.className = "position-fixed top-0 end-0 p-3";
            toast.style.zIndex = 9999;
            toast.innerHTML = `<div class="toast align-items-center text-white ${
                isError ? "bg-danger" : "bg-success"
            } border-0 show" role="alert" aria-live="assertive" aria-atomic="true"><div class="d-flex"><div class="toast-body">${message}</div><button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button></div></div>`;
            document.body.appendChild(toast);
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    }
    const switchRoleButtonElement =
        document.getElementById("switchRoleToCoach");

    if (switchRoleButtonElement) {
        const guardianSwitchId = switchRoleButtonElement.getAttribute(
            "data-guardian-switch-id"
        );

        switchRoleButtonElement.addEventListener("click", function (event) {
            event.preventDefault();

            const url = route("guardian.switchRoleToCoach", {
                guardian: guardianSwitchId,
            });
            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content");

            fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: JSON.stringify({
                    guardianId: guardianSwitchId,
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        window.location.href = data.redirectUrl;
                    } else {
                        alert(data.message || "Failed to switch role.");
                    }
                })
                .catch((error) => {
                    console.error("Error:", error);
                });
        });
    }

    function handlePlayerEditClick(event) {
        const editButton = event.target.closest(".edit[data-player-id]");
        if (editButton) {
            const playerId = editButton.getAttribute("data-player-id");
            const url = route("guardian.players.edit", { id: playerId });

            fetch(url)
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById("playerIdInput").value =
                            data.player.id;
                        document.getElementById("editFirstName").value =
                            data.player.firstName;
                        document.getElementById("editLastName").value =
                            data.player.lastName;
                        document.getElementById("editEmail").value =
                            data.player.email;
                        document.getElementById("editGender").value =
                            data.player.gender;
                        document.getElementById("editBirthdate").value =
                            data.player.birthDate;
                        document.getElementById("editGrade").value =
                            data.player.grade;
                        document.getElementById("editStreet").value =
                            data.player.street;
                        document.getElementById("editTown").value =
                            data.player.town;

                        document.getElementById("editState").value =
                            data.player.state;

                        const profilePhotoUrl = data.player.profilePhoto;
                        if (!data.player.profilePhoto) {
                        }
                        const imagePreview =
                            document.getElementById("editImagePreview");

                        if (profilePhotoUrl) {
                            imagePreview.src = profilePhotoUrl;
                            imagePreview.style.display = "block";
                            document.getElementById(
                                "editSelectedFileName"
                            ).textContent = profilePhotoUrl.split("/").pop();
                        } else {
                            imagePreview.src = "";
                            imagePreview.style.display = "none";
                            document.getElementById(
                                "editSelectedFileName"
                            ).textContent = "No file selected";
                        }

                        $("#playerEdit").modal("show");
                    } else {
                        console.error("Failed to load player data.");
                    }
                });
        }
    }

    // Attach event listener using event delegation for editing players
    document.addEventListener("click", handlePlayerEditClick);

    // JS for handling the image upload and preview
    document
        .getElementById("uploadIcon")
        .addEventListener("click", function () {
            document.getElementById("profilePhoto").click();
        });

    document
        .getElementById("profilePhoto")
        .addEventListener("change", function () {
            const fileInput = document.getElementById("profilePhoto");
            const fileNameDisplay = document.getElementById("selectedFileName");
            const imagePreview = document.getElementById("imagePreview");
            const file = fileInput.files[0];

            if (file) {
                fileNameDisplay.textContent = file.name;

                const reader = new FileReader();
                reader.onload = function (e) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = "block";
                };
                reader.readAsDataURL(file);
            } else {
                fileNameDisplay.textContent = "No file selected";
                imagePreview.style.display = "none";
            }
        });

    // Ajax request-for handling the adding a player form

    // Add handler to open the 'Add Additional Guardian' modal
    const addGuardianBtn = document.getElementById(
        "additional-guardian-add-button"
    );
    if (addGuardianBtn) {
        addGuardianBtn.addEventListener("click", function () {
            var modal = new bootstrap.Modal(
                document.getElementById("guardianAdd")
            );
            modal.show();
        });
    }

    const buttonElementForOpeningPlayerModal =
        document.getElementById("addNewPlayer");
    const playerAddModal = new bootstrap.Modal(
        document.getElementById("playerAdd")
    );

    buttonElementForOpeningPlayerModal.addEventListener("click", function () {
        clearModalData();
        playerAddModal.show();
    });

    function clearModalData() {
        const form = document.getElementById("submitPlayerData");

        form.reset();

        const errorFields = document.querySelectorAll(".invalid-feedback");
        errorFields.forEach(function (errorField) {
            errorField.classList.add("d-none");
            errorField.innerText = "";
        });

        const invalidInputs = document.querySelectorAll(".is-invalid");
        invalidInputs.forEach(function (invalidInput) {
            invalidInput.classList.remove("is-invalid");
        });

        document.getElementById("successMessage").classList.add("d-none");
        document.getElementById("errorMessage").classList.add("d-none");

        document.getElementById("imagePreview").style.display = "none";
        document.getElementById("selectedFileName").innerText =
            "No file selected";

        document.getElementById("loaderForAddPlayerImage").style.display =
            "none";
    }

    document
        .getElementById("addPlayerButton")
        .addEventListener("click", async function (event) {
            event.preventDefault();

            const form = document.getElementById("submitPlayerData");
            const formData = new FormData(form);

            const successMessage = document.getElementById("successMessage");
            const errorMessage = document.getElementById("errorMessage");
            form.querySelectorAll(".is-invalid").forEach((el) =>
                el.classList.remove("is-invalid")
            );
            form.querySelectorAll(".invalid-feedback").forEach(
                (el) => (el.textContent = "")
            );

            showLoaderForAddPlayer();

            try {
                let url = route("guardian.player.add");
                const response = await fetch(url, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: formData,
                });

                const data = await response.json();

                if (data.success) {
                    form.reset();
                    hideLoaderForAddPlayer();

                    const imagePreview =
                        document.getElementById("imagePreview");
                    imagePreview.src = "";
                    imagePreview.style.display = "none";
                    const fileNameDisplay =
                        document.getElementById("selectedFileName");
                    fileNameDisplay.textContent = "No file selected";

                    successMessage.textContent = data.message;
                    successMessage.classList.remove("d-none");
                    errorMessage.classList.add("d-none");
                    form.reset();
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    if (data.errors) {
                        hideLoaderForAddPlayer();
                        for (const [key, value] of Object.entries(
                            data.errors
                        )) {
                            const inputElement = document.querySelector(
                                `[name="${key}"]`
                            );
                            const errorElement = document.querySelector(
                                `#${key}Error`
                            );
                            if (inputElement) {
                                inputElement.classList.add("is-invalid");
                            }
                            if (errorElement) {
                                errorElement.textContent = value[0];
                                errorElement.classList.remove("d-none");
                                errorElement.style.display = "block";
                            }
                        }
                    } else {
                        errorMessage.textContent = data.message;
                        errorMessage.classList.remove("d-none");
                        hideLoaderForAddPlayer();
                    }
                }
            } catch (error) {
                hideLoaderForAddPlayer();
                errorMessage.textContent =
                    "An error occurred. Please try again.";
                errorMessage.classList.remove("d-none");
                successMessage.classList.add("d-none");
            }
        });

    function showLoaderForAddPlayer() {
        document.getElementById("addPlayerButtonText").style.display = "none";
        document.getElementById("loaderForAddPlayerImage").style.display =
            "inline-block";
        const button = document.getElementById("addPlayerButton");
        button.classList.add("buttonLoader");
    }

    function hideLoaderForAddPlayer() {
        document.getElementById("addPlayerButtonText").style.display = "inline";
        document.getElementById("loaderForAddPlayerImage").style.display =
            "none";
        const button = document.getElementById("addPlayerButton");
        button.classList.remove("buttonLoader");
    }

    // JS for handling the add guardian form
    document
        .getElementById("addGuardianButton")
        .addEventListener("click", async function (event) {
            event.preventDefault();
            const submitButton = document.getElementById("addGuardianButton");

            const form = document.getElementById("submitGuardianData");

            const inputs = form.querySelectorAll("input");
            const loader = document.getElementById("submitLoader");

            let isValid = true;

            inputs.forEach((input) => {
                if (input.value.trim() === "") {
                    isValid = false;
                    input.classList.add("is-invalid");
                } else {
                    input.classList.remove("is-invalid");
                }
            });

            if (!isValid) {
                const errorMessage = document.getElementById(
                    "guardianErrorMessage"
                );

                errorMessage.classList.remove("d-none");
                errorMessage.textContent = "Please Fill All the Details";

                return;
            }

            const formData = new FormData(form);

            const successMessage = document.getElementById(
                "guardianSuccessMessage"
            );
            const errorMessage = document.getElementById(
                "guardianErrorMessage"
            );

            form.querySelectorAll(".is-invalid").forEach((el) =>
                el.classList.remove("is-invalid")
            );
            form.querySelectorAll(".invalid-feedback").forEach(
                (el) => (el.textContent = "")
            );

            try {
                submitButton.disabled = "true";
                showLoader();

                let url = route("guardian.sendInvitationToAdditionalGuardian");
                const response = await fetch(url, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: formData,
                });

                const data = await response.json();

                if (data.success) {
                    successMessage.textContent = data.message;
                    successMessage.classList.remove("d-none");
                    errorMessage.classList.add("d-none");
                    hideLoader();
                    submitButton.disabled = "false";

                    form.reset();

                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    hideLoader();
                    submitButton.disabled = false;
                    if (data.errors) {
                        for (const [key, value] of Object.entries(
                            data.errors
                        )) {
                            const inputElement = document.querySelector(
                                `[name="${key}"]`
                            );
                            const errorElement =
                                inputElement.parentElement.querySelector(
                                    ".invalid-feedback"
                                );

                            if (inputElement) {
                                inputElement.classList.add("is-invalid");
                            }
                            if (errorElement) {
                                errorElement.textContent = value[0];
                            }
                        }
                    } else {
                        errorMessage.textContent = data.message;
                        errorMessage.classList.remove("d-none");
                    }
                }
            } catch (error) {
                submitButton.disabled = false;
                hideLoader();
                errorMessage.textContent =
                    "An error occurred. Please try again.";
                errorMessage.classList.remove("d-none");
                successMessage.classList.add("d-none");
            }
        });

    function showLoader() {
        document.getElementById("inviteGuardianButtonText").style.display =
            "none";
        document.getElementById("loaderForInviteGuardianImage").style.display =
            "inline-block";
        const button = document.getElementById("addGuardianButton");
        button.classList.add("buttonLoader");
    }

    function hideLoader() {
        document.getElementById("inviteGuardianButtonText").style.display =
            "inline";
        document.getElementById("loaderForInviteGuardianImage").style.display =
            "none";
        const button = document.getElementById("addGuardianButton");
        button.classList.remove("buttonLoader");
    }

    // JS for handling the edit guardian request
    document
        .querySelectorAll(".player-meta .edit[data-guardian-id]")
        .forEach((item) => {
            item.addEventListener("click", function () {
                const guardianId = this.getAttribute("data-guardian-id");
                const editIcon = document.getElementById(
                    `editIcon-${guardianId}`
                );

                const loaderUrl = this.getAttribute("data-loader-guardian-url");

                const originalIconSrc = editIcon.src;
                editIcon.src = loaderUrl;

                let url = route("guardian.guardian.edit", { id: guardianId });

                fetch(url)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            editIcon.src = originalIconSrc;

                            document.getElementById("guardianIdInput").value =
                                data.guardian.id;
                            document.getElementById(
                                "editGuardianFirstName"
                            ).value = data.guardian.firstName;
                            document.getElementById(
                                "editGuardianLastName"
                            ).value = data.guardian.lastName;
                            document.getElementById("editGuardianEmail").value =
                                data.guardian.email;
                            document.getElementById(
                                "editGuardianMobile"
                            ).value = data.guardian.mobile_number;

                            $("#guardianEdit").modal("show");
                        } else {
                            console.error("Failed to load guardian data.");
                            editIcon.src = originalIconSrc;
                        }
                    })
                    .catch((error) => {
                        console.error("Error fetching guardian data:", error);
                        editIcon.src = originalIconSrc;
                    });
            });
        });

    document
        .getElementById("editGuardianButton")
        .addEventListener("click", function (event) {
            event.preventDefault();
            const formData = new FormData(
                document.getElementById("editGuardianData")
            );

            const guardianId = document.getElementById("guardianIdInput").value;
            formData.append("guardian_id", guardianId);

            let url = route("guardian.guardian.update");

            fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById(
                            "editGuardianSuccessMessage"
                        ).textContent = "Guardian updated successfully!";
                        document
                            .getElementById("editGuardianSuccessMessage")
                            .classList.remove("d-none");

                        setTimeout(() => {
                            document
                                .getElementById("editGuardianSuccessMessage")
                                .classList.add("d-none");
                            $("#guardianEdit").modal("hide");
                            window.location.reload();
                        }, 2000);
                    } else {
                        document.getElementById(
                            "editGuardianErrorMessage"
                        ).textContent = data.message;

                        document
                            .getElementById("editGuardianErrorMessage")
                            .classList.remove("d-none");

                        setTimeout(() => {
                            document
                                .getElementById("editGuardianErrorMessage")
                                .classList.add("d-none");
                        }, 3000);
                    }
                });
        });

    $("#guardianAdd").on("hidden.bs.modal", function () {
        document.getElementById("submitGuardianData").reset();

        document
            .getElementById("guardianSuccessMessage")
            .classList.add("d-none");
        document.getElementById("guardianErrorMessage").classList.add("d-none");

        document
            .querySelectorAll("#submitGuardianData .is-invalid")
            .forEach((el) => el.classList.remove("is-invalid"));

        document
            .querySelectorAll("#submitGuardianData .invalid-feedback")
            .forEach((el) => (el.textContent = ""));
    });

    const loadMoreGuardiansButton = document.getElementById(
        "load-more-guardians"
    );
    let isFirstRequest = true;
    let lastGuardianId = getLastGuardianId();
    let fetchedLastGuardianId = null;

    if (loadMoreGuardiansButton) {
        loadMoreGuardiansButton.addEventListener("click", function () {
            let guardiansUrl = route("guardian.guardians.loadMore");
            let requestUrl;

            if (isFirstRequest) {
                requestUrl =
                    guardiansUrl +
                    (lastGuardianId ? `?lastGuardianId=${lastGuardianId}` : "");
                isFirstRequest = false;
            } else {
                requestUrl =
                    guardiansUrl +
                    (fetchedLastGuardianId
                        ? `?lastGuardianId=${fetchedLastGuardianId}`
                        : "");
            }

            loadMoreGuardians(requestUrl);
        });
    }

    function getLastGuardianId() {
        const lastGuardianElement = document.getElementById("lastGuardianId");
        return lastGuardianElement
            ? lastGuardianElement.getAttribute("data-additionalguardian-id")
            : null;
    }

    function loadMoreGuardians(requestUrl) {
        fetch(requestUrl)
            .then((response) => response.json())
            .then((data) => {
                let guardiansContainer = document.getElementById(
                    "guardians-pagination"
                );
                let pagination = document.getElementById(
                    "guardians-pagination"
                );

                data.additionalGuardians.forEach((guardian) => {
                    let guardianDiv = document.createElement("div");
                    guardianDiv.classList.add(
                        "player-meta__box",
                        "d-flex",
                        "flex-column",
                        "text-center",
                        "align-items-center",
                        "justify-content-center"
                    );
                    guardianDiv.innerHTML = `
                        <p>
                            ${guardian.firstName} ${guardian.lastName}<br />
                            ${guardian.email}
                        </p>
                        <div class="edit d-flex" data-additionalguardian-id="${guardian.id}" id="fetchedLastGuardianId">
                        </div>
                    `;

                    guardiansContainer.insertAdjacentElement(
                        "beforebegin",
                        guardianDiv
                    );

                    fetchedLastGuardianId = guardian.id;
                });

                if (data.additionalGuardiansNextPage) {
                    pagination.innerHTML = `
                        <button class="cta" id="load-more-guardians" data-url="${data.additionalGuardiansNextPage}">Show More Guardians</button>
                    `;
                    document
                        .getElementById("load-more-guardians")
                        .addEventListener("click", function () {
                            loadMoreGuardians(
                                this.getAttribute("data-url") +
                                    `&lastGuardianId=${fetchedLastGuardianId}`
                            );
                        });
                } else {
                    pagination.innerHTML = "";
                }
            })
            .catch((error) => showError(error));
    }

    // Attach event listener using event delegation for editing player

    const loadMorePlayersButton = document.getElementById("load-more-players");
    let loadedPlayerIds = getLoadedPlayerIds();

    if (loadMorePlayersButton) {
        loadMorePlayersButton.addEventListener("click", function () {
            let playersUrl = route("guardian.players.loadMore");
            let requestUrl =
                playersUrl + `?loadedPlayerIds=${loadedPlayerIds.join(",")}`;

            loadMorePlayers(requestUrl);
        });
    }

    function getLoadedPlayerIds() {
        const playerElements = document.querySelectorAll("[data-player-id]");
        return Array.from(playerElements).map((el) =>
            el.getAttribute("data-player-id")
        );
    }

    function loadMorePlayers(requestUrl) {
        const csrfToken = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        fetch(requestUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": csrfToken,
            },
            body: JSON.stringify({
                loadedPlayerIds: loadedPlayerIds.join(","),
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                let playersContainer =
                    document.getElementById("players-pagination");
                let pagination = document.getElementById("players-pagination");
                data.players.forEach((player) => {
                    let playerDiv = document.createElement("div");
                    playerDiv.classList.add(
                        "player-meta__box",
                        "d-flex",
                        "flex-column",
                        "text-center",
                        "align-items-center",
                        "justify-content-center"
                    );
                    playerDiv.innerHTML = `
                <p>
                    <a href="guardian/player/profile/${
                        player.id
                    }" class="player-profile">
                    ${player.firstName} ${player.lastName}
                    </a><br />
                     ${player.email ? player.email : "N/A"}
                </p>
                <div class="edit d-flex" data-player-id="${player.id}">
                    <img src="images/edit-icon.svg" alt="" width="20" height="20" id="editIcon-${
                        player.id
                    }" data-loader-player-url="images/ripple-loading.svg"/>
                </div>
            `;
                    playersContainer.insertAdjacentElement(
                        "beforebegin",
                        playerDiv
                    );
                });
                if (data.playersNextPage) {
                    pagination.innerHTML = `
                <button class="cta" id="load-more-players" data-url="${data.playersNextPage}">Show More Players</button>
            `;
                    document
                        .getElementById("load-more-players")
                        .addEventListener("click", function () {
                            loadMorePlayers(this.getAttribute("data-url"));
                        });
                } else if (data.playersNextPage == null) {
                    pagination.innerHTML = "";
                }
            })
            .catch((error) => showError(error));
    }

    // Function to handle player editing
    function handlePlayerEditClick(event) {
        const editButton = event.target.closest(".edit[data-player-id]");
        if (editButton) {
            const playerId = editButton.getAttribute("data-player-id");

            let editIcon = document.getElementById(`editIcon-${playerId}`);
            let loaderUrl = editIcon.getAttribute("data-loader-player-url");

            const originalIconSrc = editIcon.src;

            editIcon.src = loaderUrl;
            clearValidationErrors();
            const url = route("guardian.players.edit", { id: playerId });

            fetch(url)
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        editIcon.src = originalIconSrc;
                        document.getElementById("playerIdInput").value =
                            data.player.id;
                        document.getElementById("editFirstName").value =
                            data.player.firstName;
                        document.getElementById("editLastName").value =
                            data.player.lastName;
                        document.getElementById("editEmail").value =
                            data.player.email;
                        document.getElementById("editGender").value =
                            data.player.gender;
                        document.getElementById("editBirthdate").value =
                            data.player.birthDate;
                        document.getElementById("editGrade").value =
                            data.player.grade;
                        document.getElementById("editStreet").value =
                            data.player.street;
                        document.getElementById("editTown").value =
                            data.player.town;

                        document.getElementById("editState").value =
                            data.player.state;
                        const profilePhotoUrl = data.player.profilePhoto;
                        const imagePreview =
                            document.getElementById("editImagePreview");

                        if (profilePhotoUrl) {
                            imagePreview.src = profilePhotoUrl;
                            imagePreview.style.display = "block";
                            document.getElementById(
                                "editSelectedFileName"
                            ).textContent = profilePhotoUrl.split("/").pop();
                        } else {
                            imagePreview.src = "";
                            imagePreview.style.display = "none";
                            document.getElementById(
                                "editSelectedFileName"
                            ).textContent = "No file selected";
                        }

                        $("#playerEdit").modal("show");
                    } else {
                        console.error("Failed to load player data.");
                        editIcon.src = originalIconSrc;
                    }
                });
        }
    }

    function clearValidationErrors() {
        const form = document.getElementById("editPlayerData");
        form.querySelectorAll(".is-invalid").forEach((el) =>
            el.classList.remove("is-invalid")
        );
        form.querySelectorAll(".invalid-feedback").forEach(
            (el) => (el.textContent = "")
        );
        document.getElementById("editSuccessMessage").classList.add("d-none");
        document.getElementById("editErrorMessage").classList.add("d-none");
    }

    const playerPagination = document.getElementById("players-pagination");

    if (playerPagination) {
        playerPagination.addEventListener("click", handlePlayerEditClick);
    }

    // Handling profile photo upload for the edit player form
    document
        .getElementById("editUploadIcon")
        .addEventListener("click", function () {
            document.getElementById("editProfilePhoto").click();
        });

    document
        .getElementById("editProfilePhoto")
        .addEventListener("change", function () {
            const fileInput = document.getElementById("editProfilePhoto");
            const fileNameDisplay = document.getElementById(
                "editSelectedFileName"
            );
            const imagePreview = document.getElementById("editImagePreview");
            const file = fileInput.files[0];

            if (file) {
                fileNameDisplay.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function (e) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = "block";
                };
                reader.readAsDataURL(file);
            } else {
                fileNameDisplay.textContent = "No file selected";
                imagePreview.style.display = "none";
            }
        });

    document
        .getElementById("editPlayerButton")
        .addEventListener("click", function (event) {
            event.preventDefault();
            const form = document.getElementById("editPlayerData");
            const formData = new FormData(form);
            if (!validateForm()) {
                return;
            }

            const url = route("guardian.players.update");

            fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById(
                            "editSuccessMessage"
                        ).textContent = data.message;
                        document
                            .getElementById("editSuccessMessage")
                            .classList.remove("d-none");

                        setTimeout(() => {
                            document
                                .getElementById("editSuccessMessage")
                                .classList.add("d-none");
                            $("#playerEdit").modal("hide");
                            window.location.reload();
                        }, 2000);
                    } else {
                        handleValidationErrors(data.errors);
                        document.getElementById(
                            "editErrorMessage"
                        ).textContent =
                            data.message ||
                            "An error occurred while updating the player.";
                        document
                            .getElementById("editErrorMessage")
                            .classList.remove("d-none");

                        setTimeout(() => {
                            document
                                .getElementById("editErrorMessage")
                                .classList.add("d-none");
                        }, 3000);
                    }
                })
                .catch((error) => {
                    console.error("Error:", error);
                    document.getElementById("editErrorMessage").textContent =
                        "An unexpected error occurred.";
                    document
                        .getElementById("editErrorMessage")
                        .classList.remove("d-none");

                    setTimeout(() => {
                        document
                            .getElementById("editErrorMessage")
                            .classList.add("d-none");
                    }, 3000);
                });
        });

    function validateForm() {
        resetValidationErrors();

        let isValid = true;
        const fields = [
            {
                id: "editFirstName",
                errorId: "editFirstNameError",
                message: " The First name Field is required.",
            },
            {
                id: "editLastName",
                errorId: "editLastNameError",
                message: "The Last name Field is required.",
            },
            {
                id: "editBirthdate",
                errorId: "editBirthDateError",
                message: "The Birthdate Field is required.",
            },
            {
                id: "editGrade",
                errorId: "editGradeError",
                message: "The Grade Field is required.",
            },
            {
                id: "editStreet",
                errorId: "editStreetError",
                message: "The Street Field is required.",
            },

            {
                id: "editTown",
                errorId: "editTownError",
                message: "The Town Field is required.",
            },

            {
                id: "editState",
                errorId: "editStateError",
                message: "The State Field is required.",
            },
        ];

        fields.forEach(({ id, errorId, message }) => {
            const inputElement = document.getElementById(id);
            const errorElement = document.getElementById(errorId);

            if (!inputElement.value.trim()) {
                inputElement.classList.add("is-invalid");
                errorElement.textContent = message;
                errorElement.classList.remove("d-none");
                isValid = false;
            }
        });

        return isValid;
    }

    function handleValidationErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const errorElement = document.getElementById(
                `edit${capitalizeFirstLetter(field)}Error`
            );
            const inputElement = document.getElementById(
                `edit${capitalizeFirstLetter(field)}`
            );

            if (errorElement && inputElement) {
                inputElement.classList.add("is-invalid");
                errorElement.textContent = messages.join(", ");
                errorElement.classList.remove("d-none");
            }
        }
    }

    function resetValidationErrors() {
        document.querySelectorAll(".is-invalid").forEach((element) => {
            element.classList.remove("is-invalid");
        });
        document.querySelectorAll(".invalid-feedback").forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });
    }

    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    //handling the additional guardian popup

    function showError(error) {
        let errorDiv = document.querySelector(".bg-red-500");
        if (errorDiv) {
            errorDiv.innerHTML = `<ul><li>${error.message}</li></ul>`;
        }
    }

    let successAlert = document.getElementById("successAlert");

    if (successAlert) {
        setTimeout(() => {
            successAlert.style.display = "none";
        }, 2000);
    }

    const joinNowButtons = document.querySelectorAll(".join-now-btn");

    joinNowButtons.forEach((button) => {
        button.addEventListener("click", (event) => {
            const programSlug = button.getAttribute("data-program-slug");
            const playerId = button.getAttribute("data-player-id");
            if (programSlug) {
                let registrationUrl = route("program.show", {
                    program: programSlug,
                });

                // Add player ID as query parameter if it exists
                if (playerId) {
                    registrationUrl += `?invited_player=${playerId}`;
                }

                window.location.href = registrationUrl;
            } else {
                console.error("Program slug not found for this button.");
            }
        });
    });

    // Handle reject invitation buttons for post-tryout programs
    document.addEventListener("click", function (event) {
        if (event.target.classList.contains("reject-invitation-btn")) {
            const button = event.target;
            const invitationId = button.getAttribute("data-invitation-id");
            const playerName = button.getAttribute("data-player-name");
            const teamName = button.getAttribute("data-team-name");

            // Show confirmation dialog
            if (
                confirm(
                    `Are you sure you want to reject the invitation for ${playerName} to join ${teamName}?`
                )
            ) {
                // Disable button and show loading state
                button.disabled = true;
                button.textContent = "Rejecting...";

                // Make AJAX request to reject invitation
                fetch(route("guardian.rejectInvitation"), {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": csrfToken,
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: JSON.stringify({
                        invitation_id: invitationId,
                    }),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            // Remove the invitation row from the table
                            const row = button.closest("tr");
                            row.remove();

                            // Show success message
                            alert(
                                data.message ||
                                    "Invitation rejected successfully."
                            );

                            // Check if there are no more invitations
                            const tableBody = document.querySelector(
                                ".table-program tbody"
                            );
                            const remainingRows =
                                tableBody.querySelectorAll("tr").length;
                            if (remainingRows <= 1) {
                                // Only header row remains
                                tableBody.innerHTML = `
                                <tr>
                                    <th class="py-0"></th>
                                    <th class="py-0"></th>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                                             style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499;">
                                            No Invitations
                                        </div>
                                    </td>
                                </tr>
                            `;
                            }
                        } else {
                            // Re-enable button and show error
                            button.disabled = false;
                            button.textContent = "Reject";
                            alert(
                                data.message ||
                                    "Failed to reject invitation. Please try again."
                            );
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        // Re-enable button and show error
                        button.disabled = false;
                        button.textContent = "Reject";
                        alert("An error occurred. Please try again.");
                    });
            }
        }
    });
});
