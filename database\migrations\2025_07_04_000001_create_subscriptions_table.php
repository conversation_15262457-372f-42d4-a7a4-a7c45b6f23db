<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();

            // Core subscription details
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('stripe_subscription_id')->unique()->index();
            $table->string('stripe_customer_id')->index();
            $table->string('stripe_product_id')->nullable();
            $table->string('stripe_price_id')->nullable();

            // Subscription status and lifecycle
            $table->enum('status', [
                'incomplete',
                'incomplete_expired',
                'trialing',
                'active',
                'past_due',
                'canceled',
                'unpaid',
                'paused'
            ])->default('incomplete');
            $table->enum('payment_type', ['recurring', 'outstanding_to_recurring'])->default('recurring');

            // Financial details
            $table->decimal('amount_per_payment', 10, 2); // Amount charged per billing cycle
            $table->decimal('total_amount_due', 10, 2); // Total amount to be collected
            $table->decimal('amount_paid', 10, 2)->default(0); // Total amount paid so far
            $table->decimal('initial_amount_paid', 10, 2)->default(0); // First payment amount
            $table->string('currency', 3)->default('usd');

            // Billing cycle information
            $table->string('interval', 20)->default('month'); // month, year, etc.
            $table->integer('interval_count')->default(1); // Every X months/years
            $table->integer('number_of_payments'); // Total number of payments planned
            $table->integer('payments_completed')->default(0); // Number of payments completed

            // Important dates
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->timestamp('current_period_start')->nullable();
            $table->timestamp('current_period_end')->nullable();
            $table->timestamp('next_payment_date')->nullable();
            $table->timestamp('trial_start')->nullable();
            $table->timestamp('trial_end')->nullable();
            $table->timestamp('canceled_at')->nullable();
            $table->timestamp('ended_at')->nullable();

            // Cancellation details
            $table->boolean('cancel_at_period_end')->default(false);
            $table->timestamp('cancel_at')->nullable();
            $table->string('cancellation_reason')->nullable();

            // Program and player information
            $table->json('program_ids')->nullable(); // Programs this subscription covers
            $table->json('player_ids')->nullable(); // Players this subscription covers
            $table->json('pending_amounts_by_program')->nullable(); // Outstanding amounts by program

            // Customer information
            $table->string('customer_email');
            $table->string('customer_name')->nullable();
            $table->string('billing_address')->nullable();
            $table->string('billing_state')->nullable();
            $table->string('billing_city')->nullable();

            // Subscription management
            $table->boolean('pause_collection')->default(false);
            $table->json('pause_collection_behavior')->nullable();
            $table->string('default_payment_method')->nullable();
            $table->decimal('application_fee_percent', 5, 2)->nullable();

            // Metadata and additional information
            $table->json('metadata')->nullable(); // Store additional Stripe metadata
            $table->text('description')->nullable();
            $table->json('discount_details')->nullable(); // Any applied discounts

            // Webhook and sync information
            $table->timestamp('last_synced_at')->nullable();
            $table->json('sync_errors')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['status', 'next_payment_date']);
            $table->index(['start_date', 'end_date']);
            $table->index('current_period_end');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
