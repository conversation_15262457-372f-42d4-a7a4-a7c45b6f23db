<?php

namespace App\Services;

use App\Models\PaymentTransaction;
use App\Models\ExternalPaymentLink;
use App\Models\CreditUsageLog;
use App\Models\Program;
use App\Models\User;
use App\Models\ProgramRegistration;
use App\Models\PlayerProgram;
use App\Mail\ExternalPaymentLinkMail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class PaymentTransactionService
{
    /**
     * Create a new payment transaction for Individual/AAU programs
     */
    public function createPaymentTransaction(array $data): PaymentTransaction
    {
        return DB::transaction(function () use ($data) {
            $user = User::findOrFail($data['user_id']);
            $program = Program::findOrFail($data['program_id']);
            $playerIds = $data['player_ids'];

            // Calculate total amount
            $totalAmount = 0;
            foreach ($playerIds as $playerId) {
                $totalAmount += $user->calculateAmountForProgram($program->id);
            }

            // Create payment transaction
            $transaction = PaymentTransaction::create([
                'user_id' => $user->id,
                'program_id' => $program->id,
                'player_ids' => $playerIds,
                'total_amount' => $totalAmount,
                'payment_method' => $data['payment_method'] ?? 'card',
                'payment_type' => $data['payment_type'] ?? 'full',
                'status' => 'pending',
                'external_email' => $data['external_email'] ?? null,
                'payment_metadata' => $data['metadata'] ?? [],
            ]);

            // Create program registrations (unpaid initially)
            foreach ($playerIds as $playerId) {
                $amount = $user->calculateAmountForProgram($program->id);

                ProgramRegistration::create([
                    'user_id' => $user->id,
                    'program_id' => $program->id,
                    'player_id' => $playerId,
                    'amount' => $amount,
                    'is_paid' => false,
                ]);

                // Create player-program relationship
                PlayerProgram::firstOrCreate([
                    'player_id' => $playerId,
                    'program_id' => $program->id,
                ]);
            }

            return $transaction;
        });
    }

    /**
     * Process credit payment for a transaction
     */
    public function processCreditPayment(PaymentTransaction $transaction, float $creditAmount): array
    {
        $user = $transaction->user;
        $availableCredit = $user->getTotalAvailableCredit();

        if ($availableCredit <= 0) {
            return [
                'success' => false,
                'message' => 'No available credit',
                'credit_used' => 0,
                'remaining_amount' => $transaction->total_amount,
            ];
        }

        $creditToUse = min($creditAmount, $availableCredit, $transaction->total_amount);

        return DB::transaction(function () use ($transaction, $user, $creditToUse) {
            // Use credit with detailed logging
            $creditUsed = $user->useCredit(
                $creditToUse,
                $transaction->id,
                $transaction->program_id,
                $transaction->player_ids
            );

            // Update transaction
            $transaction->update([
                'credit_used' => $creditUsed,
                'pending_amount' => $transaction->total_amount - $creditUsed,
                'payment_method' => $creditUsed >= $transaction->total_amount ? 'credit' : 'mixed',
            ]);

            // If fully paid with credit, complete the transaction
            if ($creditUsed >= $transaction->total_amount) {
                $this->completeTransaction($transaction);

                return [
                    'success' => true,
                    'fully_paid' => true,
                    'credit_used' => $creditUsed,
                    'remaining_amount' => 0,
                    'message' => 'Payment completed using credit',
                ];
            }

            return [
                'success' => true,
                'fully_paid' => false,
                'credit_used' => $creditUsed,
                'remaining_amount' => $transaction->total_amount - $creditUsed,
                'message' => 'Credit applied, remaining amount needs to be paid',
            ];
        });
    }

    /**
     * Create external payment link
     */
    public function createExternalPaymentLink(PaymentTransaction $transaction, string $externalEmail): ExternalPaymentLink
    {
        $playerNames = $transaction->getPlayerNames();

        $externalLink = ExternalPaymentLink::create([
            'payment_transaction_id' => $transaction->id,
            'requesting_user_id' => $transaction->user_id,
            'program_id' => $transaction->program_id,
            'external_email' => $externalEmail,
            'amount_to_pay' => $transaction->getRemainingAmount(),
            'player_ids' => $transaction->player_ids,
            'player_names' => $playerNames,
            'expires_at' => Carbon::now()->addDays(7),
        ]);

        // Update transaction
        $transaction->update([
            'external_email' => $externalEmail,
            'external_payment_token' => $externalLink->token,
            'external_payment_status' => 'link_created',
            'payment_method' => $transaction->credit_used > 0 ? 'mixed' : 'external_link',
        ]);

        return $externalLink;
    }

    /**
     * Send external payment link via email
     */
    public function sendExternalPaymentLink(ExternalPaymentLink $link): bool
    {
        try {
            // TODO: Create and send email with payment link
            Mail::to($link->external_email)->send(new ExternalPaymentLinkMail($link));

            $link->update([
                'status' => 'sent',
                'sent_at' => Carbon::now(),
            ]);

            $link->paymentTransaction->update([
                'external_payment_status' => 'link_sent',
                'external_link_sent_at' => Carbon::now(),
            ]);

            Log::info('External payment link sent', [
                'link_id' => $link->id,
                'email' => $link->external_email,
                'amount' => $link->amount_to_pay,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send external payment link', [
                'link_id' => $link->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Complete a payment transaction
     */
    public function completeTransaction(PaymentTransaction $transaction, array $paymentDetails = []): bool
    {
        return DB::transaction(function () use ($transaction, $paymentDetails) {
            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'payment_completed_at' => Carbon::now(),
                'stripe_payment_intent_id' => $paymentDetails['stripe_payment_intent_id'] ?? null,
                'stripe_charge_id' => $paymentDetails['stripe_charge_id'] ?? null,
                'paid_amount' => $paymentDetails['paid_amount'] ?? $transaction->getRemainingAmount(),
            ]);

            // For external payments, create registration records if they don't exist
            if ($transaction->payment_type === 'external') {
                foreach ($transaction->player_ids as $playerId) {
                    // Create or update ProgramRegistration
                    $registration = ProgramRegistration::firstOrCreate([
                        'user_id' => $transaction->user_id,
                        'program_id' => $transaction->program_id,
                        'player_id' => $playerId,
                    ], [
                        'amount' => $transaction->total_amount,
                        'is_paid' => false,
                        'pending_amount' => $transaction->total_amount,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);

                    // Update registration to mark as paid
                    $registration->update([
                        'is_paid' => true,
                        'pending_amount' => 0,
                    ]);

                    // Create PlayerProgram if it doesn't exist
                    PlayerProgram::firstOrCreate([
                        'player_id' => $playerId,
                        'program_id' => $transaction->program_id,
                    ], [
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);

                    // Update invitation status to accepted
                    \App\Models\AdminInvitesPlayerForProgram::where('user_id', $playerId)
                        ->where('program_id', $transaction->program_id)
                        ->where('status', 'pending')
                        ->update(['status' => 'accepted']);
                }
            } else {
                // For regular payments, just mark existing registrations as paid
                ProgramRegistration::where('user_id', $transaction->user_id)
                    ->where('program_id', $transaction->program_id)
                    ->whereIn('player_id', $transaction->player_ids)
                    ->update(['is_paid' => true]);
            }

            Log::info('Payment transaction completed', [
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $transaction->user_id,
                'program_id' => $transaction->program_id,
                'total_amount' => $transaction->total_amount,
                'credit_used' => $transaction->credit_used,
                'paid_amount' => $transaction->paid_amount,
                'payment_type' => $transaction->payment_type,
            ]);

            return true;
        });
    }

    /**
     * Get payment summary for admin dashboard
     */
    public function getPaymentSummaryForProgram(int $programId): array
    {
        $program = Program::findOrFail($programId);
        $transactions = $program->paymentTransactions()->with(['user', 'creditUsageLogs', 'externalPaymentLink'])->get();

        $summary = [
            'program' => $program,
            'total_transactions' => $transactions->count(),
            'completed_payments' => $transactions->where('status', 'completed')->count(),
            'pending_payments' => $transactions->where('status', 'pending')->count(),
            'total_revenue' => $transactions->where('status', 'completed')->sum('paid_amount'),
            'total_credit_used' => $transactions->sum('credit_used'),
            'external_links_sent' => $transactions->whereNotNull('external_email')->count(),
            'transactions' => $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->transaction_id,
                    'guardian_name' => $transaction->user->name,
                    'guardian_email' => $transaction->user->email,
                    'player_names' => $transaction->getPlayerNames(),
                    'total_amount' => $transaction->total_amount,
                    'paid_amount' => $transaction->paid_amount,
                    'credit_used' => $transaction->credit_used,
                    'pending_amount' => $transaction->getRemainingAmount(),
                    'payment_method' => $transaction->payment_method,
                    'payment_type' => $transaction->payment_type,
                    'status' => $transaction->status,
                    'external_email' => $transaction->external_email,
                    'external_payment_status' => $transaction->external_payment_status,
                    'created_at' => $transaction->created_at,
                    'completed_at' => $transaction->payment_completed_at,
                    'credit_details' => $transaction->creditUsageLogs->map(function ($log) {
                        return [
                            'credit_amount_used' => $log->credit_amount_used,
                            'credit_balance_before' => $log->credit_balance_before,
                            'credit_balance_after' => $log->credit_balance_after,
                            'used_at' => $log->created_at,
                        ];
                    }),
                ];
            }),
        ];

        return $summary;
    }
}
