<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'user_id',
        'program_id',
        'player_ids',
        'total_amount',
        'paid_amount',
        'credit_used',
        'pending_amount',
        'payment_method',
        'payment_type',
        'status',
        'external_email',
        'external_payment_token',
        'external_link_sent_at',
        'external_link_expires_at',
        'external_payment_status',
        'stripe_payment_intent_id',
        'stripe_charge_id',
        'stripe_subscription_id',
        'payment_metadata',
        'credit_usage_details',
        'payment_completed_at',
    ];

    protected $casts = [
        'player_ids' => 'array',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'credit_used' => 'decimal:2',
        'pending_amount' => 'decimal:2',
        'payment_metadata' => 'array',
        'credit_usage_details' => 'array',
        'external_link_sent_at' => 'datetime',
        'external_link_expires_at' => 'datetime',
        'payment_completed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = 'TXN_' . strtoupper(Str::random(12)) . '_' . time();
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function creditUsageLogs(): HasMany
    {
        return $this->hasMany(CreditUsageLog::class);
    }

    public function externalPaymentLink(): HasOne
    {
        return $this->hasOne(ExternalPaymentLink::class);
    }

    // Helper Methods
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function hasExternalPayment(): bool
    {
        return !empty($this->external_email);
    }

    public function getTotalCreditUsed(): float
    {
        return $this->creditUsageLogs->sum('credit_amount_used');
    }

    public function getPlayerNames(): array
    {
        if (empty($this->player_ids)) {
            return [];
        }

        return User::whereIn('id', $this->player_ids)
            ->get()
            ->map(function ($user) {
                return $user->firstName . ' ' . $user->lastName;
            })
            ->toArray();
    }

    public function getRemainingAmount(): float
    {
        return max(0, $this->total_amount - $this->paid_amount - $this->credit_used);
    }

    public function canUseCredit(): bool
    {
        return $this->status === 'pending' && $this->user->getTotalAvailableCredit() > 0;
    }

    // Scopes
    public function scopeForProgram($query, $programId)
    {
        return $query->where('program_id', $programId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeWithExternalPayment($query)
    {
        return $query->whereNotNull('external_email');
    }
}
