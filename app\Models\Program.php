<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Program extends Model
{
    use HasFactory;

    protected $table = 'programs';
    protected $casts = [
        'frequency_days' => 'array',
    ];
    protected $fillable = [
        'name',
        'slug',
        'sub_program',
        'location',
        'sport',
        'gender',
        'age_restriction_from',
        'age_restriction_to',
        'grade',
        'birth_date_cutoff',
        'registration_opening_date',
        'registration_closing_date',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'frequency',
        'frequency_days',
        'number_of_registers',
        'enable_waitlist',
        'cost',
        'enable_early_bird_specials',
        'payment',
        'program_description',
        'season',
        'type',
        'status',
        'early_bird_specials_date',
        'down_payment',
        'up_to_months',
    ];


    public static function boot()
    {
        parent::boot();

        static::creating(function ($program) {
            $baseSlug = Str::slug($program->name);
            $slug = $baseSlug;
            $i = 1;

            while (Program::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $i++;
            }

            $program->slug = $slug;
        });
    }

    public function earlyBirdPricing()
    {
        return $this->hasMany(EarlyBirdPricing::class, 'program_id');
    }


    public function players()
    {
        return $this->hasMany(PlayerProgram::class, 'program_id');
    }


    public function scopeSport(Builder $query, string $sport): Builder
    {
        return $query->where('sport', '=', $sport);
    }


    public function scopeAgeGroup(Builder $query, string $ageGroup): Builder
    {
        $ageRanges = [
            'adult' => [19, 100],
            'u18' => [17, 18],
            'u16' => [15, 16],
            'u14' => [13, 14],
            'u12' => [11, 12],
            'u10' => [9, 10],
            'u8' => [7, 8],
        ];


        $startAge = $ageRanges[$ageGroup][0] ?? null;
        $endAge = $ageRanges[$ageGroup][1] ?? null;


        if ($startAge !== null && $endAge !== null) {
            return $query->where('age_restriction_from', '>=', $startAge)
                ->where('age_restriction_to', '<=', $endAge);
        }


        if ($startAge !== null && $endAge === null) {
            return $query->where('age_restriction_from', '>=', $startAge);
        }

        return $query;
    }


    private function getAgeRestrictions($ageGroup)
    {
        switch ($ageGroup) {
            case 'adult':
                return [18, null];
            case 'highSchool':
                return [14, 18];
            case 'u14':
                return [12, 14];
            case 'u12':
                return [10, 12];
            case 'u10':
                return [8, 10];
            case 'u8':
                return [6, 8];
            default:
                return null;
        }
    }

    public function teamPrograms()
    {
        return $this->hasMany(TeamProgram::class);
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_program', 'program_id', 'team_id');
    }


    public function registrations()
    {
        return $this->hasMany(ProgramRegistration::class, 'program_id');
    }

    /**
     * Get payment transactions for this program
     */
    public function paymentTransactions()
    {
        return $this->hasMany(\App\Models\PaymentTransaction::class);
    }

    /**
     * Get credit usage logs for this program
     */
    public function creditUsageLogs()
    {
        return $this->hasMany(\App\Models\CreditUsageLog::class);
    }

    /**
     * Get external payment links for this program
     */
    public function externalPaymentLinks()
    {
        return $this->hasMany(\App\Models\ExternalPaymentLink::class);
    }

    /**
     * Check if program supports specific payment type
     */
    public function supportsPaymentType($paymentType)
    {
        $supportedTypes = explode(',', $this->payment);
        return in_array($paymentType, $supportedTypes);
    }

    /**
     * Get available payment methods for this program
     */
    public function getAvailablePaymentMethods()
    {
        $methods = [];
        $paymentTypes = explode(',', $this->payment);

        foreach ($paymentTypes as $type) {
            $methods[] = trim($type);
        }

        return $methods;
    }

    /**
     * Check if program is Individual or AAU type
     */
    public function isIndividualOrAAU()
    {
        return in_array($this->type, ['Individual', 'AAU']);
    }

    /**
     * Get comprehensive payment statistics for admin
     */
    public function getPaymentStatistics()
    {
        $transactions = $this->paymentTransactions();

        return [
            'total_transactions' => $transactions->count(),
            'completed_transactions' => $transactions->where('status', 'completed')->count(),
            'pending_transactions' => $transactions->where('status', 'pending')->count(),
            'total_revenue' => $transactions->where('status', 'completed')->sum('paid_amount'),
            'total_credit_used' => $transactions->sum('credit_used'),
            'external_payment_links' => $this->externalPaymentLinks()->count(),
            'active_external_links' => $this->externalPaymentLinks()->active()->count(),
        ];
    }

    /**
     * Get coupons associated with this program
     */
    public function coupons()
    {
        return $this->hasMany(ProgramCoupon::class);
    }

    /**
     * Apply coupon to program price
     */
    public function applyDiscount($couponCode)
    {
        $coupon = $this->coupons()
            ->where('code', $couponCode)
            ->first();

        if (!$coupon || !$coupon->isValid()) {
            return false;
        }

        $discount = $coupon->calculateDiscount($this->cost);
        return [
            'original_price' => $this->cost,
            'discount_amount' => $discount,
            'final_price' => $this->cost - $discount
        ];
    }
}
