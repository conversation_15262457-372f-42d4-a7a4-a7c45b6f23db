<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice {{ $invoice->stripe_invoice_id ?: 'INV-' . $invoice->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .customer-info, .invoice-details {
            flex: 1;
        }
        .invoice-details {
            text-align: right;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .total {
            text-align: right;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>INVOICE</h1>
        <h2>Mass Premier Courts</h2>
    </div>

    <div class="invoice-info">
        <div class="customer-info">
            <h3>Bill To:</h3>
            <p>
                <strong>{{ $invoice->user->firstName }} {{ $invoice->user->lastName }}</strong><br>
                {{ $invoice->user->email }}<br>
                @if($invoice->user->address)
                    {{ $invoice->user->address }}<br>
                @endif
                @if($invoice->user->town)
                    {{ $invoice->user->town }}, {{ $invoice->user->state }}
                @endif
            </p>
        </div>
        <div class="invoice-details">
            <h3>Invoice Details:</h3>
            <p>
                <strong>Invoice #:</strong> {{ $invoice->stripe_invoice_id ?: 'INV-' . $invoice->id }}<br>
                <strong>Date:</strong> {{ $invoice->created_at->format('M d, Y') }}<br>
                <strong>Status:</strong> {{ ucfirst($invoice->status) }}<br>
                <strong>Payment Type:</strong> {{ ucfirst(str_replace('_', ' ', $invoice->payment_type ?? 'unknown')) }}
            </p>
        </div>
    </div>

    @if($invoice->programs->count() > 0)
    <h3>Programs:</h3>
    <table>
        <thead>
            <tr>
                <th>Program Name</th>
                <th>Sport</th>
                <th>Location</th>
                <th>Season</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->programs as $program)
            <tr>
                <td>{{ $program->name }}</td>
                <td>{{ $program->sport }}</td>
                <td>{{ $program->location }}</td>
                <td>{{ $program->season }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    @if($invoice->players->count() > 0)
    <h3>Players:</h3>
    <table>
        <thead>
            <tr>
                <th>Player Name</th>
                <th>Email</th>
                <th>Age</th>
                <th>Grade</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->players as $player)
            <tr>
                <td>{{ $player->firstName }} {{ $player->lastName }}</td>
                <td>{{ $player->email }}</td>
                <td>{{ $player->age ?: 'N/A' }}</td>
                <td>{{ $player->grade ?: 'N/A' }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="total">
        <p><strong>Total Amount: ${{ number_format($invoice->amount, 2) }}</strong></p>
        @if($invoice->refunded_amount > 0)
        <p><strong>Refunded: ${{ number_format($invoice->refunded_amount, 2) }}</strong></p>
        <p><strong>Remaining: ${{ number_format($invoice->remaining_amount, 2) }}</strong></p>
        @endif
    </div>

    @if($invoice->refunds->count() > 0)
    <h3>Refunds:</h3>
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Amount</th>
                <th>Reason</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->refunds as $refund)
            <tr>
                <td>{{ $refund->created_at->format('M d, Y') }}</td>
                <td>${{ number_format($refund->amount, 2) }}</td>
                <td>{{ ucfirst($refund->reason) }}</td>
                <td>{{ ucfirst($refund->status) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Mass Premier Courts</p>
    </div>
</body>
</html>
