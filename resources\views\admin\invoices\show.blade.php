@extends('layouts.app')

@section('title', 'Invoice Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Invoice Details</h4>
                    <div>
                        @if($invoice->stripe_invoice_id)
                            <a href="{{ route('admin.invoices.download', $invoice) }}"
                               class="btn btn-outline-primary">
                                <i class="bi bi-download"></i> Download PDF
                            </a>
                        @endif
                        <a href="{{ route('admin.invoices.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Invoices
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Invoice Information -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Invoice Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Invoice #:</strong></td>
                                            <td>{{ $invoice->stripe_invoice_id ?: 'INV-' . $invoice->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $invoice->status === 'paid' ? 'success' : ($invoice->status === 'open' ? 'warning' : 'secondary') }}">
                                                    {{ ucfirst($invoice->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Payment Type:</strong></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ ucfirst(str_replace('_', ' ', $invoice->payment_type ?? 'unknown')) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount:</strong></td>
                                            <td>${{ number_format($invoice->amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Refunded:</strong></td>
                                            <td>${{ number_format($invoice->refunded_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Remaining:</strong></td>
                                            <td>${{ number_format($invoice->remaining_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Date:</strong></td>
                                            <td>{{ $invoice->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        @if($invoice->paid_at)
                                        <tr>
                                            <td><strong>Paid At:</strong></td>
                                            <td>{{ $invoice->paid_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Customer Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td>{{ $invoice->user->firstName }} {{ $invoice->user->lastName }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td>{{ $invoice->user->email }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td>{{ $invoice->user->mobile_number ?: 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td>
                                                {{ $invoice->user->address ?: 'N/A' }}
                                                @if($invoice->user->town)
                                                    <br>{{ $invoice->user->town }}, {{ $invoice->user->state }}
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Programs and Players -->
                            @if($invoice->programs->count() > 0)
                            <div class="mt-4">
                                <h5>Programs</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Program Name</th>
                                                <th>Sport</th>
                                                <th>Location</th>
                                                <th>Season</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($invoice->programs as $program)
                                            <tr>
                                                <td>{{ $program->name }}</td>
                                                <td>{{ $program->sport }}</td>
                                                <td>{{ $program->location }}</td>
                                                <td>{{ $program->season }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @endif

                            @if($invoice->players->count() > 0)
                            <div class="mt-4">
                                <h5>Players</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Player Name</th>
                                                <th>Email</th>
                                                <th>Age</th>
                                                <th>Grade</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($invoice->players as $player)
                                            <tr>
                                                <td>{{ $player->firstName }} {{ $player->lastName }}</td>
                                                <td>{{ $player->email }}</td>
                                                <td>{{ $player->age ?: 'N/A' }}</td>
                                                <td>{{ $player->grade ?: 'N/A' }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Refunds Section -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Refunds</h5>
                                </div>
                                <div class="card-body">
                                    @if($invoice->refunds->count() > 0)
                                        @foreach($invoice->refunds as $refund)
                                        <div class="border-bottom pb-2 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span class="fw-bold">${{ number_format($refund->amount, 2) }}</span>
                                                <span>{!! $refund->status_badge !!}</span>
                                            </div>
                                            <small class="text-muted">
                                                {{ ucfirst($refund->reason) }} - {{ $refund->created_at->format('M d, Y') }}
                                            </small>
                                            @if($refund->metadata && isset($refund->metadata['notes']))
                                            <div class="mt-1">
                                                <small class="text-muted">{{ $refund->metadata['notes'] }}</small>
                                            </div>
                                            @endif
                                        </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">No refunds processed</p>
                                    @endif

                                    @if($invoice->canBeRefunded())
                                    <div class="mt-3">
                                        <button type="button"
                                                class="btn btn-warning btn-sm w-100"
                                                onclick="showRefundModal({{ $invoice->id }}, {{ $invoice->amount }}, {{ $invoice->refunded_amount }})">
                                            <i class="bi bi-arrow-clockwise"></i> Process Refund
                                        </button>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Stripe Information -->
                            @if($invoice->stripe_invoice_id || $invoice->stripe_payment_intent_id)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Stripe Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        @if($invoice->stripe_invoice_id)
                                        <tr>
                                            <td><strong>Invoice ID:</strong></td>
                                            <td><code>{{ $invoice->stripe_invoice_id }}</code></td>
                                        </tr>
                                        @endif
                                        @if($invoice->stripe_payment_intent_id)
                                        <tr>
                                            <td><strong>Payment Intent:</strong></td>
                                            <td><code>{{ $invoice->stripe_payment_intent_id }}</code></td>
                                        </tr>
                                        @endif
                                        @if($invoice->stripe_charge_id)
                                        <tr>
                                            <td><strong>Charge ID:</strong></td>
                                            <td><code>{{ $invoice->stripe_charge_id }}</code></td>
                                        </tr>
                                        @endif
                                        @if($invoice->stripe_subscription_id)
                                        <tr>
                                            <td><strong>Subscription:</strong></td>
                                            <td><code>{{ $invoice->stripe_subscription_id }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Subscription Management:</strong></td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-warning mb-1"
                                                            onclick="disableAutoPay({{ $invoice->id }})"
                                                            title="Pause subscription payments">
                                                        <i class="bi bi-pause-circle"></i> Disable Auto-Pay
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success mb-1"
                                                            onclick="enableAutoPay({{ $invoice->id }})"
                                                            title="Resume subscription payments">
                                                        <i class="bi bi-play-circle"></i> Enable Auto-Pay
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="cancelSubscription({{ $invoice->id }})"
                                                            title="Cancel subscription at period end">
                                                        <i class="bi bi-x-circle"></i> Cancel Subscription
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Refund</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="refundForm">
                    <div class="mb-3">
                        <label class="form-label">Invoice Amount</label>
                        <input type="text" class="form-control" id="invoice-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Already Refunded</label>
                        <input type="text" class="form-control" id="refunded-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Refundable Amount</label>
                        <input type="text" class="form-control" id="refundable-amount" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Refund Amount *</label>
                        <input type="number" class="form-control" id="refund-amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Reason *</label>
                        <select class="form-select" id="refund-reason" required>
                            <option value="">Select Reason</option>
                            <option value="duplicate">Duplicate</option>
                            <option value="fraudulent">Fraudulent</option>
                            <option value="requested_by_customer">Requested by Customer</option>
                            <option value="expired_uncaptured_charge">Expired Uncaptured Charge</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="refund-notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="processRefund()">Process Refund</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
let currentInvoiceId = {{ $invoice->id }};

function showRefundModal(invoiceId, amount, refundedAmount) {
    currentInvoiceId = invoiceId;
    const refundableAmount = amount - refundedAmount;

    document.getElementById('invoice-amount').value = '$' + parseFloat(amount).toFixed(2);
    document.getElementById('refunded-amount').value = '$' + parseFloat(refundedAmount).toFixed(2);
    document.getElementById('refundable-amount').value = '$' + parseFloat(refundableAmount).toFixed(2);
    document.getElementById('refund-amount').max = refundableAmount;
    document.getElementById('refund-amount').value = refundableAmount;

    new bootstrap.Modal(document.getElementById('refundModal')).show();
}

function processRefund() {
    const amount = document.getElementById('refund-amount').value;
    const reason = document.getElementById('refund-reason').value;
    const notes = document.getElementById('refund-notes').value;

    if (!amount || !reason) {
        alert('Please fill in all required fields.');
        return;
    }

    fetch(`/admin/invoices/${currentInvoiceId}/refund`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: parseFloat(amount),
            reason: reason,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('refundModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing the refund.');
    });
}

// Subscription management functions
function cancelSubscription(invoiceId) {
    if (!confirm('Are you sure you want to cancel this subscription? It will be cancelled at the end of the current billing period.')) {
        return;
    }

    fetch(`/admin/invoices/${invoiceId}/cancel-subscription`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while cancelling the subscription.');
    });
}

function disableAutoPay(invoiceId) {
    if (!confirm('Are you sure you want to disable auto-pay for this subscription? Future payments will not be automatically charged.')) {
        return;
    }

    fetch(`/admin/invoices/${invoiceId}/disable-autopay`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while disabling auto-pay.');
    });
}

function enableAutoPay(invoiceId) {
    if (!confirm('Are you sure you want to enable auto-pay for this subscription? Future payments will be automatically charged.')) {
        return;
    }

    fetch(`/admin/invoices/${invoiceId}/enable-autopay`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while enabling auto-pay.');
    });
}
</script>
@endpush
