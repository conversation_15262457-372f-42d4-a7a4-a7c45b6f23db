<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old programs table if it exists
        if (Schema::hasTable('programs')) {
            Schema::dropIfExists('programs');
        }

        // Create the new consolidated programs table
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('sub_program');
            $table->string('location');
            $table->string('sport');
            $table->string('gender');
            $table->integer('age_restriction_from')->nullable();
            $table->integer('age_restriction_to')->nullable();
            $table->string('grade')->nullable();
            $table->date('birth_date_cutoff')->nullable()->comment('Players born on or after this date can join the program');
            $table->date('registration_opening_date');
            $table->date('registration_closing_date');
            $table->date('start_date');
            $table->date('end_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->string('frequency');
            $table->json('frequency_days');
            $table->integer('number_of_registers');
            $table->boolean('enable_waitlist')->default(false);
            $table->decimal('cost', 10, 2);
            $table->decimal('minimum_recurring_amount', 10, 2)->nullable();
            $table->boolean('enable_early_bird_specials')->default(false);
            $table->enum('payment', ['full', 'recurring', 'split']);
            $table->text('program_description');
            $table->string('season');
            $table->enum('type', ['Individual', 'Team', 'AAU', 'Tryout']);
            $table->enum('status', ['public', 'private'])->default('public');
            $table->boolean('is_draft')->default(0);
            $table->date('early_bird_specials_date')->nullable();
            $table->timestamps();
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
