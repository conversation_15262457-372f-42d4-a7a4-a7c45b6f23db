<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CreditUsageLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_transaction_id',
        'guardian_credit_id',
        'user_id',
        'program_id',
        'credit_amount_used',
        'credit_balance_before',
        'credit_balance_after',
        'player_ids',
        'usage_context',
    ];

    protected $casts = [
        'player_ids' => 'array',
        'credit_amount_used' => 'decimal:2',
        'credit_balance_before' => 'decimal:2',
        'credit_balance_after' => 'decimal:2',
    ];

    // Relationships
    public function paymentTransaction(): BelongsTo
    {
        return $this->belongsTo(PaymentTransaction::class);
    }

    public function guardianCredit(): BelongsTo
    {
        return $this->belongsTo(GuardianCredit::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    // Helper Methods
    public function getPlayerNames(): array
    {
        if (empty($this->player_ids)) {
            return [];
        }
        
        return User::whereIn('id', $this->player_ids)->pluck('name')->toArray();
    }

    // Scopes
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForProgram($query, $programId)
    {
        return $query->where('program_id', $programId);
    }
}
