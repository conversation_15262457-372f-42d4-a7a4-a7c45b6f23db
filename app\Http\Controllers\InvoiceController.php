<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Refund;
use App\Models\User;
use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Invoice as StripeInvoice;
use Stripe\Refund as StripeRefund;
use Stripe\PaymentIntent;
use Stripe\Charge;
use Stripe\Subscription;
use Stripe\Exception\ApiErrorException;

class InvoiceController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Invoice::with(['user', 'refunds']);

        // Filter by user if not admin
        if (!$user->hasRole('admin')) {
            $query->where('user_id', $user->id);
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $invoices = $query->orderBy('created_at', 'desc')->paginate(15);

        if ($request->ajax()) {
            return response()->json([
                'invoices' => view('admin.invoices.partialInvoicesList', compact('invoices'))->render(),
                'pagination' => $invoices->links('pagination::bootstrap-5')->render(),
            ]);
        }

        return view('admin.invoices.index', compact('invoices'));
    }

    public function show(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        $invoice->load(['user', 'refunds']);

        return view('admin.invoices.show', compact('invoice'));
    }

    public function createInvoiceFromStripe($stripeInvoiceId)
    {
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $stripeInvoice = StripeInvoice::retrieve($stripeInvoiceId);
            $paymentIntent = $stripeInvoice->payment_intent ? PaymentIntent::retrieve($stripeInvoice->payment_intent) : null;

            // Extract metadata
            $metadata = $stripeInvoice->metadata->toArray();

            // Find user from metadata
            $userId = $metadata['user_id'] ?? null;
            if (!$userId) {
                Log::error('No user_id found in invoice metadata', ['stripe_invoice_id' => $stripeInvoiceId]);
                return null;
            }

            $user = User::find($userId);
            if (!$user) {
                Log::error('User not found for invoice', ['user_id' => $userId, 'stripe_invoice_id' => $stripeInvoiceId]);
                return null;
            }

            // Create or update invoice
            $invoice = Invoice::updateOrCreate(
                ['stripe_invoice_id' => $stripeInvoiceId],
                [
                    'user_id' => $userId,
                    'stripe_payment_intent_id' => $stripeInvoice->payment_intent,
                    'stripe_charge_id' => $paymentIntent ? $paymentIntent->latest_charge : null,
                    'stripe_subscription_id' => $stripeInvoice->subscription,
                    'amount' => $stripeInvoice->amount_paid / 100,
                    'currency' => $stripeInvoice->currency,
                    'status' => $stripeInvoice->status,
                    'payment_type' => $this->normalizePaymentType($metadata['payment_type'] ?? null),
                    'program_ids' => isset($metadata['program_id']) ? [$metadata['program_id']] : (isset($metadata['program_ids']) ? json_decode($metadata['program_ids'], true) : null),
                    'player_ids' => isset($metadata['player_ids']) ? json_decode($metadata['player_ids'], true) : null,
                    'metadata' => $metadata,
                    'invoice_date' => $stripeInvoice->created,
                    'due_date' => $stripeInvoice->due_date,
                    'paid_at' => $stripeInvoice->status === 'paid' ? now() : null,
                ]
            );

            return $invoice;
        } catch (ApiErrorException $e) {
            Log::error('Stripe API error creating invoice', [
                'stripe_invoice_id' => $stripeInvoiceId,
                'error' => $e->getMessage()
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Error creating invoice from Stripe', [
                'stripe_invoice_id' => $stripeInvoiceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function processRefund(Request $request, Invoice $invoice)
    {
        $this->authorize('refund', $invoice);

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $invoice->refundable_amount,
            'reason' => 'required|in:duplicate,fraudulent,requested_by_customer,expired_uncaptured_charge',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            // Get the charge ID from the invoice
            $chargeId = $invoice->stripe_charge_id;
            if (!$chargeId) {
                // Try to get charge from payment intent
                if ($invoice->stripe_payment_intent_id) {
                    $paymentIntent = PaymentIntent::retrieve($invoice->stripe_payment_intent_id);
                    $chargeId = $paymentIntent->latest_charge;
                }
            }

            if (!$chargeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'No charge found for this invoice.'
                ], 400);
            }

            // Create refund in Stripe
            $stripeRefund = StripeRefund::create([
                'charge' => $chargeId,
                'amount' => $request->amount * 100, // Convert to cents
                'reason' => $request->reason,
                'metadata' => [
                    'invoice_id' => $invoice->id,
                    'user_id' => $invoice->user_id,
                    'notes' => $request->notes,
                    'refunded_by' => auth()->id(),
                ],
            ]);

            // Create refund record in database
            $refund = Refund::create([
                'invoice_id' => $invoice->id,
                'stripe_refund_id' => $stripeRefund->id,
                'amount' => $request->amount,
                'currency' => $stripeRefund->currency,
                'reason' => $request->reason,
                'status' => $stripeRefund->status,
                'processed_at' => $stripeRefund->status === 'succeeded' ? now() : null,
                'metadata' => [
                    'notes' => $request->notes,
                    'refunded_by' => auth()->id(),
                ],
            ]);

            // Update invoice refund status
            $totalRefunded = $invoice->refunds()->where('status', 'succeeded')->sum('amount');
            $invoice->refunded_amount = $totalRefunded;
            $invoice->refund_status = $totalRefunded >= $invoice->amount ? 'full' : ($totalRefunded > 0 ? 'partial' : 'none');
            $invoice->save();

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully.',
                'refund' => $refund,
                'invoice' => $invoice->fresh(),
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Stripe refund error', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Stripe error: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            Log::error('Refund processing error', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error processing refund: ' . $e->getMessage()
            ], 500);
        }
    }

    public function downloadInvoice(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            if ($invoice->stripe_invoice_id) {
                $stripeInvoice = StripeInvoice::retrieve($invoice->stripe_invoice_id);

                // Get the invoice PDF from Stripe
                $pdf = $stripeInvoice->invoice_pdf;

                if ($pdf) {
                    return redirect($pdf);
                }
            }

            // If no Stripe PDF, generate our own
            return $this->generateInvoicePDF($invoice);
        } catch (\Exception $e) {
            Log::error('Error downloading invoice', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Unable to download invoice.');
        }
    }

    private function generateInvoicePDF(Invoice $invoice)
    {
        // This would generate a custom PDF invoice
        // For now, we'll return a simple view
        $invoice->load(['user']);

        return view('admin.invoices.pdf', compact('invoice'));
    }

    public function syncInvoicesFromStripe()
    {
        try {
            Stripe::setApiKey(env('STRIPE_SECRET'));

            $invoices = StripeInvoice::all([
                'limit' => 100,
                'status' => 'paid',
            ]);

            $syncedCount = 0;
            foreach ($invoices->data as $stripeInvoice) {
                $invoice = $this->createInvoiceFromStripe($stripeInvoice->id);
                if ($invoice) {
                    $syncedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully synced {$syncedCount} invoices from Stripe."
            ]);
        } catch (\Exception $e) {
            Log::error('Error syncing invoices from Stripe', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Error syncing invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Normalize payment type to match database enum values
     */
    private function normalizePaymentType($paymentType)
    {
        if (!$paymentType) {
            return null;
        }

        $mapping = [
            'recurringPayments' => 'recurring',
            'fullAmount' => 'full',
            'specificAmount' => 'split',
            'splitPayments' => 'split',
            'Outstanding to recurring' => 'outstanding_to_recurring',
        ];

        return $mapping[$paymentType] ?? $paymentType;
    }

    /**
     * Cancel subscription for recurring payment invoice
     */
    public function cancelSubscription(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        if (!$invoice->stripe_subscription_id) {
            return response()->json(['success' => false, 'message' => 'No subscription found for this invoice']);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Cancel the subscription at period end
            $subscription = Subscription::update($invoice->stripe_subscription_id, [
                'cancel_at_period_end' => true
            ]);

            // Update invoice status
            $invoice->update([
                'status' => 'cancelled',
                'metadata' => array_merge($invoice->metadata ?? [], [
                    'cancelled_at' => now()->toISOString(),
                    'cancelled_by' => auth()->id()
                ])
            ]);

            Log::info('Subscription cancelled', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'cancelled_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription will be cancelled at the end of the current billing period'
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Failed to cancel subscription', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['success' => false, 'message' => 'Failed to cancel subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Disable auto-pay for subscription (pause subscription)
     */
    public function disableAutoPay(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        if (!$invoice->stripe_subscription_id) {
            return response()->json(['success' => false, 'message' => 'No subscription found for this invoice']);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Pause the subscription
            $subscription = Subscription::update($invoice->stripe_subscription_id, [
                'pause_collection' => [
                    'behavior' => 'void'
                ]
            ]);

            // Update invoice metadata
            $invoice->update([
                'metadata' => array_merge($invoice->metadata ?? [], [
                    'auto_pay_disabled_at' => now()->toISOString(),
                    'auto_pay_disabled_by' => auth()->id()
                ])
            ]);

            Log::info('Auto-pay disabled for subscription', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'disabled_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Auto-pay has been disabled for this subscription'
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Failed to disable auto-pay', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['success' => false, 'message' => 'Failed to disable auto-pay: ' . $e->getMessage()]);
        }
    }

    /**
     * Enable auto-pay for subscription (resume subscription)
     */
    public function enableAutoPay(Invoice $invoice)
    {
        $this->authorize('view', $invoice);

        if (!$invoice->stripe_subscription_id) {
            return response()->json(['success' => false, 'message' => 'No subscription found for this invoice']);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Resume the subscription
            $subscription = Subscription::update($invoice->stripe_subscription_id, [
                'pause_collection' => null
            ]);

            // Update invoice metadata
            $invoice->update([
                'metadata' => array_merge($invoice->metadata ?? [], [
                    'auto_pay_enabled_at' => now()->toISOString(),
                    'auto_pay_enabled_by' => auth()->id()
                ])
            ]);

            Log::info('Auto-pay enabled for subscription', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'enabled_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Auto-pay has been enabled for this subscription'
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Failed to enable auto-pay', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->stripe_subscription_id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['success' => false, 'message' => 'Failed to enable auto-pay: ' . $e->getMessage()]);
        }
    }
}
