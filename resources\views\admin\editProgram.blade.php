@extends('layouts.app')
@section('title', 'Edit Program')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Edit Program</h1>
    </section>

    @if (session('success'))
        <div id="successMessageForSession">
            <span id="successText">{{ session('success') }}</span>
        </div>
    @endif

    @if (session('error'))
        <div id="errorMessageForSession">
            <span id="errorText">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Frontend Validation Error Message -->
    <div id="frontendErrorMessage" style="display: none;">
        <span id="frontendErrorText"></span>
    </div>

    @if (
        $errors->hasAny([
            'early_bird_specials_date',
            'early_bird_from.*',
            'early_bird_to.*',
            'price_before.*',
            'price_on_or_after.*',
        ]))
        <div id="errorMessageForSession">
            If Early Bird Specials have been checked, you must fill in all the details correctly.
        </div>
    @endif

    <div class="backButtonforAddPrograms">
        <a class="cta"
            href="{{ url()->previous() !== url()->current() ? url()->previous() : route('admin.program.allPrograms') }}">
            Back
        </a>
    </div>



    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-9">
                    <form class="row" action="{{ route('admin.program.update', $program->slug) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="name">Program Name</label>
                            <input class="form-control @error('name') is-invalid @enderror" id="programName" type="text"
                                name="name" value="{{ old('name', $program->name) }}" />
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Program Type -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="type">Program Type</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('type') is-invalid @enderror" id="type"
                                    name="type">
                                    <option value="" {{ old('type', $program->type) == '' ? 'selected' : '' }}>Select
                                        Program Type</option>
                                    <option value="Individual"
                                        {{ old('type', $program->type) == 'Individual' ? 'selected' : '' }}>Individual
                                    </option>
                                    <option value="Team" {{ old('type', $program->type) == 'Team' ? 'selected' : '' }}>
                                        Team</option>
                                    <option value="AAU" {{ old('type', $program->type) == 'AAU' ? 'selected' : '' }}>AAU
                                    </option>
                                    <option value="Tryout" {{ old('type', $program->type) == 'Tryout' ? 'selected' : '' }}>
                                        Tryout</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <!-- Sub Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="sub_program">Sub Program Name</label>
                            <input class="form-control @error('sub_program') is-invalid @enderror" id="subProgramName"
                                name="sub_program" type="text"
                                value="{{ old('sub_program', $program->sub_program) }}" />
                            @error('sub_program')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="location">Location</label>
                            <input class="form-control @error('location') is-invalid @enderror" id="location"
                                name="location" type="text" value="{{ old('location', $program->location) }}" />
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Sports -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="sport">Sport</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('sport') is-invalid @enderror" id="sports"
                                    name="sport">
                                    <option value="" {{ old('sport', $program->sport) === '' ? 'selected' : '' }}>
                                        Select</option>
                                    <option value="basketball"
                                        {{ old('sport', $program->sport) === 'basketball' ? 'selected' : '' }}>Basketball
                                    </option>
                                    <option value="volleyball"
                                        {{ old('sport', $program->sport) === 'volleyball' ? 'selected' : '' }}>Volleyball
                                    </option>
                                    <option value="pickleball"
                                        {{ old('sport', $program->sport) === 'pickleball' ? 'selected' : '' }}>Pickleball
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('sport')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Gender -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('gender') is-invalid @enderror" id="gender"
                                    name="gender">
                                    <option value="" {{ old('gender', $program->gender) == '' ? 'selected' : '' }}>
                                        Select</option>
                                    <option value="boys"
                                        {{ old('gender', $program->gender) == 'boys' ? 'selected' : '' }}>Boys</option>
                                    <option value="girls"
                                        {{ old('gender', $program->gender) == 'girls' ? 'selected' : '' }}>Girls</option>

                                    <option value="coed"
                                        {{ old('gender', $program->gender) == 'coed' ? 'selected' : '' }}>Co-ed</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('gender')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Age Restriction -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Age Restriction (optional)</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_from">Start
                                        Age</label>
                                    <input class="form-control @error('age_restriction_from') is-invalid @enderror"
                                        id="age_restriction_from" name="age_restriction_from" type="number"
                                        value="{{ old('age_restriction_from', $program->age_restriction_from) }}" />
                                    @error('age_restriction_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_to">End Age</label>
                                    <input class="form-control @error('age_restriction_to') is-invalid @enderror"
                                        id="age_restriction_to" name="age_restriction_to" type="number"
                                        value="{{ old('age_restriction_to', $program->age_restriction_to) }}" />
                                    @error('age_restriction_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Grade -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="grade">Grade</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('grade') is-invalid @enderror" id="grade"
                                    name="grade">
                                    <option value="" {{ old('grade', $program->grade) == '' ? 'selected' : '' }}>
                                        Select
                                    </option>
                                    <option value="6th" {{ old('grade', $program->grade) == '6th' ? 'selected' : '' }}>
                                        6th
                                    </option>
                                    <option value="7th" {{ old('grade', $program->grade) == '7th' ? 'selected' : '' }}>
                                        7th
                                    </option>
                                    <option value="8th" {{ old('grade', $program->grade) == '8th' ? 'selected' : '' }}>
                                        8th
                                    </option>
                                    <option value="9th" {{ old('grade', $program->grade) == '9th' ? 'selected' : '' }}>
                                        9th
                                    </option>
                                    <option value="10th"
                                        {{ old('grade', $program->grade) == '10th' ? 'selected' : '' }}>
                                        10th
                                    </option>
                                    <option value="adult"
                                        {{ old('grade', $program->grade) == 'adult' ? 'selected' : '' }}>Adult
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('grade')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Birth Date Cutoff -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birth_date_cutoff">Birth Date Cutoff</label>
                            <input class="form-control @error('birth_date_cutoff') is-invalid @enderror"
                                id="birth_date_cutoff" name="birth_date_cutoff" type="date"
                                value="{{ old('birth_date_cutoff', $program->birth_date_cutoff) }}" />
                            @error('birth_date_cutoff')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Registration -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Registration</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_opening_date">Registration Start</label>
                                    <input class="form-control @error('registration_opening_date') is-invalid @enderror"
                                        id="registration_opening_date" name="registration_opening_date" type="date"
                                        value="{{ old('registration_opening_date', $program->registration_opening_date) }}" />
                                    @error('registration_opening_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_closing_date">Registration End</label>
                                    <input class="form-control @error('registration_closing_date') is-invalid @enderror"
                                        id="registration_closing_date" name="registration_closing_date" type="date"
                                        value="{{ old('registration_closing_date', $program->registration_closing_date) }}" />
                                    @error('registration_closing_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Start Date and End Date -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_date">Start Date</label>
                                    <input class="form-control @error('start_date') is-invalid @enderror" id="start_date"
                                        name="start_date" type="date"
                                        value="{{ old('start_date', $program->start_date) }}" />
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_date">End Date</label>
                                    <input class="form-control @error('end_date') is-invalid @enderror" id="end_date"
                                        name="end_date" type="date"
                                        value="{{ old('end_date', $program->end_date) }}" />
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Start Time and End Time -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_time">Start Time</label>
                                    <input class="form-control @error('start_time') is-invalid @enderror" id="start_time"
                                        name="start_time" type="time"
                                        value="{{ old('start_time', $program->start_time) }}" />
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_time">End Time</label>
                                    <input class="form-control @error('end_time') is-invalid @enderror" id="end_time"
                                        name="end_time" type="time"
                                        value="{{ old('end_time', $program->end_time) }}" />
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Frequency -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Frequency</div>

                            <div class="form-sub-label text-uppercase mb-3">Daily</div>
                            <div class="row">
                                @foreach (['mon', 'tue', 'wed', 'thur', 'fri', 'sat', 'sun'] as $day)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency_days') is-invalid @enderror"
                                                id="{{ $day }}" name="frequency_days[]" type="checkbox"
                                                value="{{ $day }}"
                                                {{ is_array(old('frequency_days', $program->frequency_days)) && in_array($day, old('frequency_days', $program->frequency_days)) ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $day }}">{{ strtoupper($day) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="form-sub-label text-uppercase mb-3">Weekly</div>
                            <div class="row">
                                @foreach (['every-week', 'every-other-week', 'once-per-month'] as $week)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency') is-invalid @enderror"
                                                id="{{ $week }}" name="frequency" type="radio"
                                                value="{{ $week }}"
                                                {{ old('frequency', $program->frequency) === $week ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $week }}">{{ strtoupper(str_replace('-', ' ', $week)) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            @error('frequency')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                        </div>

                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Enrollment (optional)</div>
                            <div class="form-sub-label text-uppercase mb-3">Registration Limit</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-2">
                                    <label class="text-uppercase visually-hidden form-label"
                                        for="number_of_registers">Number of Registers</label>
                                    <input class="form-control @error('number_of_registers') is-invalid @enderror"
                                        id="number_of_registers" name="number_of_registers" type="number"
                                        value="{{ old('number_of_registers', $program->number_of_registers) }}" />
                                    @error('number_of_registers')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check">
                                        <input type="hidden" name="enable_waitlist" value="0">
                                        <input class="form-check-input @error('enable_waitlist') is-invalid @enderror"
                                            id="enable-waitlist" name="enable_waitlist" type="checkbox" value="1"
                                            {{ old('enable_waitlist', $program->enable_waitlist) ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="enable-waitlist">Enable
                                            Waitlist</label>
                                    </div>
                                    @error('enable_waitlist')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Cost -->
                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Cost</div>
                            <div class="form-sub-label text-uppercase mb-3">&nbsp;</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase visually-hidden form-label" for="cost">Cost</label>
                                    <input class="form-control @error('cost') is-invalid @enderror"
                                        id="cost"style="width: 100%;" name="cost" type="number" step="0.01"
                                        value="{{ old('cost', $program->cost) }}" />
                                    @error('cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-8">
                                    <div class="form-check col-md-">
                                        <input type="hidden" name="enable_early_bird_specials" value="0">
                                        <input class="form-check-input" id="enable_early_bird_specials" type="checkbox"
                                            value="1" name="enable_early_bird_specials"
                                            {{ old('enable_early_bird_specials', $program->enable_early_bird_specials) ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="enable_early_bird_specials">
                                            Enable Early Bird Specials
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="early_bird_pricing_section" class="col-md-12 mt-4 mb-4 to_set_mb3"
                            style="{{ old('enable_early_bird_specials', $program->enable_early_bird_specials) ? '' : 'display: none;' }}">
                            @if ($earlyBirdPricing->isEmpty())
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.0') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.0') }}" />
                                            @error('early_bird_to.0')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.0') }}" />
                                            @error('price_before.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="date">Date</label>
                                            <input class="form-control" id="date" type="date"
                                                name="early_bird_specials_date"
                                                value="{{ old('early_bird_specials_date') }}" />
                                            @error('early_bird_specials_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceOnorAfter">Price On or
                                                After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.0') }}" />
                                            @error('price_on_or_after.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.1') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.1') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.1') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label" for="priceOnorAfter">
                                                Price On
                                                or After</label>
                                            <input class="form-control" id="price_on_or_after[]" type="number"
                                                step="0.01" @error('price_on_or_after.1') is-invalid @enderror
                                                name="price_on_or_after[]" value="{{ old('price_on_or_after.1') }}" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.2') }}" />
                                            @error('early_bird_to.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.2') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.2') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.2') }}" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.3') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.3') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.3') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.3') }}" />
                                        </div>
                                    </div>
                                </div>
                            @else
                                <!-- Early Bird Pricing -->
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    @for ($index = 0; $index < 4; $index++)
                                        <div class="row align-items-center">
                                            <div class="mb-4 col-md">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_from_{{ $index }}">From</label>
                                                @endif
                                                <input class="form-control" id="early_bird_from_{{ $index }}"
                                                    type="number" step="0.01" name="early_bird_from[]"
                                                    value="{{ old('early_bird_from.' . $index, $earlyBirdPricing[$index]->from ?? '') }}" />
                                            </div>
                                            <div class="mb-4 col-md">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_to_{{ $index }}">To</label>
                                                @endif
                                                <input class="form-control" id="early_bird_to_{{ $index }}"
                                                    type="number" step="0.01" name="early_bird_to[]"
                                                    value="{{ old('early_bird_to.' . $index, $earlyBirdPricing[$index]->to ?? '') }}" />
                                            </div>
                                            <div class="mb-4 col-md-3">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_before_{{ $index }}">Price Before</label>
                                                @endif
                                                <input class="form-control" id="price_before_{{ $index }}"
                                                    type="number" step="0.01" name="price_before[]"
                                                    value="{{ old('price_before.' . $index, $earlyBirdPricing[$index]->price_before ?? '') }}" />
                                            </div>
                                            @if ($index == 0)
                                                <div class="mb-4 col-md-3">
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_specials_date">Date</label>
                                                    <input class="form-control" id="early_bird_specials_date"
                                                        type="date" name="early_bird_specials_date"
                                                        value="{{ old('early_bird_specials_date', $program->early_bird_specials_date) }}" />
                                                </div>
                                            @else
                                                <div class="mb-4 col-md-3"></div>
                                            @endif

                                            <div class="mb-4 col-md-3">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_on_or_after_{{ $index }}">Price On or
                                                        After</label>
                                                @endif
                                                <input class="form-control" id="price_on_or_after_{{ $index }}"
                                                    type="number" step="0.01" name="price_on_or_after[]"
                                                    value="{{ old('price_on_or_after.' . $index, $earlyBirdPricing[$index]->price_on_or_after ?? '') }}" />
                                            </div>
                                        </div>
                                    @endfor
                                </div>

                            @endif
                        </div>

                        <!-- Program Coupons -->
                        <div class="col-12 mt-4 mb-4">
                            <div class="form-label text-uppercase">Program Coupons</div>
                            <button type="button" class="btn btn-primary mt-2" id="add-coupon-btn">Add Coupon</button>
                            <div id="coupons-container" class="mt-3" style="{{ $program->coupons && $program->coupons->count() > 0 ? '' : 'display: none;' }}">
                                @if($program->coupons && $program->coupons->count() > 0)
                                    @foreach($program->coupons as $index => $coupon)
                                    <div class="coupon-entry mb-4" data-index="{{ $index }}">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label class="form-label text-uppercase">Coupon Code</label>
                                                <input type="text" name="coupons[{{ $index }}][code]" class="form-control coupon-code" value="{{ $coupon->code }}" placeholder="e.g., SUMMER2025" required>
                                                <input type="hidden" name="coupons[{{ $index }}][id]" value="{{ $coupon->id }}">
                                                <div class="invalid-feedback">Please enter a unique coupon code.</div>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-uppercase">Discount Type</label>
                                                <select name="coupons[{{ $index }}][discount_type]" class="form-select" required>
                                                    <option value="percentage" {{ $coupon->discount_type == 'percentage' ? 'selected' : '' }}>Percentage Off</option>
                                                    <option value="fixed" {{ $coupon->discount_type == 'fixed' ? 'selected' : '' }}>Fixed Amount Off</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-uppercase">Value</label>
                                                <input type="number" step="0.01" min="0.01" name="coupons[{{ $index }}][discount_value]" class="form-control" value="{{ $coupon->discount_value }}" placeholder="20" required>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-uppercase">Usage Limit</label>
                                                <input type="number" min="1" name="coupons[{{ $index }}][usage_limit]" class="form-control" value="{{ $coupon->usage_limit }}" placeholder="Optional">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-uppercase">Valid Until</label>
                                                <input type="date" name="coupons[{{ $index }}][valid_until]" class="form-control" value="{{ optional($coupon->valid_until)->format('Y-m-d') }}">
                                            </div>
                                            <div class="col-md-1">
                                                <label class="form-label text-uppercase">&nbsp;</label>
                                                <button type="button" class="btn btn-danger remove-coupon form-control">Remove</button>
                                            </div>
                                            <div class="col-md-11">
                                                <label class="form-label text-uppercase">Description</label>
                                                <input type="text" name="coupons[{{ $index }}][description]" class="form-control" value="{{ $coupon->description }}" placeholder="Optional coupon description">
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>

                        <!-- Program Description -->
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="program_description">Program Description
                                <span class="red-text">*</span></label>
                            <textarea class="form-control @error('program_description') is-invalid @enderror" id="program_description"
                                name="program_description" style="height: 140px">
                            {{ old('program_description', $program->program_description) }}
                        </textarea>
                            @error('program_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Payment -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Payment</div>
                            <div class="row">
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="FullPaymentOnly" type="radio"
                                            value="full" name="payment"
                                            {{ old('payment', $program->payment) === 'full' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="FullPaymentOnly">Full
                                            Payment only</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="recurringPayment" type="radio"
                                            value="recurring" name="payment"
                                            {{ old('payment', $program->payment) === 'recurring' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="RecurringPayment">Recurring
                                            Payment</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="SplitPayment" type="radio" value="split"
                                            name="payment"
                                            {{ old('payment', $program->payment) === 'split' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="SplitPayment">Split
                                            Payment</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="mb-4" id="minimumMonthlyPaymentDiv"
                            style="{{ old('payment', $program->payment) === 'recurring' ? 'display:block;' : 'display:none;' }}">
                            <label class="text-uppercase form-label" for="down_payment">
                                Down Payment
                            </label>
                            <input class="form-control @error('down_payment') is-invalid @enderror"
                                id="down_payment" name="down_payment" type="number" step="0.01"
                                min="0" placeholder="Enter amount in dollars, Minimum $1"
                                value="{{ old('down_payment', $program->down_payment) }}" />
                            @error('down_payment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <div class="mt-3">
                                <label class="text-uppercase form-label" for="payment_months">
                                    Number of Months
                                </label>
                                <select class="form-control @error('payment_months') is-invalid @enderror" id="payment_months" name="payment_months">
                                    <option value="">Select number of months</option>
                                    @for ($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ old('payment_months', $program->up_to_months) == $i ? 'selected' : '' }}>{{ $i }} {{ $i == 1 ? 'Month' : 'Months' }}</option>
                                    @endfor
                                </select>
                                @error('payment_months')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>                        <div class="mb-4" id="status">
                            <label class="form-label text-uppercase" for="status">Status</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('status') is-invalid @enderror" id="status"
                                    name="status">
                                    <option value="" {{ old('status', $program->status) == '' ? 'selected' : '' }}>
                                        Select
                                    </option>
                                    <option value="public"
                                        {{ old('status', $program->status) == 'public' ? 'selected' : '' }}>Public
                                    </option>
                                    <option value="private"
                                        {{ old('status', $program->status) == 'private' ? 'selected' : '' }}>Private
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <div class="d-flex justify-content-between align-items-center mt-5">
                            <div class="col-md-6 d-flex justify-content-end mt-5 mr-1">
                                <button class="cta py-0 mr-1" style="margin-right:1rem">Update Program <span
                                        class="d-none ms-3 cta-response"></span></button>
                            </div>
                    </form>
                    <div class="col-md-6 d-flex mt-5">
                        <form action="{{ route('admin.program.destroy', ['program' => $program->slug]) }}"
                            method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="cta py-0">Delete Program</button>
                        </form>

                    </div>
                </div>
            </div>
        </div>
        </div>
        <style>
            .coupon-entry {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                background-color: #f9f9f9;
            }
            .coupon-entry:hover {
                border-color: #007bff;
                background-color: #f0f8ff;
            }
            #add-coupon-btn {
                background-color: #0b4499 !important;
                border-color: #0b4499 !important;
                color: white !important;
                border-radius: 6px;
                font-weight: 600;
            }
            #add-coupon-btn:hover {
                background-color: #062e69 !important;
                border-color: #062e69 !important;
                color: white !important;
            }
            .remove-coupon {
                font-size: 11px;
                padding: 6px 4px;
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
                color: white !important;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .remove-coupon:hover {
                background-color: #c82333 !important;
                border-color: #bd2130 !important;
            }
            .is-invalid {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
            }
            .validation-error {
                color: #dc3545;
                font-size: 0.875em;
                margin-top: 0.25rem;
            }
            #frontendErrorMessage {
                position: fixed;
                top: -100px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                padding: 15px 25px;
                color: #ffffff;
                font-size: 18px;
                border-radius: 8px;
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
                opacity: 0;
                transition: opacity 0.3s ease;
                background-color: #e74c3c;
                max-width: 80%;
                text-align: center;
            }
            #frontendErrorMessage.slide-in {
                top: 20px;
                opacity: 1;
                animation: slide-in 0.5s ease-out;
            }
            #frontendErrorMessage.slide-out {
                animation: slide-out 0.5s ease-in forwards;
            }
        </style>
        <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Coupon Management
                let couponCount = {{ $program->coupons ? $program->coupons->count() : 0 }};
                const addCouponBtn = document.getElementById('add-coupon-btn');
                const couponsContainer = document.getElementById('coupons-container');
                
                function createCouponEntry(index) {
                    return `
                        <div class="coupon-entry mb-4" data-index="${index}">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label text-uppercase">Coupon Code</label>
                                    <input type="text" name="coupons[${index}][code]" class="form-control coupon-code" placeholder="e.g., SUMMER2025" required>
                                    <div class="invalid-feedback">Please enter a unique coupon code.</div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Discount Type</label>
                                    <select name="coupons[${index}][discount_type]" class="form-select" required>
                                        <option value="percentage">Percentage Off</option>
                                        <option value="fixed">Fixed Amount Off</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Value</label>
                                    <input type="number" step="0.01" min="0.01" name="coupons[${index}][discount_value]" class="form-control" placeholder="20" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Usage Limit</label>
                                    <input type="number" min="1" name="coupons[${index}][usage_limit]" class="form-control" placeholder="Optional">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-uppercase">Valid Until</label>
                                    <input type="date" name="coupons[${index}][valid_until]" class="form-control">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label text-uppercase">&nbsp;</label>
                                    <button type="button" class="btn btn-danger remove-coupon form-control">Remove</button>
                                </div>
                                <div class="col-md-11">
                                    <label class="form-label text-uppercase">Description</label>
                                    <input type="text" name="coupons[${index}][description]" class="form-control" placeholder="Optional coupon description">
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                addCouponBtn.addEventListener('click', function() {
                    couponsContainer.style.display = 'block';
                    couponsContainer.insertAdjacentHTML('beforeend', createCouponEntry(couponCount));
                    couponCount++;
                    validateCouponCodes();
                });

                // Handle remove coupon buttons
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-coupon')) {
                        const couponEntry = e.target.closest('.coupon-entry');
                        couponEntry.remove();
                        if (document.querySelectorAll('.coupon-entry').length === 0) {
                            couponsContainer.style.display = 'none';
                        }
                        validateCouponCodes();
                    }
                });
                
                // Validate coupon codes for duplicates
                function validateCouponCodes() {
                    const codeInputs = document.querySelectorAll('.coupon-code');
                    const codes = [];
                    let hasDuplicates = false;
                    
                    codeInputs.forEach(input => {
                        const code = input.value.trim().toLowerCase();
                        input.classList.remove('is-invalid');
                        
                        if (code && codes.includes(code)) {
                            input.classList.add('is-invalid');
                            hasDuplicates = true;
                        } else if (code) {
                            codes.push(code);
                        }
                    });
                    
                    return !hasDuplicates;
                }
                
                // Add real-time validation
                document.addEventListener('input', function(e) {
                    if (e.target.classList.contains('coupon-code')) {
                        validateCouponCodes();
                    }
                });
                
                // Initial validation for existing coupons
                if (document.querySelectorAll('.coupon-entry').length > 0) {
                    validateCouponCodes();
                }
                
                // Comprehensive form validation
                function validateForm() {
                    let isValid = true;
                    let errors = [];
                    
                    // Required fields validation
                    const requiredFields = [
                        { id: 'programName', name: 'Program Name' },
                        { id: 'type', name: 'Program Type' },
                        { id: 'subProgramName', name: 'Sub Program Name' },
                        { id: 'location', name: 'Location' },
                        { id: 'sports', name: 'Sport' },
                        { id: 'gender', name: 'Gender' },
                        { id: 'birth_date_cutoff', name: 'Birth Date Cutoff' },
                        { id: 'registration_opening_date', name: 'Registration Opening Date' },
                        { id: 'registration_closing_date', name: 'Registration Closing Date' },
                        { id: 'start_date', name: 'Start Date' },
                        { id: 'end_date', name: 'End Date' },
                        { id: 'start_time', name: 'Start Time' },
                        { id: 'end_time', name: 'End Time' },
                        { id: 'cost', name: 'Cost' },
                        { id: 'program_description', name: 'Program Description' },
                        { id: 'status', name: 'Status' }
                    ];
                    
                    requiredFields.forEach(field => {
                        const element = document.getElementById(field.id);
                        if (!element || !element.value.trim()) {
                            errors.push(`${field.name} is required`);
                            if (element) element.classList.add('is-invalid');
                            isValid = false;
                        } else {
                            if (element) element.classList.remove('is-invalid');
                        }
                    });
                    
                    // Frequency days validation
                    const frequencyDays = document.querySelectorAll('input[name="frequency_days[]"]:checked');
                    if (frequencyDays.length === 0) {
                        errors.push('At least one frequency day must be selected');
                        isValid = false;
                    }
                    
                    // Frequency validation
                    const frequency = document.querySelector('input[name="frequency"]:checked');
                    if (!frequency) {
                        errors.push('Frequency selection is required');
                        isValid = false;
                    }
                    
                    // Payment type validation
                    const payment = document.querySelector('input[name="payment"]:checked');
                    if (!payment) {
                        errors.push('Payment type selection is required');
                        isValid = false;
                    }
                    
                    // Date validation
                    const regOpenDate = document.getElementById('registration_opening_date').value;
                    const regCloseDate = document.getElementById('registration_closing_date').value;
                    const startDate = document.getElementById('start_date').value;
                    const endDate = document.getElementById('end_date').value;
                    
                    if (regOpenDate && regCloseDate && regOpenDate > regCloseDate) {
                        errors.push('Registration closing date must be after opening date');
                        isValid = false;
                    }
                    
                    if (regCloseDate && startDate && regCloseDate > startDate) {
                        errors.push('Start date must be after registration closing date');
                        isValid = false;
                    }
                    
                    if (startDate && endDate && startDate > endDate) {
                        errors.push('End date must be after start date');
                        isValid = false;
                    }
                    
                    // Age restriction validation
                    const ageFrom = document.getElementById('age_restriction_from').value;
                    const ageTo = document.getElementById('age_restriction_to').value;
                    if (ageFrom && ageTo && parseInt(ageFrom) > parseInt(ageTo)) {
                        errors.push('Age restriction end must be greater than or equal to start');
                        isValid = false;
                    }
                    
                    // Cost validation
                    const cost = document.getElementById('cost').value;
                    if (cost && (isNaN(cost) || parseFloat(cost) < 0)) {
                        errors.push('Cost must be a valid positive number');
                        isValid = false;
                    }
                    
                    // Recurring payment validation
                    if (payment && payment.value === 'recurring') {
                        const downPayment = document.getElementById('down_payment').value;
                        const paymentMonths = document.getElementById('payment_months').value;
                        
                        if (!downPayment || parseFloat(downPayment) < 1) {
                            errors.push('Down payment must be at least $1 for recurring payments');
                            isValid = false;
                        }
                        
                        if (cost && downPayment && parseFloat(downPayment) > parseFloat(cost)) {
                            errors.push('Down payment cannot exceed program cost');
                            isValid = false;
                        }
                        
                        if (!paymentMonths) {
                            errors.push('Number of months is required for recurring payments');
                            isValid = false;
                        }
                    }
                    
                    // Early bird validation
                    const earlyBirdEnabled = document.getElementById('enable_early_bird_specials').checked;
                    if (earlyBirdEnabled) {
                        const earlyBirdFrom = document.getElementById('early_bird_from_1')?.value;
                        const earlyBirdTo = document.getElementById('early_bird_to_1')?.value;
                        const priceBefore = document.getElementById('price_before_1')?.value;
                        const priceAfter = document.getElementById('price_on_or_after_1')?.value;
                        
                        if (!earlyBirdFrom || !earlyBirdTo || !priceBefore || !priceAfter) {
                            errors.push('All early bird pricing fields are required when enabled');
                            isValid = false;
                        }
                    }
                    
                    // Coupon validation
                    if (!validateCouponCodes()) {
                        errors.push('Duplicate coupon codes are not allowed');
                        isValid = false;
                    }
                    
                    // Coupon fields validation
                    const couponEntries = document.querySelectorAll('.coupon-entry');
                    couponEntries.forEach((entry, index) => {
                        const code = entry.querySelector('input[name*="[code]"]').value;
                        const discountValue = entry.querySelector('input[name*="[discount_value]"]').value;
                        
                        if (code && (!discountValue || parseFloat(discountValue) < 0.01)) {
                            errors.push(`Coupon ${index + 1}: Discount value must be at least 0.01`);
                            isValid = false;
                        }
                        
                        const usageLimit = entry.querySelector('input[name*="[usage_limit]"]').value;
                        if (usageLimit && parseInt(usageLimit) < 1) {
                            errors.push(`Coupon ${index + 1}: Usage limit must be at least 1`);
                            isValid = false;
                        }
                    });
                    
                    if (!isValid) {
                        showFrontendError(errors.join('<br>'));
                    }
                    
                    return isValid;
                }
                
                // Show frontend error message
                function showFrontendError(message) {
                    const errorDiv = document.getElementById('frontendErrorMessage');
                    const errorText = document.getElementById('frontendErrorText');
                    errorText.innerHTML = message;
                    errorDiv.style.display = 'block';
                    errorDiv.classList.add('slide-in');
                    
                    // Auto hide after 8 seconds
                    setTimeout(() => {
                        errorDiv.classList.remove('slide-in');
                        errorDiv.classList.add('slide-out');
                        setTimeout(() => {
                            errorDiv.style.display = 'none';
                            errorDiv.classList.remove('slide-out');
                        }, 500);
                    }, 8000);
                }
                
                // Form submission validation
                document.querySelector('form').addEventListener('submit', function(e) {
                    e.preventDefault();
                    if (validateForm()) {
                        this.submit();
                    }
                    return false;
                });
                
                // Real-time validation for key fields
                document.addEventListener('input', function(e) {
                    if (e.target.classList.contains('form-control')) {
                        e.target.classList.remove('is-invalid');
                    }
                });

                // Early Bird Management
                const earlyBirdCheckbox = document.getElementById('enable_early_bird_specials');
                const earlyBirdPricingSection = document.getElementById('early_bird_pricing_section');

                function toggleEarlyBirdPricing() {
                    if (earlyBirdCheckbox.checked) {
                        earlyBirdPricingSection.style.display = '';
                    } else {
                        earlyBirdPricingSection.style.display = 'none';
                    }
                }


                toggleEarlyBirdPricing();


                earlyBirdCheckbox.addEventListener('change', toggleEarlyBirdPricing);


                const recurringPaymentRadio = document.getElementById('recurringPayment');
                const oneTimePaymentRadio = document.getElementById('FullPaymentOnly');
                const splitPaymentRadio = document.getElementById('SplitPayment');
                const minimumMonthlyPaymentDiv = document.getElementById('minimumMonthlyPaymentDiv');

                recurringPaymentRadio.addEventListener('change', togglePaymentDiv);
                oneTimePaymentRadio.addEventListener('change', togglePaymentDiv);
                splitPaymentRadio.addEventListener('change', togglePaymentDiv);

                function togglePaymentDiv() {
                    if (recurringPaymentRadio.checked) {
                        minimumMonthlyPaymentDiv.style.display = 'block';
                    } else {
                        minimumMonthlyPaymentDiv.style.display = 'none';
                    }
                }

                showSessionSuccessMessage();
                showSessionErrorMessage();
            });
        </script>

    </section>
    <script>
        tinymce.init({
            selector: 'textarea#program_description',
            width: 1000,
            height: 300,
            plugins: [
                'advlist', 'autolink', 'link', 'image', 'charmap', 'preview', 'anchor',
                'searchreplace', 'wordcount', 'code', 'fullscreen', 'insertdatetime',
                'table', 'print'
            ],
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen',
            menubar: false,
            content_style: 'body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }'
        });
    </script>


@endsection
