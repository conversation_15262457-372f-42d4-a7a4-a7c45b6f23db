<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CoachController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ForgotPasswordController;
use App\Http\Controllers\GuardianController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\ProgramController;
use App\Mail\TempMail;
use App\Models\Program;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Password;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Support\Facades\Hash;



Route::middleware('CheckReferrer')->group(function () {



    Route::get('/', function () {

        $user = auth()->user();

        if ($user) {

            $roles = $user->roles->pluck('name');
            if ($roles->contains('admin')) {
                return redirect()->route('admin.dashboard');
                // there is no player dashboard so ignore it. if you don't like it you can remove it
            } elseif ($roles->contains('player')) {
                abort(404, "Unauthorized access. Players are not allowed to log in directly.");
            } elseif ($roles->contains('coach') && $user->current_role == 'coach') {
                return redirect()->route('coach.dashboard');
            } elseif ($roles->contains('coach') && $user->current_role == 'guardian') {
                return redirect()->route('guardian.dashboard');
            } elseif ($roles->contains('guardian') && $user->current_role == 'guardian') {
                return redirect()->route('guardian.dashboard');
            } elseif ($roles->contains('guardian') && $user->current_role == 'coach') {
                return redirect()->route('coach.dashboard');
            } elseif ($roles->contains('guardian')) {
                return redirect()->route('guardian.dashboard');
            }
        }

        return view('auth.login');
    })->name('redirectLogin');

    //program routes-->

    Route::get('/programs', [ProgramController::class, 'index'])->name('programs');
    Route::get('/signup/program/{program}', [ProgramController::class, 'show'])->name('program.show');
    Route::post('/signup/program/{program}', [ProgramController::class, 'store'])->name('program.add');
    Route::get('/select-player/{id}', [ProgramController::class, 'selectPlayerOrSelectTeam'])->name('selectPlayer');
    Route::post('/register-team/{programSlug}/{selectedTeamId}', [ProgramController::class, 'registerTeam'])->name('registerTeam');



    //authentication routes
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/forgot-password', [AuthController::class, 'forgotPasswordPage'])->name('forgotPasswordPage');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('forgotPassword');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
    Route::get('/reset-password/{token}', function (string $token) {
        $email = session('email');
        return view('auth.reset-password', ['token' => $token, 'email' => $email]);
    })->name('password.reset');

    //sign up and login page
    Route::middleware('IsloggedIn')->group(function () {
        Route::view('/login', 'auth.login')->name('loginPage');
        Route::get('/coach/signup', [CoachController::class, 'coachSignup'])->name('coach.signup');
        Route::post('/coach/store', [CoachController::class, 'coachStore'])->name('coach.store');
        Route::get('/coach/invited/signup/{user}/{email}/{token}', [CoachController::class, 'invitedCoachSignup'])->name('invitedCoachSignup');
        Route::prefix('guardian/')->name('guardian.')->group(function () {
            // Guardian search for modal

            Route::match(['get', 'post'], 'signup/{token?}', [GuardianController::class, 'signup'])->name('signup');
            Route::get('/guardian/signup/invited/{coachInviteToken?}', [GuardianController::class, 'guardianSignupInvitedByCoach'])->name('guardianSignupInvitedByCoach');
        });
    });


    //Admin Routes For Ajax Calls
    Route::middleware(['auth'])->group(function () {
        Route::prefix('admin/api/')->name('admin.api.')->middleware(['role.check:admin', 'checkSession'])->group(function () {
            Route::get('guardians', [AdminController::class, 'getGuardians'])->name('getGuardians');
            Route::get('player/guardian', [AdminController::class, 'guardianOfPlayer'])->name('guardianOfPlayer');
            Route::post('store-player', [AdminController::class, 'store_player'])->name('store_player');
            Route::get('player/{id}', [AdminController::class, 'show_player'])->name('show_player');
            Route::post('edit-player/{id}', [AdminController::class, "edit_player"])->name('edit_player');

            Route::post('store-admin', [AdminController::class, 'store_admin'])->name('store_admin');
            Route::get('admin/{id}', [AdminController::class, 'show_admin'])->name('show_admin');
            Route::post('edit-admin/{id}', [AdminController::class, "edit_admin"])->name('edit_admin');

            Route::post('store-coach', [AdminController::class, 'store_coach'])->name('store_coach');
            Route::get('coach/{id}', [AdminController::class, 'show_coach'])->name('show_coach');
            Route::post('edit-coach/{id}', [AdminController::class, "edit_coach"])->name('edit_coach');

            // Team creation routes
            Route::get('getCoaches', [AdminController::class, 'getCoaches'])->name('getCoaches');
            Route::get('getTeamPrograms', [AdminController::class, 'getTeamPrograms'])->name('getTeamPrograms');
            Route::post('createTeam', [AdminController::class, 'createTeam'])->name('createTeam');

            Route::post('store-guardian', [AdminController::class, 'store_guardian'])->name('store_guardian');
            Route::get('guardian/{id}', [AdminController::class, 'show_guardian'])->name('show_guardian');
            Route::post('edit-guardian/{id}', [AdminController::class, "edit_guardian"])->name('edit_guardian');

            // Player merging routes
            Route::get('players-for-merge', [AdminController::class, 'getPlayersForMerge'])->name('getPlayersForMerge');
            Route::get('player-details/{id}', [AdminController::class, 'getPlayerDetails'])->name('getPlayerDetails');
            Route::post('merge-players', [AdminController::class, 'mergePlayers'])->name('mergePlayers');
            Route::post('merge-guardians', [AdminController::class, 'mergeGuardians'])->name('mergeGuardians');

            // Player guardian management routes
            Route::get('player-guardians/{id}', [AdminController::class, 'getPlayerGuardians'])->name('getPlayerGuardians');
            Route::post('add-guardian-to-player', [AdminController::class, 'addGuardianToPlayer'])->name('addGuardianToPlayer');
            Route::post('remove-guardian-from-player', [AdminController::class, 'removeGuardianFromPlayer'])->name('removeGuardianFromPlayer');

            // Tryout program invitation routes
            Route::get('available-players-for-tryout/{programId}', [AdminController::class, 'getAvailablePlayersForTryoutProgram'])->name('getAvailablePlayersForTryout');
            Route::post('send-tryout-program-invitation', [AdminController::class, 'sendTryoutProgramInvitation'])->name('sendTryoutProgramInvitation');

            // Team invitation routes
            Route::get('available-teams-for-program/{programId}', [AdminController::class, 'getAvailableTeamsForProgram'])->name('getAvailableTeamsForProgram');
            Route::post('invite-player-to-team', [AdminController::class, 'invitePlayerToTeam'])->name('invitePlayerToTeam');
        });
    });


    //Admin Routes
    Route::middleware(['auth'])->group(function () {
        Route::prefix('admin/')->name('admin.')->middleware(['role.check:admin', 'checkSession'])->group(function () {
            Route::get('', [AdminController::class, 'index'])->name('dashboard');
            Route::get('users', [AdminController::class, 'allUsers'])->name('users');
            Route::delete('delete/{user}', [AdminController::class, 'destroy_user'])->name('destroy');
            Route::get('/payments/registrations', [AdminController::class, 'paymentsAndRegistrations'])->name('payments-and-registrations');
            Route::get('/program-registrations-payments', [AdminController::class, 'programRegistrationsAndPayments'])->name('program.registrations.payments');
            Route::get('/program-payment-details/{programId}', [AdminController::class, 'programPaymentDetails'])->name('program.payment.details');
            Route::get('/export-program-payment-data/{programId}', [AdminController::class, 'exportProgramPaymentData'])->name('export.program.payment.data');
            Route::get('/reports', [AdminController::class, 'adminReports'])->name('reports');
            Route::get('/edit/teams/{program}/{team}/{coach}', [AdminController::class, 'editTeams'])->name('editTeams');
            Route::delete('/delete/team/from/program/{program}/{team}/{coach}', [AdminController::class, 'removeTeamFromProgram'])->name('removeTeamFromProgram');
            Route::delete('/teams/delete', [AdminController::class, 'deletePlayerFromTeam'])->name('deletePlayerFromTeam');
            Route::delete('/player/from/program/{program}/{player}', [AdminController::class, 'removePlayerFromProgram'])->name('removePlayerFromProgram');

            Route::get('/teams/{program}', [AdminController::class, 'allTeams'])->name('allTeams');
            Route::post('/add/team/to-program', [AdminController::class, 'addTeamToProgram'])->name('addTeamToProgram');
            Route::get('/players/{program}', [AdminController::class, 'allPlayers'])->name('allPlayers');
            Route::post('/add/player/to-program', [AdminController::class, 'addPlayerToProgram'])->name('addPlayerToProgram');

            Route::prefix('programs/')->name('program.')->group(function () {
                Route::get('programs', [AdminController::class, 'allPrograms'])->name('allPrograms');
                Route::post('toggle-draft/{program}', [AdminController::class, 'toggleDraft'])->name('admin.program.toggle-draft');
                Route::get('create', [AdminController::class, 'program_create'])->name('add');
                Route::post('store', [AdminController::class, 'program_store'])->name('store');
                Route::get('edit/{slug}', [AdminController::class, 'program_edit'])->name('edit');
                Route::put('update/{slug}', [AdminController::class, 'program_update'])->name('update');
                Route::delete('delete/{program}', [AdminController::class, 'program_destroy'])->name('destroy');
                Route::get("tryout-programs", [AdminController::class, 'tryOutPrograms'])->name('tryoutPrograms');
            });


            // report Routes

            Route::get("/towns", [AdminController::class, "towns"])->name("towns");
            Route::get("/sport", [AdminController::class, 'sport'])->name('sport');
            Route::get("/players", [AdminController::class, 'players'])->name('players');
            Route::get("/genders", [AdminController::class, 'genders'])->name('genders');
            Route::get("/programs", [AdminController::class, 'programs'])->name('programs');
            Route::get('/ages', [AdminController::class, 'ages'])->name('ages');
            Route::get('/users/report', [AdminController::class, 'usersReport'])->name('usersReport');
            Route::get('/programs/report', [AdminController::class, 'programsReport'])->name('programsReport');
            Route::get('/specific/report', [AdminController::class, 'specificReport'])->name('specificReport');
            Route::get('/financial/report', [AdminController::class, 'financialReport'])->name('financialReport');
            Route::get('/programs/fullreport', [AdminController::class, 'exportProgramsToExcel'])->name('export.programs');
            Route::get('/users/report/export', [AdminController::class, 'exportUsersReport'])->name('users.report.export');
            Route::get('/admin/export-finance', [AdminController::class, 'exportFinanceToExcel'])->name('export.finance.report');
            Route::get('/view-teams', [AdminController::class, 'viewAllTeams'])->name('viewAllTeams');

            // Invoice management routes
            Route::prefix('invoices/')->name('invoices.')->group(function () {
                Route::get('/', [\App\Http\Controllers\InvoiceController::class, 'index'])->name('index');
                Route::get('/{invoice}', [\App\Http\Controllers\InvoiceController::class, 'show'])->name('show');
                Route::post('/{invoice}/refund', [\App\Http\Controllers\InvoiceController::class, 'processRefund'])->name('refund');
                Route::get('/{invoice}/download', [\App\Http\Controllers\InvoiceController::class, 'downloadInvoice'])->name('download');
                Route::post('/sync-stripe', [\App\Http\Controllers\InvoiceController::class, 'syncInvoicesFromStripe'])->name('sync');

                // Subscription management routes
                Route::post('/{invoice}/cancel-subscription', [\App\Http\Controllers\InvoiceController::class, 'cancelSubscription'])->name('cancel-subscription');
                Route::post('/{invoice}/disable-autopay', [\App\Http\Controllers\InvoiceController::class, 'disableAutoPay'])->name('disable-autopay');
                Route::post('/{invoice}/enable-autopay', [\App\Http\Controllers\InvoiceController::class, 'enableAutoPay'])->name('enable-autopay');
            });

            // Charge management routes
            Route::prefix('charges/')->name('charges.')->group(function () {
                Route::get('/', [\App\Http\Controllers\ChargeController::class, 'index'])->name('index');
                Route::get('/{charge}', [\App\Http\Controllers\ChargeController::class, 'show'])->name('show');
                Route::post('/{charge}/refund', [\App\Http\Controllers\ChargeController::class, 'processRefund'])->name('refund');
                Route::get('/{charge}/receipt', [\App\Http\Controllers\ChargeController::class, 'downloadReceipt'])->name('receipt');
            });

            // Guardian credit management routes
            Route::prefix('credits/')->name('credits.')->group(function () {
                Route::post('/add', [AdminController::class, 'addCreditToGuardian'])->name('add');
                Route::get('/guardian/{guardianId}', [AdminController::class, 'getGuardianCredits'])->name('getGuardianCredits');
                Route::get('/guardians', [AdminController::class, 'getGuardiansWithCredits'])->name('getGuardiansWithCredits');
            });

            // Payment transaction details
            Route::get('/payment-transaction/{id}', [AdminController::class, 'getPaymentTransactionDetails'])->name('payment.transaction.details');
        });



        //guardian routes

        Route::prefix('guardian/')->name('guardian.')->middleware(['role.check:guardian', 'checkSession'])->group(function () {
            Route::get('', [GuardianController::class, 'index'])->name('dashboard');
            Route::get('/guardians/load-more', [GuardianController::class, 'loadMoreGuardians'])->name('guardians.loadMore');
            Route::post('/players/load-more', [GuardianController::class, 'loadMorePlayers'])->name('players.loadMore');
            Route::post('/store-player', [GuardianController::class, 'player_add'])->name('player.add');
            Route::post('/store-player/add-to-program/{id}', [GuardianController::class, 'player_addToProgram'])->name('player.addToProgram');
            Route::get('/players/{id}/edit', [GuardianController::class, 'edit'])->name('players.edit');
            Route::get('/guardian/{id}/edit', [GuardianController::class, 'guardian_edit'])->name('guardian.edit');
            Route::post('/guardian/update', [GuardianController::class, 'guardian_update'])->name('guardian.update');
            Route::post('/players/update', [GuardianController::class, 'update'])->name('players.update');

            Route::post('/invite/additional-guardian', [GuardianController::class, 'sendInvitationToAdditionalGuardian'])->name('sendInvitationToAdditionalGuardian');

            Route::post('add/additional-guardian', [GuardianController::class, 'addAdditionalGuardian'])->name('addAdditionalGuardian');
            Route::get('search', [GuardianController::class, 'search'])->name('search');
            // Route::get('/payment', [GuardianController::class,'show_payment_option'])->name('payment.show');
            Route::get('/player/profile/{playerId}', [GuardianController::class, 'showPlayerProfile'])->name('player.profileShow');
            Route::post('/register/player', [GuardianController::class, 'registerPlayer'])->name('player.registerForProgram');
            Route::post('/register/player/for-program', [GuardianController::class, 'registerPlayerForProgram'])->name('registerPlayerForProgram');
            Route::post('/switch/role/{guardian}', [GuardianController::class, 'switchRoleToCoach'])->name('switchRoleToCoach');
            Route::post('/accept/invitation/player', [GuardianController::class, 'acceptInvitation'])->name('acceptInvitation');
            Route::post('/reject/invitation/player', [GuardianController::class, 'rejectInvitation'])->name('rejectInvitation');
            Route::get('/guardians/payment/options', [GuardianController::class, 'guardiansForPayment'])->name('guardiansForPayment');
            Route::get('player/payment', [GuardianController::class, 'showPaymentPage'])->name('payment');
            Route::get('/payment/options', [GuardianController::class, 'showPaymentOptions'])->name('showPaymentOptions');
            Route::get('/payment/type/', [GuardianController::class, 'programPayment'])->name('programPayment');
            Route::get('/payment/type/accept/invite', [GuardianController::class, 'programPaymentforAcceptInvite'])->name('programPaymentforAcceptInvite');
            Route::post('/payment/method', [GuardianController::class, 'paymentMethod'])->name('paymentMethod');
            Route::get('/check-credit', [GuardianController::class, 'checkCredit'])->name('checkCredit');
            Route::post('/apply-coupon', [GuardianController::class, 'applyCoupon'])->name('applyCoupon');
            Route::post('/apply-coupon', [GuardianController::class, 'applyCoupon'])->name('applyCoupon');
            Route::get('/card-details', [GuardianController::class, 'cardDetails'])->name('cardDetails');

            // Checkout routes
            Route::post('/prepare-checkout', [\App\Http\Controllers\CheckoutController::class, 'prepareCheckout'])->name('prepare-checkout');
            Route::get('/checkout', [\App\Http\Controllers\CheckoutController::class, 'showCheckout'])->name('checkout');
            Route::post('/create-payment-intent', [\App\Http\Controllers\CheckoutController::class, 'createPaymentIntent'])->name('create-payment-intent');
            Route::post('/process-credit-payment', [\App\Http\Controllers\CheckoutController::class, 'processCreditPayment'])->name('process-credit-payment');
            Route::get('/payment-success', [\App\Http\Controllers\CheckoutController::class, 'paymentSuccess'])->name('payment-success');
        });

        //coach routes

        Route::prefix('coach/')->name('coach.')->middleware(['role.check:coach', 'checkSession'])->group(function () {
            Route::get('/dashboard/{team?}', [CoachController::class, 'index'])->name('dashboard');
            Route::get('/coach/{id}/edit', [CoachController::class, 'coach_edit'])->name('coach.edit');
            Route::post('/coach/update', [CoachController::class, 'coach_update'])->name('coach.update');
            Route::get('/coach/team-update/{id}', [CoachController::class, 'coach_team_edit'])->name('team.edit');
            Route::post('/coach/team/update', [CoachController::class, 'coach_team_update'])->name('team.update');


            Route::get('/search-player', [CoachController::class, 'searchPlayer'])->name('searchPlayer');
            Route::get('/search/coach', [CoachController::class, 'searchCoach'])->name('searchAnotherCoach');
            Route::post('invite-player/{player}', [CoachController::class, 'invitePlayer'])->name('invite.player');
            Route::post('/invite-coach/{coachId}', [CoachController::class, 'inviteCoach'])->name('invite.coach');
            Route::post('/create/new-team', [CoachController::class, 'createNewTeam'])->name('createNewTeam');
            Route::get('/teams', [CoachController::class, 'coachTeams'])->name('teams');
            Route::post('/manage-team', [CoachController::class, 'manageTeam'])->name('manageTeam');
            Route::get('/manage-teams', [CoachController::class, 'manageTeams'])->name('manageTeams');
            Route::post('/invite-mail/guardian', [CoachController::class, 'sendInviteMailToGuardian'])->name('sendInviteMailToGuardian');
            Route::post('/switch/role/{coach}', [CoachController::class, 'switchRoleToGuardian'])->name('switchRoleToGuardian');
            Route::get('/check-coach-invitation-status/{coachId}/{teamId}', [CoachController::class, 'checkCoachInvitationStatus'])->name('checkCoachInvitationStatus');
        });
    });
});
Route::get('/coach/accept-invite/{coach}', [CoachController::class, 'coachAcceptedInvite'])->name('coach.acceptInvitation');



//payment route

Route::middleware(['auth', 'checkSession'])->group(function () {
    Route::post('/set-payment-amount', [PaymentController::class, 'setPaymentAmount']);
    Route::get('/payment', [PaymentController::class, 'index'])->name('payment.index');
    Route::post('/payment', [PaymentController::class, 'fullPayment'])->name('payment.process');
    Route::post('/paymentoptions/payment', [PaymentController::class, 'processPaymentForPaymentOptions'])->name('payment.processForPaymentOptions');
    // Route::post('/setup-recurring-payment', [PaymentController::class, 'setupSubscription'])->name('setup-recurring-payment');
    Route::post('/setup-recurring-payment', [PaymentController::class, 'setupRecurringPayments'])->name('setup-recurring-payment');
    Route::post('/paymentoptions/setup-recurring-payment', [PaymentController::class, 'setupSubscriptionForPaymentOptions'])->name('paymentOptions.setup-recurring-payment');
    // Route::post('/recurring/payment/status', [PaymentController::class, 'recurringPaymentStatus'])->name('recurringPayments.status');
    Route::post('/recurring/payment/status', [PaymentController::class, 'recurringPaymentDetails'])->name('recurringPayments.status');


    Route::post('/split/payments', [PaymentController::class, 'specificAmountPayment'])->name('specificAmountPayment');

    Route::post('/paymentoptions/recurring/payment/status', [PaymentController::class, 'recurringPaymentStatusForPaymentOptions'])->name('recurringPayments.statusForPaymentOptions');
    Route::post('/payment/update', [PaymentController::class, 'updatePaymentStatus'])->name('payment.update');
    Route::post('paymentoptions/payment/update', [PaymentController::class, 'updatePaymentStatusForPaymentOptions'])->name('payment.updateForPaymentOptions');
    Route::post('guardian/updateInvitationAndPayment', [PaymentController::class, 'acceptInvitePayment'])->name('acceptInvitePayment');
    Route::post('guardian/credit-payment', [PaymentController::class, 'processCreditPayment'])->name('credit.payment');
    Route::get('/payment/success', [PaymentController::class, 'success'])->name('payment.success');
    Route::get('/payment/error', [PaymentController::class, 'error'])->name('payment.error');

    // Individual/AAU Program Payment Routes
    Route::prefix('individual-program-payment')->name('individual.program.payment.')->group(function () {
        Route::post('/process', [\App\Http\Controllers\IndividualProgramPaymentController::class, 'processPayment'])->name('process');
        Route::get('/options', [\App\Http\Controllers\IndividualProgramPaymentController::class, 'showPaymentOptions'])->name('options');
        Route::post('/process-selection', [\App\Http\Controllers\IndividualProgramPaymentController::class, 'processPaymentSelection'])->name('process.selection');
        Route::get('/card-details', [\App\Http\Controllers\IndividualProgramPaymentController::class, 'showCardDetails'])->name('card.details');
        Route::post('/process-card-payment', [\App\Http\Controllers\IndividualProgramPaymentController::class, 'processCardPayment'])->name('process.card');
    });
});

// External Payment Routes (no auth required)
Route::prefix('external-payment')->name('external.payment.')->group(function () {
    Route::get('/{token}', [\App\Http\Controllers\ExternalPaymentController::class, 'showPaymentPage'])->name('show');
    Route::post('/{token}/process', [\App\Http\Controllers\ExternalPaymentController::class, 'processPayment'])->name('process');
    Route::get('/{token}/success', [\App\Http\Controllers\ExternalPaymentController::class, 'showSuccessPage'])->name('success');
    Route::get('/{token}/status', [\App\Http\Controllers\ExternalPaymentController::class, 'checkStatus'])->name('status');
});

// Admin routes for external payment management
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::post('/external-payment-links/{linkId}/resend', [\App\Http\Controllers\ExternalPaymentController::class, 'resendLink'])->name('external.payment.resend');
});
