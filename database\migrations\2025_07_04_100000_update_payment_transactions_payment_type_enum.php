<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to alter the enum to include 'external'
        DB::statement("ALTER TABLE payment_transactions MODIFY COLUMN payment_type ENUM('full', 'split', 'recurring', 'external') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'external' from the enum (only if no records use it)
        DB::statement("ALTER TABLE payment_transactions MODIFY COLUMN payment_type ENUM('full', 'split', 'recurring') NOT NULL");
    }
};
